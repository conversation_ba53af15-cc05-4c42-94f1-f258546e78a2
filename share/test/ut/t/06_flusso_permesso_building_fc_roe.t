#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 21;
use JSON;

use WPSOAP::Collection::Activity::PERMESSO_BUILDING;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "TIM";
my $contractId = "FTTH";
my $workingGroupCode = 106656;

$class = 'API::ART';

$class = 'WPSOAP::Collection::Activity::PERMESSO_BUILDING';
my $collPermessoBuilding = eval { 
	WPSOAP::Collection::Activity::PERMESSO_BUILDING->new(
		ART	=> $art 
	)
};
ok	defined($collPermessoBuilding),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collPermessoBuilding, $class;

my $permessoBuilding = $collPermessoBuilding->crea(
	"privatePermitDescription" => "Primo permesso per integrazione area permessi"
	, "privatePermitType" => 'Scavo civile'
	, "privatePermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "assetId" => [1,2]
	, "workingGroupCode" => $workingGroupCode
	, "oneForAll" => 1
	, "targetAsset" => 'ROE'
);
ok defined($permessoBuilding),
	$msg->( qq{crea()});

$class=ref($permessoBuilding);
my $action = 'RICHIESTA_PERMESSO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
	}
	),
	$msg->(qq{step($action)});

$action = 'SOSPENSIONE';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'PERMESSO_OTTENUTO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		authorizationDate => "2013-07-14T01:00:00+01:00",
	}
	),
	$msg->(qq{step($action)});

ok $permessoBuilding->get_current_status_name() eq 'CHIUSA',
	$msg->("check_stato_finale (".$permessoBuilding->get_current_status_name().")");

$class=ref($collPermessoBuilding);
$permessoBuilding = $collPermessoBuilding->crea(
	"privatePermitDescription" => "Primo permesso per integrazione area permessi 2"
	, "privatePermitType" => 'Scavo civile'
	, "privatePermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "assetId" => [1,2]
	, "workingGroupCode" => $workingGroupCode
	, "oneForAll" => 1
	, "targetAsset" => 'ROE'
);
ok defined($permessoBuilding),
	$msg->( qq{crea()});

$class=ref($permessoBuilding);
$action = 'SOSPENSIONE';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_PERMESSO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',	
	}
	),
	$msg->(qq{step($action)});

$action = 'PERMESSO_RIFIUTATO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

ok $permessoBuilding->get_current_status_name() eq 'RIFIUTATA',
	$msg->("check_stato_finale (".$permessoBuilding->get_current_status_name().")");

$permessoBuilding = $collPermessoBuilding->crea(
	"privatePermitDescription" => "Primo permesso per integrazione area permessi 4"
	, "privatePermitType" => 'Scavo civile'
	, "privatePermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "assetId" => [1,2]
	, "workingGroupCode" => $workingGroupCode
	, "oneForAll" => 1
	, "targetAsset" => 'ROE'
);
ok defined($permessoBuilding),
	$msg->( qq{creaPermessoLavori()});

$action = 'ANNULLAMENTO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

ok $permessoBuilding->get_current_status_name() eq 'ANNULLATA',
	$msg->("check_stato_finale (".$permessoBuilding->get_current_status_name().")");

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
