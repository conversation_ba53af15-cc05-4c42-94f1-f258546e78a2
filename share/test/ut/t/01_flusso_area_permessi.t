#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 40;
use JSON;

use WPSOAP::Collection::System::PROJECT;
use WPSOAP::Collection::Activity::AP_LC;
use WPSOAP::Collection::Activity::PERMESSO_LAVORI;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $projectId = "99999";
my $projectGroupName = "PROJECT_".$customerId."_".$contractId."_".$projectId;
my $projectGroupDescription = $projectGroupName;
my $project4RPBGroupName = "PROJECT4RPB_".$customerId."_".$contractId."_".$projectId;
my $project4RPBGroupDescription = $project4RPBGroupName;
my $cityGroupName = "CITY_9999";
my $cityGroupDescription = $cityGroupName;
my $cadastralCode = 'A794';
my $cityId = '9999';

$class = 'API::ART';
my $city_group = $art->create_group(
	NAME => $cityGroupName
	, DESCRIPTION => $cityGroupDescription
	, IS_AUTOGENERATED => 1
);
ok defined($city_group),
	$msg->( qq{create_group()});

$class = 'WPSOAP::Collection::System::PROJECT';
my $collProject = eval { 
	WPSOAP::Collection::System::PROJECT->new(
		ART	=> $art 
	)
};
ok	defined($collProject),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collProject, $class;

my $progetto = $collProject->crea(
	"customerId"					=> $customerId
	, "contractId"					=> $contractId
	, "projectId"					=> $projectId
	, "projectGroupName"			=> $projectGroupName
	, "projectGroupDescription"		=> $projectGroupDescription
	, "project4RPBGroupName"		=> $project4RPBGroupName
	, "project4RPBGroupDescription"	=> $project4RPBGroupDescription
	, "cityGroupName"				=> $cityGroupName
	, "cadastralCode"				=> $cadastralCode
	, "cityId"						=> $cityId
);
ok defined($progetto),
	$msg->( qq{crea()});

$class = 'WPSOAP::Collection::Activity::AP_LC';
my $collAP_LC = eval { 
	WPSOAP::Collection::Activity::AP_LC->new(
		ART	=> $art 
	)
};
ok	defined($collAP_LC),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collAP_LC, $class;

my $ap_lc = $collAP_LC->crea(
	"customerId" => $customerId
	, "contractId" => $contractId
	, "projectId" => $progetto->property('projectId')
	, "permitsAreaId" => '1'
	, "polygon" => {"coordinates" => [[[38.097943,13.369353],[38.09768,13.369102],[38.097831,13.368853]]],"type" => "Polygon"}
	, "requestDate" => '2012-07-14T01:00:00+01:00'
	, "name" => 'test AP'
);
ok defined($ap_lc),
	$msg->( qq{crea()});

$class=ref($ap_lc);
my $action = 'MODIFICA_AREA';
ok $ap_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"polygon" => {"coordinates" => [[[38.097943,13.369353],[38.09768,13.369102],[38.097831,13.368853]]],"type" => "Polygon"}
		, "requestDate" => '2012-07-14T01:00:00+01:00'
		, "name" => 'test AP'
		, "infrastructureLength" => 10
	}
	),
	$msg->(qq{step($action)});

$action = 'FORECASTING';
ok $ap_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"forecastStartDate" => '2012-07-14T01:00:00+01:00',
		"forecastEndDate" => '2012-07-15T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

$class = 'WPSOAP::Collection::Activity::PERMESSO_LAVORI';
my $collPermessoLavori = eval { 
	WPSOAP::Collection::Activity::PERMESSO_LAVORI->new(
		ART	=> $art 
	)
};
ok	defined($collPermessoLavori),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collPermessoLavori, $class;

my $permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "AP_LC" => $ap_lc
);
ok defined($permessoLavori),
	$msg->( qq{crea()});

$class=ref($permessoLavori);
$action = 'RICHIESTA_PERMESSO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

#$action = 'INOLTRO_SOLLECITO';
#ok $permessoLavori->step(
#	ACTION			=> $action
#	,DESCRIPTION	=> $action
#	,PROPERTIES		=> {}
#	),
#	$msg->(qq{step($action)});

$action = 'SOSPENSIONE';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA_POSITIVA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		authorizationDate => "2013-07-14T01:00:00+01:00",
		endAuthorizationDate => "2014-07-14T01:00:00+01:00"
	}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_DEROGA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA_POSITIVA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		authorizationDate => "2013-07-14T01:00:00+01:00",
		endAuthorizationDate => "2014-07-14T01:00:00+01:00"
	}
	),
	$msg->(qq{step($action)});

$class=ref($collPermessoLavori);
$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 2"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "AP_LC" => $ap_lc
);
ok defined($permessoLavori),
	$msg->( qq{crea()});

$class=ref($permessoLavori);
$action = 'SOSPENSIONE';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_PERMESSO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'RIFIUTO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$class=ref($collPermessoLavori);
$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 3"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "AP_LC" => $ap_lc
);
ok defined($permessoLavori),
	$msg->( qq{crea()});

$class=ref($permessoLavori);
$action = 'ANNULLAMENTO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 4"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "AP_LC" => $ap_lc
);
ok defined($permessoLavori),
	$msg->( qq{creaPermessoLavori()});

$action = 'RICHIESTA_PERMESSO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'	
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($ap_lc);
$action = 'MODIFICA_AREA';
ok $ap_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"polygon" => {"coordinates" => [[[38.097943,13.369353],[38.09768,13.369102],[38.097831,13.368853]]],"type" => "Polygon"}
		, "requestDate" => '2012-07-14T01:00:00+01:00'
		, "name" => 'test AP'
		, "infrastructureLength" => 10
	}
	),
	$msg->(qq{step($action)});

$action = 'CHIUSURA';
ok $ap_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class = 'WPSOAP::Collection::Activity::AP_LC';
$ap_lc = $collAP_LC->crea(
	"customerId" => $customerId
	, "contractId" => $contractId
	, "projectId" => $progetto->property('projectId')
	, "permitsAreaId" => '2'
	, "polygon" => {"coordinates" => [[[38.097943,13.369353],[38.09768,13.369102],[38.097831,13.368853]]],"type" => "Polygon"}
	, "requestDate" => '2012-07-14T01:00:00+01:00'
	, "name" => 'test AP'
);
ok defined($ap_lc),
	$msg->( qq{crea()});

$class = ref($ap_lc);
$action = 'FORECASTING';
ok $ap_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		"forecastStartDate" => '2012-07-14T01:00:00+01:00',
		"forecastEndDate" => '2012-07-15T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

$class = 'WPSOAP::Collection::Activity::PERMESSO_LAVORI';
$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 4"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "AP_LC" => $ap_lc
);
ok defined($permessoLavori),
	$msg->( qq{creaPermessoLavori()});

$class=ref($ap_lc);
ok $ap_lc->cancellazione(),
	$msg->(qq{cancellazione});

$class = 'WPSOAP::Collection::Activity::AP_LC';
$ap_lc = $collAP_LC->crea(
	"customerId" => $customerId
	, "contractId" => $contractId
	, "projectId" => $progetto->property('projectId')
	, "permitsAreaId" => '3'
	, "polygon" => {"coordinates" => [[[38.097943,13.369353],[38.09768,13.369102],[38.097831,13.368853]]],"type" => "Polygon"}
	, "requestDate" => '2012-07-14T01:00:00+01:00'
	, "name" => 'test AP'
);
ok defined($ap_lc),
	$msg->( qq{crea()});

$class=ref($ap_lc);
ok $ap_lc->cancellazione(),
	$msg->(qq{cancellazione});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
