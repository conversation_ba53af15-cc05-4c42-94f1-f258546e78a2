#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 28;
use JSON;

use WPSOAP::Collection::System::PROJECT;
use WPSOAP::Collection::Activity::BUILDING_LC;
use WPSOAP::Collection::Activity::PERMESSO_BUILDING;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "ENEL";
my $contractId = "FTTH";
my $projectId = "99999999";
my $projectGroupName = "PROJECT_".$customerId."_".$contractId."_".$projectId;
my $projectGroupDescription = $projectGroupName;
my $project4RPBGroupName = "PROJECT4RPB_".$customerId."_".$contractId."_".$projectId;
my $project4RPBGroupDescription = $project4RPBGroupName;
my $cityGroupName = "CITY_9999";
my $cityGroupDescription = $cityGroupName;
my $cityId = '9999';
my $cityrpbGroupName = "CITY4RPB_".$cityId;
my $cityrpbGroupDescription = $cityGroupName;
my $cadastralCode = 'A794';


$class = 'API::ART';
my $city_group = $art->create_group(
	NAME => $cityGroupName
	, DESCRIPTION => $cityGroupDescription
	, IS_AUTOGENERATED => 1
);
ok defined($city_group),
	$msg->( qq{create_group()});

my $city_group_rpb = $art->create_group(
	NAME => $cityrpbGroupName
	, DESCRIPTION => $cityrpbGroupDescription
	, IS_AUTOGENERATED => 1
);
ok defined($city_group_rpb),
	$msg->( qq{create_group()});

$class = 'WPSOAP::Collection::System::PROJECT';
my $collProject = eval { 
	WPSOAP::Collection::System::PROJECT->new(
		ART	=> $art 
	)
};
ok	defined($collProject),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collProject, $class;

my $progetto = $collProject->crea(
	"customerId"					=> $customerId
	, "contractId"					=> $contractId
	, "projectId"					=> $projectId
	, "projectGroupName"			=> $projectGroupName
	, "projectGroupDescription"		=> $projectGroupDescription
	, "project4RPBGroupName"		=> $project4RPBGroupName
	, "project4RPBGroupDescription"	=> $project4RPBGroupDescription
	, "cityGroupName"				=> $cityGroupName
	, "cadastralCode"				=> $cadastralCode
	, "cityId"						=> $cityId
);
ok defined($progetto),
	$msg->( qq{crea()});

$class = 'WPSOAP::Collection::Activity::BUILDING_LC';
my $collBuilding_LC = eval { 
	WPSOAP::Collection::Activity::BUILDING_LC->new(
		ART	=> $art 
	)
};
ok	defined($collBuilding_LC),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collBuilding_LC, $class;

my $building_lc = $collBuilding_LC->crea(
	"customerId" => $customerId
	, "contractId" => $contractId
	, "projectId" => $progetto->property('projectId')
	, "buildingSinfoId" => '1'
);
ok defined($building_lc),
	$msg->( qq{crea()});

$class = 'WPSOAP::Collection::Activity::PERMESSO_BUILDING';
my $collPermessoBuilding = eval { 
	WPSOAP::Collection::Activity::PERMESSO_BUILDING->new(
		ART	=> $art 
	)
};
ok	defined($collPermessoBuilding),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collPermessoBuilding, $class;

my $permessoBuilding = $collPermessoBuilding->crea(
	"privatePermitDescription" => "Primo permesso per integrazione area permessi"
	, "privatePermitType" => 'Scavo civile'
	, "privatePermitCategory" => "Realizzazione civile"
	, "BUILDING_LC" => $building_lc
);
ok defined($permessoBuilding),
	$msg->( qq{crea()});

$class=ref($permessoBuilding);
my $action = 'RICHIESTA_PERMESSO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
	}
	),
	$msg->(qq{step($action)});

$action = 'SOSPENSIONE';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'CONFERMA_POSITIVA';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		authorizationDate => "2013-07-14T01:00:00+01:00",
	}
	),
	$msg->(qq{step($action)});
	
$class=ref($collPermessoBuilding);
$permessoBuilding = $collPermessoBuilding->crea(
	"privatePermitDescription" => "Primo permesso per integrazione area permessi 2"
	, "privatePermitType" => 'Scavo civile'
	, "privatePermitCategory" => "Realizzazione civile"
	, "BUILDING_LC" => $building_lc
);
ok defined($permessoBuilding),
	$msg->( qq{crea()});

$class=ref($permessoBuilding);
$action = 'SOSPENSIONE';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_PERMESSO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',	
	}
	),
	$msg->(qq{step($action)});

$action = 'RIFIUTO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$permessoBuilding = $collPermessoBuilding->crea(
	"privatePermitDescription" => "Primo permesso per integrazione area permessi 4"
	, "privatePermitType" => 'Scavo civile'
	, "privatePermitCategory" => "Realizzazione civile"
	, "BUILDING_LC" => $building_lc
);
ok defined($permessoBuilding),
	$msg->( qq{creaPermessoLavori()});

$action = 'ANNULLAMENTO';
ok $permessoBuilding->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($building_lc);
$action = 'MODIFICA';
ok $building_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'CHIUSURA';
ok $building_lc->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
