#!/usr/bin/perl -w

use strict;
use warnings;
use Test::More tests => 28;
use JSON;

use WPSOAP::Collection::System::NETWORK;
use WPSOAP::Collection::Activity::PERMESSO_LAVORI;

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# Test sulla creazione e gestione gerarchia
#

BEGIN {
	use_ok( 'API::ART' );
}

my ($class, $art);

my $msg = sub () { return join("::", $class, @_) . ($art && $art->last_error() ? '; LAST_ERROR="' . $art->last_error() . '"' : ''); };

$class = 'API::ART';
$art = eval { 
	API::ART->new(
		ARTID		=> $ARGV[0],
		USER		=> $ARGV[1],
		PASSWORD	=> $ARGV[2], 
		DEBUG		=> $ARGV[3], 
	)
};
ok	defined($art),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$art, $class;

my $customerId = "TIM";
my $contractId = "CREATION";
my $projectId = "99999";
my $projectGroupName = "PM_".$customerId."_".$contractId;

$class = 'WPSOAP::Collection::System::NETWORK';
my $collSystem = eval { 
	WPSOAP::Collection::System::NETWORK->new(
		ART	=> $art 
	)
};
ok	defined($collSystem),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collSystem, $class;

my $progetto = $collSystem->crea(
	"customerId"					=> $customerId
	, "contractId"					=> $contractId
	, "networkId"					=> $projectId
	, "__projectGroupName__"		=> $projectGroupName
);
ok defined($progetto),
	$msg->( qq{crea()});

my $action;
$class = 'WPSOAP::Collection::Activity::PERMESSO_LAVORI';
my $collPermessoLavori = eval { 
	WPSOAP::Collection::Activity::PERMESSO_LAVORI->new(
		ART	=> $art 
	)
};
ok	defined($collPermessoLavori),
	$msg->( qq{new()} . " " . $@ );
isa_ok	$collPermessoLavori, $class;

my $permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "targetAsset" => 'Network'
	, "assetId" => $projectId
);
ok defined($permessoLavori),
	$msg->( qq{crea()});

$class=ref($permessoLavori);
$action = 'RICHIESTA_PERMESSO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'SOSPENSIONE';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'PERMESSO_OTTENUTO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		authorizationDate => "2013-07-14T01:00:00+01:00",
		endAuthorizationDate => "2014-07-14T01:00:00+01:00",
		amount => 10
	}
	),
	$msg->(qq{step($action)});

ok $permessoLavori->get_current_status_name() eq 'CHIUSA',
	$msg->("check_stato_finale (".$permessoLavori->get_current_status_name().")");

$class=ref($collPermessoLavori);
$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 2"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "targetAsset" => 'Network'
	, "assetId" => $projectId
);
ok defined($permessoLavori),
	$msg->( qq{crea()});

$class=ref($permessoLavori);
$action = 'SOSPENSIONE';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});
	
$action = 'RIPRESA';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

$action = 'RICHIESTA_PERMESSO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'
	}
	),
	$msg->(qq{step($action)});

$action = 'PERMESSO_RIFIUTATO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

ok $permessoLavori->get_current_status_name() eq 'RIFIUTATA',
	$msg->("check_stato_finale (".$permessoLavori->get_current_status_name().")");

$class=ref($collPermessoLavori);
$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 3"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "targetAsset" => 'Network'
	, "assetId" => $projectId
);
ok defined($permessoLavori),
	$msg->( qq{crea()});

$class=ref($permessoLavori);
$action = 'ANNULLAMENTO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

ok $permessoLavori->get_current_status_name() eq 'ANNULLATA',
	$msg->("check_stato_finale (".$permessoLavori->get_current_status_name().")");

$permessoLavori = $collPermessoLavori->crea(
	"publicPermitDescription" => "Primo permesso per integrazione area permessi 4"
	, "publicPermitType" => 'Scavo tradizionale'
	, "authority" => 'Provincia'
	, "publicPermitCategory" => "Realizzazione civile"
	, "customerId" => $customerId
	, "contractId" => $contractId
	, "targetAsset" => 'Network'
	, "assetId" => $projectId
);
ok defined($permessoLavori),
	$msg->( qq{creaPermessoLavori()});

$action = 'RICHIESTA_PERMESSO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {
		requestDate => '2012-07-14T01:00:00+01:00',
		expectedAuthorizationDate => '2012-07-14T01:00:00+01:00',
		expectedEndAuthorizationDate => '2012-07-14T01:00:00+01:00'	
	}
	),
	$msg->(qq{step($action)});

$action = 'ANNULLAMENTO';
ok $permessoLavori->step(
	ACTION			=> $action
	,DESCRIPTION	=> $action
	,PROPERTIES		=> {}
	),
	$msg->(qq{step($action)});

ok $permessoLavori->get_current_status_name() eq 'ANNULLATA',
	$msg->("check_stato_finale (".$permessoLavori->get_current_status_name().")");

$class=ref($art);
ok	$art->cancel(),
	$msg->( qq{cancel()} );
