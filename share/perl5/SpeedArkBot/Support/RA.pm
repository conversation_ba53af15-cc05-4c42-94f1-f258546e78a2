package SpeedarkBot::Support::RA;
use strict;
use warnings;
use utf8;
use open qw(:std :utf8);
use Carp;
use SIRTI::ART::RemoteActivity::Source;
use SIRTI::ART::RemoteActivity::Event;
use Data::Dumper;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub new {
    my ( $this, %param ) = @_;
    my $class = ref $this || $this;
    croak __(q{Missing mandatory parameter 'art' of class API::ART})
        unless exists $param{art} && eval{$param{art}->isa('API::ART')};
    croak __(q{Missing mandatory numeric parameter 'id'})
        unless defined $param{id};
    my $self = {
        art => $param{art},
        id  => $param{id},
    };
    bless $self, $class;
}

sub art { shift->{art}; }
sub id  { shift->{id}; }

sub target {
    my ( $self ) = @_;
    SIRTI::ART::RemoteActivity::Target->new(
         DB                     => $self->art->_dbh
        ,SESSION_DESCRIPTION    => 'Recupero messaggi errore BOT'
        ,SOURCE_SERVICE      => 'BOT'
        ,SOURCE_CONTEXT      => 'SPEEDARK'
        ,TARGET_SERVICE      => 'ENFTTH_AP'
        ,TARGET_CONTEXT      => 'CUSTOMER_PROJECT'
        ,RA_DBLINK              => 'ENFTTH_CORE'
    );
}

sub ra {
    my ( $self, $id ) = @_;
    SIRTI::ART::RemoteActivity::Event->new(
        ID => $id,
        RA => $self->target,
    );
}

sub reason {
    my ( $self ) = @_;

    my @events = $self->target()->find_pending( SOURCE_REF => $self->id(), EVENT => 'REQUEST_LOOKUP_DOCUMENTATION' );
    my %reason;
    for my $eventId ( @events ) {
        my $event = $self->ra($eventId);
        my $data = $event->get_data();
        my $reason = $data->{MESSAGGIO};
        $reason = 'Errore temporaneo' if $reason =~ m/^Gentile utente/;
        $reason = 'Errore temporaneo' if $reason =~ m/^\d{3} /;
        $reason{$reason}++;
    }
    return \%reason;
}

sub fetch_all {
    my ( $self, $status, $reason ) = @_;

    my @events = $self->target()->find_pending( SOURCE_REF => $self->id(), EVENT => 'REQUEST_LOOKUP_DOCUMENTATION' );
    my %reason;
    $self->art->_dbh()->do("savepoint SSR_fetch_all");
    for my $eventId ( @events ) {
        eval {
            $self->target->fetch(
                ID=>$eventId,
                TARGET_REF=>$self->id(),
                STATUS=>$status||0,
                REASON=>$reason||'OK'
            )
        };
        return undef
            && $self->art->_dbh()->do("rollback to savepoint SSR_fetch_all")
            && $self->art->last_error($@)
                if $@;
    }
    return 1;
}

1;
