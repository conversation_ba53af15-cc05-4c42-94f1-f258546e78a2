package WPSOAP;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use REST::Client;
use HTTP::Status qw(:constants :is status_message);
use JSON;
use Crypt::JWT qw(encode_jwt decode_jwt);
use HTTP::Request::Common;
use LWP::UserAgent;

use SIRTI::Reports;
use SIRTI::Cache;
use SIRTI::Geo::Coder::Google;

use WPSOAP::Profilo;
use WPSOAP::Collection::System::CUSTOMER;
use WPSOAP::Collection::System::CONTRACT;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

my $_are_master_sessions_registered = 0;

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{UTILS} = SIRTI::Reports->instance(DB => $self->_db());
	
	$self->{WS_BASE_URL} = $ENV{WS_BASE_URL};
	
	return bless( $self, $class );
}

sub art { shift->{ART} }

sub _db { shift->{ART}->_dbh() }

sub _logger { shift->{LOGGER} }

sub _utils { shift->{UTILS} }

sub _get_ws_base_url { shift->{WS_BASE_URL} }

sub camelize
{
	my $self = shift;
	my $s = shift;
	return lcfirst(join('', map{ ucfirst lc($_) } split(/(?<=[A-Za-z])_(?=[A-Za-z])|\b/, $s)));
}

sub manage_query{
	my $self = shift;
	my $query = shift;
	my $columns = shift;
	
	my $utils = $self->_utils();
	
	my $report = $utils->report(
		JSON_RETURN_STRING => 1,
		,OUTPUT_FORMAT => 'json'
		,QUERY => $query,
		,COLUMNS => $columns
	);
	
	$self->art()->last_error($utils->last_error())
		&& return undef
			unless $report;

	return decode_json($report)->{data};
	
}

sub profilo {
	my $self = shift;
	
	my $profilo = eval {
		WPSOAP::Profilo->new(ART => $self->art())
	};
	
	$self->art()->last_error($@)
		&& return undef
			if $@;
			
	return $profilo;
}

sub rest_client {
	my $self = shift;
	
	$self->{REST_CLIENT} = REST::Client->new( host => $self->_get_ws_base_url() )
		unless($self->{REST_CLIENT});
	
	return $self->{REST_CLIENT};
}

# FIXME: ora la funzione è utilizzata anche per entrare su altri sottosistemi e quindi
#        è scorretto che si chiami invoke_rest
sub invoke_rest {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 RESOURCE	=> { isa => 'SCALAR', pattern => qr{^/} }
				}
				,OPTIONAL => {
					 QUERY_PARAMS	=> { isa => 'HASH' }
					,METHOD			=> { isa => 'SCALAR', list => [ 'GET', 'POST', 'PUT', 'DELETE' ]}
					,BODY			=> { isa => 'HASH' }
					,RAW_BODY		=> { isa => 'SCALAR' }
					,HEADERS		=> { isa => 'HASH' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	$params{METHOD} = 'GET'
		unless defined $params{METHOD};
	
	$params{HEADERS} = { 'Accept' => 'application/json' }
		unless defined $params{HEADERS};
	
	$self->art()->last_error(__("You cannot use BODY and RAW_BODY jointly"))
		&& return undef
			if(defined $params{BODY} && defined $params{RAW_BODY});
	
	$self->art()->last_error(__("You cannot use BODY param with method GET or DELETE"))
		&& return undef
			if(defined $params{BODY} && $params{METHOD} =~ /^(GET|DELETE)$/);
	
	my $rest_client = $self->rest_client();
	
	my $query_params = '';
	if(defined $params{QUERY_PARAMS}) {
		$query_params = $rest_client->buildQuery(
			%{$params{QUERY_PARAMS}}
		);
	}
	my $url = $params{RESOURCE}.$query_params;
	
	$self->_logger()->debug("Invoco il ws $params{METHOD} ".$self->_get_ws_base_url().$url);
	my @rest_params = ($url);
	if($params{METHOD} =~ /^(POST|PUT)$/) {
		if(defined $params{BODY}) {
			if($params{RAW_BODY}) {
				push @rest_params, $params{BODY};
			} else {
				push @rest_params, encode_json($params{BODY});
				$params{HEADERS}->{"Content-Type"} = 'application/json';
			}
		} else {
			push @rest_params, {};
		}
	}
	
	# rendo tutte minuscole le chiavi in quanto l'headers è case-insensitive
	for my $k (keys %{$params{HEADERS}}){
		# se la chiave è già minuscola  non devo fare nulla
		next if lc($k) eq $k;
		# altrimenti la converto in minuscola e cancello la chiave esistente
		$params{HEADERS}->{lc($k)} = $params{HEADERS}->{$k};
		delete $params{HEADERS}->{$k};
	}
	
	push @rest_params, $params{HEADERS};
	my $method = $params{METHOD};
	#use Data::Dumper;
	#print STDERR Dumper \@rest_params;
	$rest_client->$method(@rest_params);
	my $response_code = $rest_client->responseCode();
	my @response_headers = $rest_client->responseHeaders();
	my $response_content = $rest_client->responseContent();
	my $response_headers_hash = {};

	$self->_logger()->debug("responseCode: ".$response_code." - ".status_message($response_code));
	$self->_logger()->trace("responseContent : ".$response_content);
	for my $head (@response_headers) {
		$head = lc $head;
		$self->_logger()->debug("responseHeaders.".$head." : ".$rest_client->responseHeader($head));
		$response_headers_hash->{$head} = $rest_client->responseHeader($head);
	}

	my $ret;
	if(is_success($response_code)) {
		if(defined $response_headers_hash->{'content-type'} && $response_headers_hash->{'content-type'} =~ /application\/json/) {
			$ret = eval{ decode_json $response_content };
			if($@) {
				$self->art()->last_error(__x("Unable to parse server response (HTTP_STATUS: {response_code}, MESSAGE: {response_content})", response_code => $response_code, response_content => $response_content));
				return undef;
			}
		} else {
			$ret = $response_content;
		}
	} elsif(is_client_error($response_code)) {
		$self->art()->last_error(__x("Client error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})", response_code => $response_code, response_content => $response_content));
		#return undef;
		return wantarray ? ($response_code, $response_content, $response_headers_hash) : undef;
	} elsif(is_server_error($response_code)) {
		$self->art()->last_error(__x("Server error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})", response_code => $response_code, response_content => $response_content));
		#return undef;
		return wantarray ? ($response_code, $response_content, $response_headers_hash) : undef;
	}

	return wantarray ? ($response_code, $response_content, $response_headers_hash) : $ret;
}

# la seguente funzione statica registra nel cache server la sessione master per l'utente
# accetta il parametro DEBUG => 0|1
sub register_master_sessions {
	my %params = @_;
	
	# verifico se ho già inserito le sessioni master nel cache server
	print STDERR "DEBUG> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Verifico se ho già inserito le sessioni master nel cache server\n"
		if($params{DEBUG});
	return
		if($_are_master_sessions_registered);
	
	my $CACHE = eval { SIRTI::Cache->new(DRIVER => "SIRTI::Cache::Driver::Memcached", SERVER => $ENV{ART_WS_CACHE_MEMCACHED_SERVER}); };
	if($@) {
		print STDERR "ERROR> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Impossibile istanziare la cache per salvare le sessioni master: $@";
		return;
	}
	
	# recupero le master session da gestire
	my @instances = split (',', $ENV{SERVICE_INSTANCES});
	
	for my $instance (@instances){
	
		# Registro nel cache server la sessione master dell'utente
		print STDERR "DEBUG> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Registro nel cache server la sessione master dell'utente ".$ENV{$instance."_USER_USERNAME"}." (".$ENV{$instance."_USER_SID"}.")\n"
			if($params{DEBUG});
	
		eval { $CACHE->set($ENV{$instance."_USER_SID"}, { username => $ENV{$instance."_USER_USERNAME"}, password => $ENV{$instance."_USER_PASSWORD"} }) };
		if($@) {
			print STDERR "ERROR> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: Impossibile salvare la sessione master dell'utente ".$ENV{$instance."_USER_USERNAME"}." (".$ENV{$instance."_USER_SID"}."): $@";
			return;
		}
		# La sessione master dell'utente è stata salvata
		print STDERR "DEBUG> [$$] $ENV{ART_APPLICATION_NAME}::register_master_sessions: La sessione master dell'utente ".$ENV{$instance."_USER_USERNAME"}." (".$ENV{$instance."_USER_SID"}.") è stata salvata\n"
			if($params{DEBUG});
	}
	
	$_are_master_sessions_registered = 1;
	
	return 1
}

sub customers {
	my $self = shift;
	my %params = @_;

	return $self->{CUSTOMERS} if defined $self->{CUSTOMERS};

	$self->{CUSTOMERS} = eval {
		WPSOAP::Collection::System::CUSTOMER->new(
			ART => $self->art()
		)
	};

	$self->art()->last_error($@)
		&& return undef
			if $@;

	return $self->{CUSTOMERS};
}

sub get_ref_group_by_context{
	my $self = shift;
	my %params = @_;
		
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					context		=> { isa => 'SCALAR', list => ['OF_AB', 'CREATION_TIM_OLO'] }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 0
			);
	
	return $params{context} eq 'OF_AB' ? 'USER' : 'USER01';
}

sub contracts {
	my $self 		= shift;
	my %params 		= @_;
	my $customerId	= $params{customerId}; 
	
	return $self->{CONTRACTS}->{$customerId} if exists $self->{CONTRACTS}->{$customerId};
	
	my $collCustomer = WPSOAP::Collection::System::CUSTOMER->new(ART => $self->art());
	my $customers = $collCustomer->cerca(customerId=>$customerId);
	
	$self->{CONTRACTS}->{$customerId} = eval {
		WPSOAP::Collection::System::CONTRACT->new(
			ART 		=> $self->art()
			,CUSTOMER	=> $customers->[0] 
		)
	};
	
	$self->art()->last_error($@)
		&& return undef
			if $@;
	
	return $self->{CONTRACTS}->{$customerId};
}

sub get_cdl_by_RO_central {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					RO	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					centralId	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $sql;
	my $prepare;
	my @bind_params;
	if (defined $params{centralId}){
		$sql = qq{
			select distinct cr.cdl
			from ap.mv_centrali_ro_cdl cr
			where cr.RO = ?
				and cr."centralId" = ?
			order by cr.cdl
		};
		
		$prepare = $self->art()->_create_prepare(__PACKAGE__.'_cdlROc', $sql);
		
		@bind_params = (
			$params{RO},
			$params{centralId}
		);
	} else {
		$sql = qq{
			select distinct cr.cdl
			from ap.mv_centrali_ro_cdl cr
			where cr.RO = ?
			order by cr.cdl
		};
		
		$prepare = $self->art()->_create_prepare(__PACKAGE__.'_cdlRO', $sql);
		
		@bind_params = (
			$params{RO}
		);
	}
	
	my $res = $prepare->fetchall_hashref(@bind_params);
	
	unless ($res) {
		$self->art()->last_error($self->_db()->get_errormessage());
		return undef;
	}
	
	return [map {$_->{CDL}} @{$res}];
}

sub get_cdl_by_customer_contract_central {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					customerId	=> { isa => 'SCALAR' },
					contractId	=> { isa => 'SCALAR' },
					centralId	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);

	my $map = {
		TIM => {
			CREATION => 'YR'
		}
	};

	unless (exists $map->{$params{customerId}}->{$params{contractId}}){
		$self->art()->last_error(__x("Missing mapping for customerId {customerId} and contractId {contractId}", customerId => $params{customerId}, contractId => $params{contractId}));
		return undef;
	}

	my $sql = qq{
		select distinct cr.cdl
		from ap.mv_centrali_ro_cdl cr
		where cr.TIPO_INTERVENTO = ?
			and cr."centralId" = ?
		order by cr.cdl
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_cdlewtc', $sql);
	
	my @bind_params = (
		$map->{$params{customerId}}->{$params{contractId}},
		$params{centralId}
	);

	
	my $res = $prepare->fetchall_hashref(@bind_params);
	
	unless ($res) {
		$self->art()->last_error($self->_db()->get_errormessage());
		return undef;
	}
	
	return [map {$_->{CDL}} @{$res}];
}

sub get_geo_object {

	my $self = shift;

	unless (exists $self->{GEO}){
		for ('GMAP_CLIENT', 'GMAP_KEY'){
			$self->art()->last_error(__x("Missing env {env}", env => $_))
					&& return undef
							unless defined $ENV{$_};
		}

		$self->{GEO} = eval {
			SIRTI::Geo::Coder::Google->new(
					client => $ENV{GMAP_CLIENT},
					key => $ENV{GMAP_KEY},
			)
		};
		return undef
				if $@;
	}

	return $self->{GEO};
}

sub get_aor_ro_by_central {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					centralId	=> { isa => 'SCALAR' },
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);

	my $sql = qq{
		select distinct SETTORE,AOR,RO,CITTA,CLLI
		from AP.TT001_REF_CENTRALI
		where ACL = ?
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_aorRo', $sql);
	my $res = $prepare->fetchall_hashref($params{centralId});
	
	unless ($res) {
		$self->art()->last_error($self->_db()->get_errormessage());
		return undef;
	}
	
	return $res;
}

sub get_customerContactsTo_CC {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					AOR				=> { isa => 'SCALAR' },
					RO				=> { isa => 'SCALAR' },
					customerSystem	=> { isa => 'SCALAR' },
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);

	my $sql = qq{
		select distinct "TO","CC"
		from AP.TT001_REF_DESTINATARI_TT
		where AOR = ?
        and RO = ? 
        and "Applicativo" = ?
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_customerContacts', $sql);
	
	
	my $res = $prepare->fetchall_hashref($params{AOR},$params{RO},$params{customerSystem});
	
	unless ($res) {
		$self->art()->last_error($self->_db()->get_errormessage());
		return undef;
	}
	
	return $res;
}

sub get_email_AT_by_CIDASP{
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					technicalAssistantId	=> { isa => 'SCALAR' },
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);

	my $sql = qq{
		select distinct "EMAIL",
		from ap.mv_anag_persone
		where CIDSAP = ?
        
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_get_email_AT', $sql);
	
	
	my $res = $prepare->fetchall_hashref($params{technicalAssistantId});
	
	unless ($res) {
		$self->art()->last_error($self->_db()->get_errormessage());
		return undef;
	}
	
	return $res;
}

sub get_resp_user{
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					technicalAssistantId	=> { isa => 'SCALAR' },
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);

	my $sql = qq{
		select *
		from ap.mv_anag_persone_resp
		where USER_CIDSAP = ?
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_get_email_AT', $sql);
	
	
	my $res = $prepare->fetchall_hashref($params{technicalAssistantId});
	
	unless ($res) {
		$self->art()->last_error($self->_db()->get_errormessage());
		return undef;
	}
	
	return $res->[0]||{};
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => $ENV{ART_SCRIPT_USER},
			PASSWORD => $ENV{ART_SCRIPT_PASSWORD}
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoap = eval{
		WPSOAP->new(ART => $art);
	};
	
	if ($@) {
		die "WPSOAP error: $@";
	}
	
	my $res= $wpsoap->get_cdl_by_customer_contract_central(
		customerId => 'TIM',
		contractId => 'CREATION',
		centralId => '01905G'
	);
	
	if( defined $res) {
		get_logger()->info("get_cdl_by_customer_contract_central: OK");
		get_logger()->info(Dumper $res);
	} else {
		get_logger()->error("profilo Error: " . $art->last_error());
		die;
	}

}

1;
