package WPSOAP::Collection::System::TT001;

use strict;
use warnings;
use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOAP;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOAP::System::TT001";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;

	$self = $self->SUPER::new(ART => $params->{ART});
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOCORE::LIB::' . __PACKAGE__ );

	$self->{WPSOAP} = WPSOAP->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _wpsoap{shift->{WPSOAP}}

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' },
					"networkId"			=> { isa => 'SCALAR' },
					"customerSystem" 	=> { isa => 'SCALAR' },
					#"contractId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"__FIND_ALL__" 		=> { isa => 'SCALAR',list=>[1] },
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $search_params = {
		customerId => $params{customerId},
		networkId => $params{networkId},
		customerSystem => $params{customerSystem},
		#contractId => $params{contractId}
	};

	return $self->_cerca(
		PROPERTIES => $search_params,
		__FIND_ALL__ => $params{__FIND_ALL__}||0
	);
}

sub _cerca{
    my $self = shift;
    my %params = @_;

    $params{PROPERTIES} = {} unless exists $params{PROPERTIES};

    my %search_params = (
        SYSTEM_TYPE_NAME => ['TT001'],
        ACTIVE => 1,
        SHOW_ONLY_WITH_VISIBILITY => 1,
		%params,
    );
    if ($params{__FIND_ALL__}) {
        delete $search_params{SHOW_ONLY_WITH_VISIBILITY};
        delete $params{__FIND_ALL__};
    }

    # cerco i sistemi aperti con property
    my $systems = $self->find_object(%search_params);

    unless (defined $systems){
        $self->_logger()->error( $self->_art()->last_error() );
        return undef;
    }

    return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' },
					"networkId"		=> { isa => 'SCALAR' },
					"customerSystem" 	=> { isa => 'SCALAR' },
					"contractId"		=> { isa => 'SCALAR' },
					"workingGroupCode"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"sector"							=> { isa => 'SCALAR' }
					,"AOR"								=> { isa => 'SCALAR' }
					,"RO"								=> { isa => 'SCALAR' }
					,"technicalAssistantName"			=> { isa => 'SCALAR' }
					,"central"							=> { isa => 'SCALAR' }
					,"centralId"						=> { isa => 'SCALAR' }
					,"tt001TargetAsset"					=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	my $description;
	if(defined $params{tt001TargetAsset}){
		$description = $params{"networkId"}.'_'.$params{"tt001TargetAsset"}.'_'.$params{"customerSystem"};
	}else{
		$description = $params{"networkId"}.'_'.$params{"customerSystem"};
	}
	my $groups = [
		'ADMIN',
		'GBD',
		'PM_'.$params{"customerId"}.'_'.$params{"contractId"},
		'CL_'.$params{"workingGroupCode"},


	];
	my $profilo = $self->_wpsoap->profilo;
	if ($profilo->is_service){
		my $g = $profilo->gruppi;
		my @gf = grep {$_=~/^SERVICE_/}@$g;
		if (scalar @gf){
			push @$groups,$gf[0];
		}
	}
	

	my %create_params = (
		SYSTEM_TYPE_NAME => 'TT001'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOAP'
		, DESCRIPTION => $description
		, GROUPS => $groups
		, PROPERTIES => \%params
	);
	
	$self->get_db()->do( "savepoint WPSOAP_Cll_Sys_TT001_crea" );

	my $system = $self->create (%create_params);
	
	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_TT001_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOAP::Collection::System::TT001->new(ART => $art);
	
	my $obj = $coll->crea(
		"customerId"		=> 'ENEL'
#		"requestType"		=> 'As-Built'
#		,"requestContext" 	=> 'Cables'
#		,"description"		=> 'ENEL'
#		,"requestor"		=> 'works'
#		,"asBuiltId"		=> '144311'            
	);
	
	if( defined $obj) {
		get_logger()->info("OK: ".$obj->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
