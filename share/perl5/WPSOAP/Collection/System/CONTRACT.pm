package WPSOAP::Collection::System::CONTRACT;

use strict;
use warnings;

use Data::Dumper;
use JSON;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use WPSOAP;

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOAP::System::CONTRACT";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'CUSTOMER')) unless defined $params->{CUSTOMER};
	die(__x('Param {paramname} must be of type {type}', paramname => 'CUSTOMER', type => 'WPSOAP::System::CUSTOMER')) if ref($params->{CUSTOMER}) ne 'WPSOAP::System::CUSTOMER';
	
	$self->{ART} = $params->{ART};
	
	$self->{CUSTOMER} = $params->{CUSTOMER};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub _customer { shift->{CUSTOMER} }

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					
				}
				,OPTIONAL	=> {
				   "contractId"		=> { isa => 'SCALAR' }
				  ,"contractName"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $search_params = {};
	
	$search_params->{contractId}	=	$params{contractId} 	if defined $params{contractId};
	$search_params->{contractName}	=	$params{contractName} 	if defined $params{contractName};
		
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	$params{PROPERTIES}->{customerId} = $self->_customer()->property('customerId');
		
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['CONTRACT']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub _wpsoap { shift->{WPSOAP} }

sub crea {
	
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 contractId			=> { isa => 'SCALAR' }
					,contractName		=> { isa => 'SCALAR' }
					,operationalContext	=> { isa => 'SCALAR' }
					
				}
				,OPTIONAL => {
					externalWorkTypeId	=> { isa => 'SCALAR' }
					,requiredBlocks	=> { isa => 'ARRAY' }
					,contractClaim	=> { isa => 'SCALAR' }
					,backgroundURL	=> { isa => 'SCALAR' }
					,logoURL		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $groups = ['ADMIN'];
	push @{$groups}, 'USER01' if $params{operationalContext} ne 'OC00'; 
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'CONTRACT'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOAP'
		, DESCRIPTION => $params{contractId}
		, GROUPS => $groups
		, PROPERTIES => {
			 "customerId" 				=> $self->_customer()->property('customerId')
			,"contractId"				=> $params{contractId}
			,"contractName"				=> $params{contractName}
			,"operationalContext"		=> $params{operationalContext}
		}
	);
	
	# cerco se esiste un sistema aperto per il contract sul customer passato
	my $contracts = $self->cerca( contractId => $params{contractId} );
	return undef
		unless defined $contracts;
	
	$self->art()->last_error(__("contractId already present!"))
		&& return undef
			if scalar @{$contracts};
	
	$create_params{PROPERTIES}->{externalWorkTypeId}	= $params{"externalWorkTypeId"} if defined $params{"externalWorkTypeId"};
	$create_params{PROPERTIES}->{requiredBlocks}	= $params{"requiredBlocks"} if defined $params{"requiredBlocks"};
	$create_params{PROPERTIES}->{contractClaim}		= $params{"contractClaim"} if defined $params{"contractClaim"};
	$create_params{PROPERTIES}->{backgroundURL}		= $params{"backgroundURL"} if defined $params{"backgroundURL"};
	$create_params{PROPERTIES}->{logoURL}			= $params{"logoURL"} if defined $params{"logoURL"};
	
	$self->get_db()->do( "savepoint WPSOAP_Cll_Sys_CONTRACT_crea" );
		
	my $system = $self->create(%create_params);
	
	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_CONTRACT_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef; 
	}
	
	my $adozione = $self->_customer()->adopt_children(CHILDREN => [$system]);
	
	unless (defined $adozione){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_CONTRACT_crea" );
		return undef;
	}
	
	# creo il nuovo gruppo per FC
	if ($params{operationalContext} ne 'OC00'){
		
		my $group_name = "PM_" . $self->_customer()->property('customerId')."_".$params{contractId};
		
		unless ($self->_art()->test_group_name($group_name)){
			my $group = $self->_art()->create_group(NAME=>$group_name, DESCRIPTION=> $group_name, IS_AUTOGENERATED=> 1);
				
			unless (defined $group){
				$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_CONTRACT_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef; 
			}
		}
	}
	
	$self->_logger()->debug("Info: ".Dumper($system->info()));
	$self->_logger()->debug("Properties: ".Dumper($system->property()));
	
	return $system;
}


if (__FILE__ eq $0) {

	use API::ART;
	
	eval "use WPSOAP::System::CONTRACT;";

	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
=pod	
	my $contract = WPSOAP::System::CONTRACT->new(ART => $art, ID => 2237);
	
	unless (defined $contract) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($contract));
	}
	
	my $coll = WPSOAP::Collection::System::CITY->new(ART => $art, CONTRACT => $contract);
	
	my $city = $coll->crea(
		"cadastralCode"	=> "A794"
	   	,"city"	 	=> "BERGAMO"
		,"province"  	=> "BG"
	);
	
	if( defined $city) {
		get_logger()->info("OK: ".$city->id(). " ". $city->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
#	my $aps = $coll->cerca(
#		"permitsAreaId" => '1'
#	);
#	
#	if( defined $aps) {
#		get_logger()->info("OK: trovate ".scalar (@{$aps}). " attività");
#		for my $ap (@{$aps}){
#			get_logger()->info("OK: ".$ap->id(). "(".$ap->name().")");
#		}
#		#print STDERR Dumper $ap_lc->property();
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
=cut
	
}

1;
