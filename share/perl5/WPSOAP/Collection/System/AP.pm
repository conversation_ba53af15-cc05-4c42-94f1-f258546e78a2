package WPSOAP::Collection::System::AP;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOAP::System::AP";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(ART => $params->{ART});
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'PROJECT')) unless defined $params->{PROJECT};
	die(__x('Param {paramname} must be of type {type}', paramname => 'PROJECT', type => 'WPSOAP::System::PROJECT')) if ref($params->{PROJECT}) ne 'WPSOAP::System::PROJECT';
	
	$self->{ART} = $params->{ART};
	
	$self->{PROJECT} = $params->{PROJECT};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub _project { shift->{PROJECT} }

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"permitsAreaId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	return $self->_cerca(
		PROPERTIES => {
			permitsAreaId	=> $params{permitsAreaId}
		}
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	# imposto i filtri relativi al progetto
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	$params{PROPERTIES}->{customerId} = $self->_project()->property('customerId');
	$params{PROPERTIES}->{contractId} = $self->_project()->property('contractId');
	$params{PROPERTIES}->{projectId} = $self->_project()->property('projectId');
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['AP']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"permitsAreaId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	# blocco se non hai il profilo per l'apertura di un'area permessi
	if (
		!$self->_project()->wpsoap()->profilo()->is_referente_permessi_ap()
		&&
		$self->_project()->wpsoap()->profilo()->is_assistente_tecnico_civile()
		&&
		$self->_project()->wpsoap()->profilo()->is_coordinatore_assistente_tecnico_civile()
	){
		$self->_art()->last_error(__x("User {username} can not open {type}", username => $self->_project()->wpsoap()->profilo()->nome_utente(), type => 'AP'));
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}

	# cerco se esiste un sistema aperto per l'area_peremsso sul progetto passato
	my $sistemiAP = $self->_project()->get_children(SYSTEM_TYPE_NAME => ['AP'], SHOW_ONLY_WITH_VISIBILITY => 1);
	
	unless (defined $sistemiAP){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	for my $sistemaAp (@{$sistemiAP}){
		if ($sistemaAp->name() eq $params{permitsAreaId}){
			$self->_art()->last_error(__x("Permission Area {permssionArea} for project {project}, customer_id {customer_id} and contract_id {contract_id} already present!", permssionArea=> $params{permitsAreaId}, project => $self->_project()->property('projectId'), customer_id => $self->_project()->property('customerId'), contract_id => $self->_project()->property('contractId')) );
			return undef
		}
	}
	
	#imposto gruppi	
	my $ap_groups = ['ADMIN', 'CITY_'.$self->_project()->property('cityId'), 'PROJECT_'.$self->_project()->property('customerId').'_'.$self->_project()->property('contractId').'_'.$self->_project()->property('projectId')];
	
	my %create_params = (
		SYSTEM_TYPE_NAME => 'AP'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOAP'
		, DESCRIPTION => join("-", $self->_project()->property('customerId'), $self->_project()->property('contractId'), $self->_project()->property('projectId'), $params{permitsAreaId}) 
		, GROUPS => $ap_groups
		, PROPERTIES => {
			"customerId"		=> $self->_project()->property('customerId'),
			"contractId"		=> $self->_project()->property('contractId'),
			"projectId"			=> $self->_project()->property('projectId'),
			"permitsAreaId"		=> $params{"permitsAreaId"},
		}
	);
	
	$self->get_db()->do( "savepoint WPSOAP_Cll_Sys_AP_crea" );
	
	my $system = $self->create (%create_params);

	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_AP_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	my $adozione = $self->_project()->adopt_children(CHILDREN => [$system]);
	
	unless (defined $adozione){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_AP_crea" );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	eval "use WPSOAP::System::PROJECT;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $project = WPSOAP::System::PROJECT->new(ART => $art, ID => 2545);
	
	unless (defined $project) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($project));
	}
	
	my $coll = WPSOAP::Collection::System::AP->new(ART => $art, PROJECT => $project);
	
	my $ap = $coll->crea(
		"permitsAreaId" => 'Il Riz'
	);
	
	if( defined $ap) {
		get_logger()->info("OK: ".$ap->id(). " ". $ap->name());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
#	my $aps = $coll->cerca(
#		"permitsAreaId" => '1'
#	);
#	
#	if( defined $aps) {
#		get_logger()->info("OK: trovate ".scalar (@{$aps}). " attività");
#		for my $ap (@{$aps}){
#			get_logger()->info("OK: ".$ap->id(). "(".$ap->name().")");
#		}
#		#print STDERR Dumper $ap_lc->property();
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
