package WPSOAP::Collection::System::EL06;

use strict;
use warnings;

use Data::Dumper;
use API::ART::Collection::System;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOAP;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::Collection::System';

our $DEFAULT_CLASS_TO_CREATE = "WPSOAP::System::EL06";

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;

	$self = $self->SUPER::new(ART => $params->{ART});
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOCORE::LIB::' . __PACKAGE__ );

	$self->{WPSOAP} = WPSOAP->new(ART => $self->{ART});
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _wpsoap{shift->{WPSOAP}}

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' },
					"contractId"	=> { isa => 'SCALAR' },
					"siteId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $search_params = {
		customerId => $params{customerId},
		contractId => $params{contractId},
		siteId => $params{siteId},
	};
	
	return $self->_cerca(
		PROPERTIES => $search_params
	);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	$params{PROPERTIES} = {} unless exists $params{PROPERTIES};
	
	# cerco i sistemi aperti con property
	my $systems = $self->find_object(
		SYSTEM_TYPE_NAME	=> ['EL06']
		, ACTIVE			=> 1
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, %params
	);
	
	unless (defined $systems){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $systems;
}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
					,"customerType"	=> { isa => 'SCALAR', list => ["PNRR5G"] }
					,"siteId"		=> { isa => 'SCALAR' }
					,"investor"		=> { isa => 'SCALAR', list => ["Infratel"]}
					,"siteType"		=> { isa => 'SCALAR', list => ["Antenna 5G"] }
					,"centralId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"regionId"			=> { isa => 'SCALAR' }
					,"cityId"			=> { isa => 'SCALAR' }
					,"centralCLLI"		=> { isa => 'SCALAR' }
					,"central"			=> { isa => 'SCALAR' }
					,"FOL"				=> { isa => 'SCALAR' }
					,"primaryWorks"		=> { isa => 'SCALAR' }
					,"restorationWorks"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $description = $params{customerId}.'_'.$params{contractId}.'_'.$params{siteId};
	
	my $groups = [
		'ADMIN',
		'PM_'.$params{customerId}.'_'.$params{contractId},
	];

	# recuperare centrali per verificarne l'esistenza e recuperare i cdl
	my $cdls = $self->_wpsoap()->get_cdl_by_customer_contract_central(
		customerId => $params{customerId},
		contractId => $params{contractId},
		centralId => $params{centralId}
	);

	return undef unless defined $cdls;

	unless (scalar @$cdls){
		$self->_art()->last_error(__x("No working group code found for central {centralId}", centralId => $params{centralId}));
		return undef;
	}
	for (@$cdls){
		unless ($self->_art()->test_group_name('CL_'.$_)){
			$self->_art()->last_error(__x("Unknown working group {wgc}", wgc => $_));
			return undef;
		}
		push @$groups, 'CL_'.$_;
	}

	my %create_params = (
		SYSTEM_TYPE_NAME => 'EL06'
		, SYSTEM_CATEGORY_NAME => 'ATTIVO'
		, SYSTEM_CLASS_NAME => 'NR'
		, OBJECT_TYPE_NAME => 'WPSOAP'
		, DESCRIPTION => $description
		, GROUPS => $groups
		, PROPERTIES => \%params
	);
	
	$self->get_db()->do( "savepoint WPSOAP_Cll_Sys_EL06_crea" );

	my $system = $self->create (%create_params);
	
	unless (defined $system){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Sys_EL06_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	return $system;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOCORE::Collection::System::PERMIT->new(ART => $art);
	
	my $obj = $coll->crea(
#		"requestType"		=> 'As-Built'
#		,"requestContext" 	=> 'Cables'
#		,"description"		=> 'ENEL'
#		,"requestor"		=> 'works'
#		,"asBuiltId"		=> '144311'            
	);
	
	if( defined $obj) {
		get_logger()->info("OK: ".$obj->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
