package WPSOAP::Collection::Activity::BUILDING_LC;

use strict;
use warnings;

use File::Temp qw/ tempdir /;
use File::Copy;
use Text::CSV_XS;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;

use API::ART::APP::Activity::BUILDING_LC;
use WPSOAP::Collection::System::BUILDING;
use WPSOAP::Collection::System::PROJECT;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystemProgetto} = WPSOAP::Collection::System::PROJECT->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system_progetto{shift->{CollSystemProgetto}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 buildingSinfoId		=> { isa => 'SCALAR' }
					,projectId	=> { isa => 'SCALAR' }
					,customerId		=> { isa => 'SCALAR' }
					,contractId	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	### impostare checks
	# cerco se esiste un sistema aperto per il progetto
	my $sistemiProgetto = $self->_get_coll_system_progetto()->cerca(
		customerId		=> $params{customerId},
		contractId	=> $params{contractId},
		projectId		=> $params{projectId},
	);
	
	unless (defined $sistemiProgetto){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se non ho il sistema progetto si tratta di un'anomalia 
	if (scalar @{$sistemiProgetto} == 0){
		$self->_art()->last_error(__x("Project {project} for customer_id {customer_id} and contract_id {contract_id} not found!", project => $params{projectId}, customer_id => $params{customerId}, contract_id => $params{contractId}) );
		return undef
	}
	
	my $get_coll_system_building = eval{
		WPSOAP::Collection::System::BUILDING->new(
			ART => $self->_art(),
			PROJECT => $sistemiProgetto->[0]
		)
	};
	$self->_logger()->error($@)
		&& return undef
			if ($@);
	
	# cerco se esiste un sistema già aperto con le stesse info
	my $sistemi = $get_coll_system_building->cerca(
		buildingSinfoId			=> $params{buildingSinfoId},
	);
	
	unless (defined $sistemi){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se ho gia' il sistema si tratta di un'anomalia 
	if (scalar @{$sistemi} > 0){
		$self->_art()->last_error(__x("Building Lifecycle for {param}={value} and {param1}={value1} already created!", param => 'buildingSinfoId', value => $params{buildingSinfoId}, param1 => 'projectId', value1 => $params{projectId}) );
		return undef
	}
	
	$self->get_db()->do( "savepoint WPSOAP_Cll_Act_BDN_LC_crea" );
	
	my $sistema = $get_coll_system_building->crea(%params);
	
	unless (defined $sistema){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_BDN_LC_crea" );
		return undef;
	}
	
	my %create_params = (
		ACTIVITY_TYPE_NAME	=> 'BUILDING_LC'
		, DESCRIPTION		=> join("-", $params{customerId}, $params{contractId}, $params{projectId}, $params{buildingSinfoId})
		, PROPERTIES		=> {}
		, SYSTEM_ID => $sistema->id()
	);
	
	for my $dt (keys %{$self->_art()->enum_activity_property(ACTIVITY_TYPE_NAME => 'BUILDING_LC', ACTION => $self->_art()->get_activity_action_open_name())}) {
		$create_params{PROPERTIES}->{$dt} = $params{$dt} if defined $params{$dt};
	}
	my $complete_address = '';
	$complete_address .= $params{placeName}." " if defined $params{placeName};
	$complete_address .= $params{streetName}.", " if defined $params{streetName};
	$complete_address .= $params{streetNumber} if defined $params{streetNumber};
	$create_params{PROPERTIES}->{completeAddress} = $complete_address
		unless $complete_address eq '';
		
	my $activity = $self->_get_coll_activity()->create(%create_params);

	unless (defined $activity){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_BDN_LC_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	# inizializzo il conteggio sui permessi
	my $ups = $activity->update_permits_count();
	
	unless (defined $ups){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_BDN_LC_crea" );
		return undef;
	}
	
	return $activity;
}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"projectId"		=> { isa => 'SCALAR' }
					,"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"buildingSinfoId"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		ACTIVITY_TYPE_NAME_EQUAL => 'BUILDING_LC'
		,SYSTEM_PROPERTIES_EQUAL => {
			customerId	=> $params{customerId},
			contractId	=> $params{contractId},
			projectId	=> $params{projectId},
		}
	};
	
	if (defined $params{buildingSinfoId}){
		$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{buildingSinfoId} = $params{buildingSinfoId} if defined $params{buildingSinfoId};
		$searchParam->{LIMIT} = 1; #per efficienza in quanto attesa sola una attività in questa situazione
	} 
	
	#cerco il sistema progetto e verifico se su quel progetto c'è il building
	my $buildings = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $buildings){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $buildings;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOAP::Collection::Activity::BUILDING_LC->new(ART => $art);
	
	my $building_lc = $coll->crea(
		"projectId" => "862"
		, "buildingSinfoId" => 'B20170623105555'
		, "contractId" => 'FTTH'
		, "customerId" => 'ENEL'
	);
	
	if( defined $building_lc) {
		get_logger()->info("OK: ".$building_lc->id(). "(".$building_lc->get_current_status_name().")");
		get_logger()->info("OK: ".$building_lc->id(). Dumper($building_lc->system_property()));
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	my $buildings = $coll->cerca(
		"projectId" => "862"
		, "buildingSinfoId" => '2049497'
		, "contractId" => 'FTTH'
		, "customerId" => 'ENEL'
	);
	
	if( defined $buildings) {
		get_logger()->info("OK: trovate ".scalar (@{$buildings}). " attività");
		for my $act (@{$buildings}){
			get_logger()->info("OK: ".$act->id(). "(".$act->get_current_status_name().")");
		}
		#print STDERR Dumper $ap_lc->property();
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
