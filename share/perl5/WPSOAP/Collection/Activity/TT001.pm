package WPSOAP::Collection::Activity::TT001;

use strict;
use warnings;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use API::ART::Collection::Activity;
use WPSOAP;
use WPSOAP::Collection::System::TT001;
use WPSOAP::Collection::System::NETWORK;
use JSON;
use HTTP::Status qw(:constants :is status_message);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystemTT001} = WPSOAP::Collection::System::TT001->new(ART => $self->{ART});
	$self->{CollSystemNetwork} = WPSOAP::Collection::System::NETWORK->new(ART => $self->{ART});

	$self->{WPSOAP} = WPSOAP->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system_tt001{shift->{CollSystemTT001}}
sub _get_coll_system_network{shift->{CollSystemNetwork}}

sub _wpsoap{shift->{WPSOAP}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' }
					#,"networkId"		=> { isa => 'SCALAR' }
					,"customerSystem"	=> { isa => 'SCALAR' }
					,"networkLookup"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"sector"					=> { isa => 'SCALAR' }
					,"AOR"						=> { isa => 'SCALAR' }
					,"RO"						=> { isa => 'SCALAR' }
					,"technicalAssistantName"	=> { isa => 'SCALAR' }
					,"central"					=> { isa => 'SCALAR' }
					,"centralId"				=> { isa => 'SCALAR' }
					,"ATTACHMENTS"				=> { isa => 'ARRAY'  }
					,"STARTUP"					=> { isa => 'SCALAR', list => [1] }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);

	my $actProps = $self->_art->enum_activity_property(ACTIVITY_TYPE_NAME => 'TT001', EXTENDED_OUTPUT => 1, ACTION => 'APERTURA');
	return undef unless defined $actProps;

	my %create_params = (
		ACTIVITY_TYPE_NAME	=> 'TT001'
		, ID_CUSTOM			=> $self->_art()->get_activity_next_id()
		, PROPERTIES		=> {}
		#,SYSTEM_ID 		=> $sys_tt001->id()
	);
	
	$self->_art()->user()->su()->activity()->enable( $self->_art()->get_group_id($ENV{ART_BINDING_SUDO_GROUP}) );
	$self->get_db()->do( "savepoint WPSOAP_Cll_Act_TT001_crea" );

	my $profilo = $self->_wpsoap()->profilo();
	my $username = $profilo->nome_utente();
	#creationUserRole valorizzato in funzione del ruolo dell’utente che ha aperto il TT
	if($profilo->is_service()){
		$create_params{PROPERTIES}->{creationUserRole} = 'SERVICE';
		#subcontractCode valorizzato col codice azienda del Service
		my $subcontractCode = $profilo->get_partner_from_username(username => $username);
		$create_params{PROPERTIES}->{subcontractCode} = $subcontractCode if defined $subcontractCode;
	} elsif($profilo->is_assistente_tecnico()){
		$create_params{PROPERTIES}->{creationUserRole} = 'AT';
	} elsif($profilo->is_gruppo_banca_dati()){
		$create_params{PROPERTIES}->{creationUserRole} = 'GBD';
	}else{
		$create_params{PROPERTIES}->{creationUserRole} = 'OTHER';
	}

	my $net = $params{networkLookup};
	my ($networkId,$tt001TargetAsset);

	# verifico se network è YS o YR
	if ($net =~ /\s\|\s/) {
		($networkId, $tt001TargetAsset) = split /\s\|\s/,$net;
	} else {
		$networkId = $net;
	}
	$params{networkId} = $networkId;
	#$params{networkLookup} = $net;
	$params{tt001TargetAsset} = $tt001TargetAsset if defined $tt001TargetAsset;

	my ($response_code, $response_content, $response_headers_hash) = $self->_wpsoap->invoke_rest(
		RESOURCE =>  sprintf($ENV{TT001_NETWORK_RESOURCE},$params{customerId},$params{networkId})
		,METHOD =>'GET'
		,HEADERS => {
			'X-API-KEY' => $ENV{TT001_NETWORK_API_KEY}
		}
	);
	return undef unless defined $response_code;
	if(is_client_error($response_code)) {		
		if ($response_code eq HTTP_NOT_FOUND){
			my $old_network = $self->_get_coll_system_network()->cerca(
				customerId => $params{customerId},
				networkIdOld => $params{networkId}
			);
			unless (defined $old_network){
				$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_TT001_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				$self->_art()->user()->su()->activity()->disable();
				return undef;
			}
			if (scalar @{$old_network}){
				($response_code, $response_content, $response_headers_hash) = $self->_wpsoap->invoke_rest(
					RESOURCE =>  sprintf($ENV{TT001_NETWORK_RESOURCE},$params{customerId},$old_network->[0]->property('networkId'))
					,METHOD =>'GET'
					,HEADERS => {
							'X-API-KEY' => $ENV{TT001_NETWORK_API_KEY}
					}
				);
				return undef unless defined $response_code;
				if(is_client_error($response_code)) {		
					if ($response_code eq HTTP_NOT_FOUND){
						$self->_art()->last_error(__x("networkid {networkId} not found", networkId => $params{networkId}));
						return undef;
					} else {
						$self->_art()->last_error(__x("Sorry, an error response code {response_code} occurred. Please contact the system administrator", response_code => $response_code));
						return undef;
					}
				}
				elsif(is_server_error($response_code)){
					$self->_art()->last_error(__x("Sorry, an error occurred. Please try again in a minute. If the issue persists, please contact the system administrator"));
					return undef;
				}
				# se è subito la societarizzazione allora prendo il nuovo valore
				$params{networkId} = $old_network->[0]->property('networkId');
			}
			$self->_art()->last_error(__x("networkid {networkId} not found", networkId => $params{networkId}));
			return undef;
		}else{
			$self->_art()->last_error(__x("Sorry, an error response code {response_code} occurred. Please contact the system administrator", response_code => $response_code));
			return undef;
		}
	}
	elsif(is_server_error($response_code)){
		$self->_art()->last_error(__x("Sorry, an error occurred. Please try again in a minute. If the issue persists, please contact the system administrator"));
		return undef;
	} else {

		$response_content = from_json($response_content);

		# sovrascrivo sempre con il valore della network recuperato da CORE per gestire correttamente la societarizzazione
		$params{networkId} = $response_content->{_activity}->{info}->{description};
	}
	
	# test visibilità gruppi
	my $gruppi_utente = $self->_art->user->groups;
	my @common_groups;
	foreach my $group (@{$response_content->{_activity}->{system}->{info}->{groups}}) {
		push @common_groups, $group if grep { $self->_art()->get_group_name($_) eq $group || $self->_art()->get_group_name($_) =~/^(GBD|SERVICE)$/ } @$gruppi_utente;
	}
	unless (@common_groups) {
		$self->_art()->last_error(__x("You are not authorized to create a Ticket for this network"));
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}	
	if (!defined $response_content->{_activity}->{properties}->{workingGroupCode} || !$response_content->{_activity}->{properties}->{workingGroupCode}) {
		$self->_art()->last_error(__x("workingGroupCode not defined on the network"));
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	if (!defined $response_content->{_activity}->{properties}->{technicalAssistantName} || !$response_content->{_activity}->{properties}->{technicalAssistantName}) {
		$self->_art()->last_error(__x("workingGroupCode not defined on the network"));
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	if (!$params{STARTUP} && !$response_content->{_activity}->{info}->{status} eq 'ANNULLATA') {
		$self->_art()->last_error(__x("Network {networkId} is status {status}", networkId => $params{networkId}, status => $response_content->{_activity}->{info}->{status}));
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	# verifico che la network sia multiCabinet e di tipo Gestione armadio
	if (
		defined $response_content->{_activity}->{properties}->{multiCabinet} && $response_content->{_activity}->{properties}->{multiCabinet} eq 'SI' 
		&&  
		defined $response_content->{_activity}->{properties}->{networkPurpose} && $response_content->{_activity}->{properties}->{networkPurpose} eq 'Gestione armadio'
		){
		unless ($params{tt001TargetAsset}){
			$self->_art()->last_error(__x("Field '{field}' is mandatory for multiCabinet network and scope {scope}", field => $actProps->{'tt001TargetAsset'}->{LABEL}, scope => $response_content->{_activity}->{properties}->{networkPurpose}));
			$self->_art()->user()->su()->activity()->disable();
			return undef;
		}
		$create_params{PROPERTIES}->{tt001TargetAsset} = $params{tt001TargetAsset};
	}

	my %cerca_params = (
		customerId 	=> $params{customerId},
		networkId 	=> $params{networkId},
	);
	if (defined $create_params{PROPERTIES}->{tt001TargetAsset}){
		$cerca_params{tt001TargetAsset} = $params{tt001TargetAsset};
	}
	if (!$params{STARTUP}){	
		# verifico che non esista già un activity TT001 aperto per la network e tt001TargetAsset passato
		my $cerca = $self->cerca(%cerca_params);
		return undef unless defined $cerca;
		if (scalar @{$cerca}){
			if (defined $create_params{PROPERTIES}->{tt001TargetAsset}){
				$self->_art()->last_error(__x("Ticket already present for the network {networkId} and field '{field}' {tt001TargetAsset}: found activity with id {id}", networkId => $params{networkId}, field => $actProps->{'tt001TargetAsset'}->{LABEL}, tt001TargetAsset => $params{tt001TargetAsset}, id => $cerca->[0]->id()));
			} else {
				$self->_art()->last_error(__x("Ticket already present for the network {networkId}: found activity with id {id}", networkId => $params{networkId}, id => $cerca->[0]->id()));
			}
			$self->_art()->user()->su()->activity()->disable();
			return undef;
		}
	}
	
	$create_params{PROPERTIES}->{central} = $response_content->{_activity}->{properties}->{central};
	$create_params{PROPERTIES}->{contractId} = $response_content->{_activity}->{system}->{properties}->{contractId};
	$create_params{PROPERTIES}->{centralId} = $response_content->{_activity}->{properties}->{centralId};
	$create_params{PROPERTIES}->{technicalAssistantName} = $response_content->{_activity}->{properties}->{technicalAssistantName};
	$create_params{PROPERTIES}->{technicalAssistantId} = $response_content->{_activity}->{properties}->{technicalAssistantId};
	$create_params{PROPERTIES}->{workingGroupCode} = $response_content->{_activity}->{properties}->{workingGroupCode};
	if(defined $create_params{PROPERTIES}->{tt001TargetAsset}){
		$create_params{DESCRIPTION} = $params{"networkId"}.'_'.$params{"tt001TargetAsset"}.'_'.$params{"customerSystem"};
	}else{
		$create_params{DESCRIPTION} = $params{"networkId"}.'_'.$params{"customerSystem"};
	}
	
	# recupero i dt AOR,SECTOR,RO dal DB
	my $centrale = $self->_wpsoap->get_aor_ro_by_central(centralId => $response_content->{_activity}->{properties}->{centralId});
	return undef unless defined $centrale;
	if (scalar @{$centrale}){
		$create_params{PROPERTIES}->{sector} = $centrale->[0]->{SETTORE};
		$create_params{PROPERTIES}->{AOR} = $centrale->[0]->{AOR};
		$create_params{PROPERTIES}->{RO} = 	$centrale->[0]->{RO};
		# recupero i dt CustomerContactsTo,CustomerContactsCC dal DB
		my $referenti = $self->_wpsoap->get_customerContactsTo_CC(AOR => $create_params{PROPERTIES}->{AOR},RO => $create_params{PROPERTIES}->{RO},customerSystem => $params{customerSystem});
		return undef unless defined $referenti;
		if (scalar @{$referenti}){
			$create_params{PROPERTIES}->{customerContactsTO} = $referenti->[0]->{TO};
			$create_params{PROPERTIES}->{customerContactsCC} = $referenti->[0]->{CC};
		}
	}else{
		$self->_art()->last_error(__x("Failed to get SECTOR,AOR,RO for centralId {centralId}", centralId => $response_content->{_activity}->{properties}->{centralId}));
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	#se non mi viene passato,valorizzo con la data di step
	unless ($params{creationDateEx}) {
		$create_params{PROPERTIES}->{creationDateEx} = $self->_art()->_dbh()->get_sysdate('yyyy-mm-dd');
	}
	
	for (
		"customerId"
		,"networkId"
		,"customerSystem"
		#,"networkLookup"
	){
		$create_params{PROPERTIES}->{$_} = $params{$_};
	}
	$create_params{PROPERTIES}->{networkLookup} = $net;
	# aggiunto clusterManager se presente
	my $clusterManager = $self->_wpsoap->get_resp_user(technicalAssistantId => $create_params{PROPERTIES}->{technicalAssistantId});
	return undef unless $clusterManager;
	if ($clusterManager->{RESP_CIDSAP}){
		$create_params{PROPERTIES}->{clusterManagerId} = $clusterManager->{RESP_CIDSAP};
		$create_params{PROPERTIES}->{clusterManagerName} = $clusterManager->{RESP_COGNOME}.' '.$clusterManager->{RESP_NOME};
	}

	my $sys_tt001;
	my $crea_sys = {};

	for (
		"customerId"
		,"networkId"
		,"customerSystem"
	){
		$crea_sys->{$_} = $params{$_};
	}
	for my $k(keys %{$create_params{PROPERTIES}}){
	
		$crea_sys->{$k} = $create_params{PROPERTIES}->{$k};
	}
	# creo il sistema tt001
	$sys_tt001 = $self->_get_coll_system_tt001()->crea(%$crea_sys);
	unless (defined $sys_tt001){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_TT001_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	
	$create_params{SYSTEM_ID} = $sys_tt001->id();

	$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
			
	if (exists $create_params{ATTACHMENTS}){
		my $meta = {
			activityId => $create_params{ID_CUSTOM},
			activityType => $create_params{ACTIVITY_TYPE_NAME},
		};
		$meta->{networkId} = $create_params{PROPERTIES}->{networkId};
		
		for my $attach (@{$create_params{ATTACHMENTS}}){
			unless (ref($attach)){
				$attach = {
					FILENAME => $attach
				};
			}
			$attach->{META} = $meta;
		}
	}
	
	my $tt001 = $self->_get_coll_activity()->create(%create_params);
	
	unless (defined $tt001){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_TT001_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	my $data = $tt001->get_data_for_notify_event();
        unless (defined $tt001->get_sender()->notify_event(%{$data})){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_TT001_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		$self->_art()->user()->su()->activity()->disable();
		return undef;
	}
	$self->_logger()->info(__x("Activity of type {type} created with id {id}", type => $tt001->info('ACTIVITY_TYPE_NAME'), id => $tt001->id()));
	$self->_art()->user()->su()->activity()->disable();

	return $tt001;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' },
					"networkId"			=> { isa => 'SCALAR' },
				}
				,OPTIONAL	=> {
					"__FIND_ALL__"		=> { isa => 'SCALAR', list => [1] },
					"tt001TargetAsset"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $search_system_properties = {};
	for my $key ( qw( customerId networkId tt001TargetAsset ) ) {
		$search_system_properties->{$key} = $params{$key} if defined $params{$key};
	}

	my $searchParam = {
		ACTIVE => 1,
		ACTIVITY_TYPE_NAME_EQUAL => 'TT001',
		SYSTEM_PROPERTIES_EQUAL => $search_system_properties,
	};
	if ($params{__FIND_ALL__}){
		delete $searchParam->{ACTIVE};
	}
	
	my $tt001 = $self->_get_coll_activity()->find_object(%{$searchParam});
	unless (defined $tt001){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $tt001;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'xt04420',
			#PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOAP::Collection::Activity::TT001->new(ART => $art);
	
	my $tt001 = $coll->crea(
		"customerId"		=> 'TIM'
		,"customerSystem"	=> 'NGNEER'
		#,"networkLookup"	=> [ '000500891655 | 00616C-047/247' ]
		,"networkLookup"	=> '000200000004'
	);
	
	if( defined $tt001) {
		get_logger()->info("OK: ".$tt001->id(). "(".$tt001->get_current_status_name().")");
		get_logger()->info("activity_property: ".Dumper $tt001->activity_property());
		get_logger()->info("system_property: ".Dumper $tt001->system_property());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	# unless($tt001->step(ACTION => 'DETTAGLI_ANOMALIA_NGNEER',
	# 	PROPERTIES => {
	# 		qtyPTELocked => 10,
	# 		reportingUser => '23456',
	# 		nameWO => 'rtyui',
	# 		typeWO => 'Network',
	# 		scope => 'Accesso',
	# 		scopeWO => 'Rame',
	# 		specialisationWO => 'FCOP',
	# 		errorSTD => 'Errore Disco Samba',
	# 	},
	# 	ATTACHMENTS => [
	# 		{
	# 			FILENAME => '/home/<USER>/.gitbuild/test.docx',
	# 			DOC_TYPE => 'ERROR_1'
	# 		}

	# 	]
	# 	)){
	# 	get_logger()->error("Error: " . $art->last_error());
	# } else {
	# 	get_logger()->info("OK: ".$tt001->id(). "(".$tt001->get_current_status_name().")");
	# 	get_logger()->info("activity_property: ".Dumper $tt001->activity_property());
	# 	get_logger()->info("system_property: ".Dumper $tt001->system_property());
	# }

	# unless($tt001->step(ACTION => 'APERTURA_TT_CLIENTE',
	# 	PROPERTIES => {
	# 		customerContactsTO => '<EMAIL>',
	# 	},
	# )){
	# 	get_logger()->error("Error: " . $art->last_error());
	# } else {
	# 	get_logger()->info("OK: ".$tt001->id(). "(".$tt001->get_current_status_name().")");
	# 	get_logger()->info("activity_property: ".Dumper $tt001->activity_property());
	# 	get_logger()->info("system_property: ".Dumper $tt001->system_property());
	# }

	# unless($tt001->step(ACTION => 'PRESA_IN_CARICO_CLIENTE',
	# 	PROPERTIES => {
	# 		customerTicketId => '<EMAIL>',
	# 	},
	# )){
	# 	get_logger()->error("Error: " . $art->last_error());
	# } else {
	# 	get_logger()->info("OK: ".$tt001->id(). "(".$tt001->get_current_status_name().")");
	# 	get_logger()->info("activity_property: ".Dumper $tt001->activity_property());
	# 	get_logger()->info("system_property: ".Dumper $tt001->system_property());
	# }

	# unless($tt001->step(ACTION => 'CHIUSURA_TT_CLIENTE',
	# 	PROPERTIES => {
	# 		#customerContactsTO => '<EMAIL>',
	# 	},
	# )){
	# 	get_logger()->error("Error: " . $art->last_error());
	# } else {
	# 	get_logger()->info("OK: ".$tt001->id(). "(".$tt001->get_current_status_name().")");
	# 	get_logger()->info("activity_property: ".Dumper $tt001->activity_property());
	# 	get_logger()->info("system_property: ".Dumper $tt001->system_property());
	# }

	# unless($tt001->step(ACTION => 'VALIDAZIONE_OK',
	# 	PROPERTIES => {
	# 		#customerContactsTO => '<EMAIL>',
	# 	},
	# )){
	# 	get_logger()->error("Error: " . $art->last_error());
	# } else {
	# 	get_logger()->info("OK: ".$tt001->id(). "(".$tt001->get_current_status_name().")");
	# 	get_logger()->info("activity_property: ".Dumper $tt001->activity_property());
	# 	get_logger()->info("system_property: ".Dumper $tt001->system_property());
	# }
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
