package WPSOAP::Collection::Activity::PERMESSO_BUILDING;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;

use WPSOAP::Collection::System::NETWORK;
use WPSOAP::Collection::System::PERMIT;
use WPSOAP::Profilo;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystemNetwork} = WPSOAP::Collection::System::NETWORK->new(ART => $self->{ART});
	
	$self->{CollSystemPermit} = WPSOAP::Collection::System::PERMIT->new(ART => $self->{ART});
	
	$self->{Profilo} = WPSOAP::Profilo->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system_network{shift->{CollSystemNetwork}}

sub _get_coll_system_permit{shift->{CollSystemPermit}}

sub _profilo{shift->{Profilo}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"privatePermitDescription"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"reference"						=> { isa => 'HASH' }
					,"invalidatePreviousReferences"	=> { isa => 'SCALAR', list => [0,1] }
					,"ATTACHMENTS"					=> { isa => 'ARRAY' }
					,"privatePermitCategory"	=> { isa => 'SCALAR', list => ["Realizzazione civile","Sottotubazione","Posa aerea"] }
					,"privatePermitType"		=> { isa => 'SCALAR', list => ["Scavo civile","Apertura pozzetto","Tubazione esterna UI","Posa in facciata"] }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);

	$params{reference} = {} unless exists $params{reference};
	
	#verifico che l'utente abbia i permessi per aprire un'attività PERMESSO_LAVORI
	$self->_art(__x("User {username} can not open {type}", username => $self->_profilo()->nome(), type => 'PERMESSO_BUILDING'))
		&& return undef
			if (
				!$self->_profilo()->is_referente_permessi_building()
				&&
				!$self->_profilo()->is_assistente_tecnico()
			);
	
	my $result;
	
	my $permesso;
	
	if (defined $params{BUILDING_LC}){
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"BUILDING_LC"				=> { isa => undef, inherits => [ 'API::ART::APP::Activity::BUILDING_LC' ] }
					}
					,OPTIONAL	=> {
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		my $building_lc = $params{BUILDING_LC};
		
		# se l'attività non è più attiva è un errore
		$self->_art("Building {id} closed!", id => $building_lc->id())->last_error()
			&& return undef
				unless $building_lc->is_active();
		
		my $ancestor = $building_lc->system()->ancestor();
		
		my %create_params = (
			ACTIVITY_TYPE_NAME	=> 'PERMESSO_BUILDING'
			, DESCRIPTION		=> $ancestor->property('customerId')."-".$ancestor->property('contractId')."-".$ancestor->property('projectId')."-".$building_lc->activity_property('buildingSinfoId')."-".$params{"privatePermitDescription"}
			, PROPERTIES		=> {
				 "buildingSinfoId"			=> $building_lc->activity_property("buildingSinfoId")
				,"projectId"				=> $building_lc->activity_property("projectId")
				,"privatePermitDescription"	=> $params{"privatePermitDescription"}
			}
			, SYSTEM_ID 		=> $building_lc->system()->id()
		);
        for my $key ( 'privatePermitCategory', 'privatePermitType') {
            $create_params{PROPERTIES}->{$key}= $params{$key} if defined $params{$key};
        }
		
		$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
		
		$self->get_db()->do( "savepoint WPSOAP_Cll_Act_PRM_BDN_crea" );
		
		$permesso = $building_lc->add_child(
			%create_params
		);
		
		unless (defined $permesso){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_BDN_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		my $ups = $building_lc->update_permits_count();
		
		unless (defined $ups){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_BDN_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		$self->_logger()->info(__x("Activity of type {type} created with id {id}", type => $permesso->info('ACTIVITY_TYPE_NAME'), id => $permesso->id()));
		$result = $permesso;
	} else { # gestione FC
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"customerId"	=> { isa => 'SCALAR' }
						,"contractId"	=> { isa => 'SCALAR' }
						,"targetAsset"	=> { isa => 'SCALAR', list => ['Network', 'ROE', 'PTE']}
					}
					,OPTIONAL	=> {
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		if (!defined $params{"assetId"}){
			$self->_art()->last_error(__x("Missing mandatory param {param}", param => 'assetId'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		if ($params{targetAsset} eq 'Network' && ref ($params{"assetId"}) eq 'ARRAY'){
			$self->_art()->last_error(__x("For {param} {value} {param1} can have only one value", param => 'targetAsset', value => $params{targetAsset}, param1 => 'assetId'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
			$self->_art()->last_error(__x("For {param} {value} {param1} must be an array", param => 'targetAsset', value => $params{targetAsset}, param1 => 'assetId'));
		if ($params{targetAsset} =~/^(ROE|PTE)$/ && ref ($params{"assetId"}) ne 'ARRAY'){
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		if ($params{targetAsset} eq 'Network'){
			# recupero la network associata
			my $networks = $self->_get_coll_system_network()->cerca(
				"customerId"	=> $params{"customerId"},
				"contractId"	=> $params{"contractId"},
				"networkId"		=> $params{assetId},
			);
			unless (defined $networks){
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			unless (scalar @{$networks}){
				$self->_art()->last_error(__x("No network found for customer {customerId}, contract {contractId} and networkId {networkId}", customerId => $params{"customerId"}, contractId => $params{"contractId"}, networkId => $params{assetId}));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			# se la trovo ne trovo solo una per costruzione
			
			my %create_params = (
				ACTIVITY_TYPE_NAME	=> 'PERMESSO_BUILDING'
				, ID_CUSTOM			=> $self->_art()->get_activity_next_id()
				, DESCRIPTION		=> $params{"customerId"}."-".$params{"contractId"}."-".$params{"assetId"}."-".$params{"privatePermitDescription"}
				, PROPERTIES		=> {
					"projectId"				=> $params{"assetId"}
					,"privatePermitDescription"	=> $params{"privatePermitDescription"}
					, "targetAsset"			=> $params{"targetAsset"}
				}
				, SYSTEM_ID 		=> $networks->[0]->id()
			);
            for my $key ( 'privatePermitCategory', 'privatePermitType') {
                $create_params{PROPERTIES}->{$key}= $params{$key} if defined $params{$key};
            }
			$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
			
			if (exists $create_params{ATTACHMENTS}){
				my $meta = {
					activityId => $create_params{ID_CUSTOM},
					activityType => $create_params{ACTIVITY_TYPE_NAME},
				};
				$meta->{networkId} = $create_params{PROPERTIES}->{projectId} if exists $create_params{PROPERTIES}->{projectId} && defined $create_params{PROPERTIES}->{projectId};
				
				for my $attach (@{$create_params{ATTACHMENTS}}){
					unless (ref($attach)){
						$attach = {
							FILENAME => $attach
						};
					}
					$attach->{META} = $meta;
				}
			}
			
			$self->get_db()->do( "savepoint WPSOAP_Cll_Act_PRM_BDN_crea" );
			
			$permesso = $self->_get_coll_activity()->create(
				%create_params
			);
			
			unless (defined $permesso){
				$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_BDN_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			$self->_logger()->info(__x("Activity of type {type} created with id {id}", type => $permesso->info('ACTIVITY_TYPE_NAME'), id => $permesso->id()));
			$result = $permesso;
			$result = [];
		} elsif ($params{targetAsset} =~/^(ROE|PTE)$/){
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> \%params
						,MANDATORY	=> {
							"workingGroupCode"	=> { isa => 'SCALAR' },
							"oneForAll"			=> { isa => 'SCALAR', list => [0,1] },
						}
						,OPTIONAL	=> {
							"networkId"	=> { isa => 'SCALAR' },
						}
						,IGNORE_EXTRA_PARAMS => 1
			);
			
			# verifico che tutti gli id passati siano effettivamente solo numerici
			for my $as (@{$params{assetId}}){
				$self->_art()->last_error(__x("assetId must be an integer"))
					&& return undef
						if $as !~ /^\d+$/;
			}

			if ($params{targetAsset} =~/^(PTE)$/){
				$self->_art()->last_error($errmsg)
					and return undef
						unless $self->_art()->check_named_params(
							ERRMSG		=> \$errmsg
							,PARAMS		=> \%params
							,MANDATORY	=> {
								"maker"	=> { isa => 'SCALAR', list => ['Team', 'Subcontract'] },
							}
							,IGNORE_EXTRA_PARAMS => 1
				);

				if ($params{maker} eq 'Team'){
					$self->_art()->last_error($errmsg)
						and return undef
							unless $self->_art()->check_named_params(
								ERRMSG         => \$errmsg
								,PARAMS         => \%params
								,MANDATORY      => {
										"team"        => { isa => 'HASH' }
								}
								,OPTIONAL       => {}
								,IGNORE_EXTRA_PARAMS => 1
					);

					$self->_art()->last_error($errmsg)
						and return undef
							unless $self->_art()->check_named_params(
								ERRMSG         => \$errmsg
								,PARAMS         => \%{$params{team}}
								,MANDATORY      => {
										"teamId"        => { isa => 'SCALAR' }
										,"teamName"     => { isa => 'SCALAR' }
								}
								,OPTIONAL       => {}
								,IGNORE_EXTRA_PARAMS => 1
					);

					
				} else {
					$self->_art()->last_error($errmsg)
					and return undef
						unless $self->_art()->check_named_params(
							ERRMSG         => \$errmsg
							,PARAMS         => \%params
							,MANDATORY      => {
								"subcontractInfo"	=> { isa => 'HASH' }
							}
							,OPTIONAL       => {}
							,IGNORE_EXTRA_PARAMS => 1
					);

					$self->_art()->last_error($errmsg)
						and return undef
							unless $self->_art()->check_named_params(
								ERRMSG         => \$errmsg
								,PARAMS         => \%{$params{"subcontractInfo"}}
								,MANDATORY      => {
									"subContractName"      => { isa => 'SCALAR' }
									,"subContractCode"      => { isa => 'SCALAR' }
								}
								,OPTIONAL       => {}
								,IGNORE_EXTRA_PARAMS => 1
					);
				}

			}
			
			# recupero la network associata se definito
			if (defined $params{networkId}){
				my $networks = $self->_get_coll_system_network()->cerca(
					"customerId"	=> $params{"customerId"},
					"contractId"	=> $params{"contractId"},
					"networkId"		=> $params{networkId},
				);
				unless (defined $networks){
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
				
				unless (scalar @{$networks}){
					$self->_art()->last_error(__x("No network found for customer {customerId}, contract {contractId} and networkId {networkId}", customerId => $params{"customerId"}, contractId => $params{"contractId"}, networkId => $params{assetId}));
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}

			}
			
			my $runs;
			if ($params{oneForAll}){
				$runs = 1;
			} else {
				$runs = scalar @{$params{assetId}};
				delete $params{"ATTACHMENTS"} if $runs > 1;
			}
			
			$self->get_db()->do( "savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
			
			for (my $i = 0; $i<$runs; $i++){
			
				# viene creato un nuovo sistema PERMIT
				my $sys_permit = $self->_get_coll_system_permit()->crea(
					"customerId"		=> $params{"customerId"},
					"contractId"		=> $params{"contractId"},
					"workingGroupCode"	=> $params{"workingGroupCode"},
					"assetId"			=> $params{"oneForAll"} ? $params{"assetId"} : [$params{"assetId"}->[$i]],
					"targetAsset"		=> $params{"targetAsset"}
				);
				unless (defined $sys_permit){
					$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
				
				my %create_params = (
					ACTIVITY_TYPE_NAME	=> 'PERMESSO_BUILDING'
					, ID_CUSTOM			=> $self->_art()->get_activity_next_id()
					, DESCRIPTION		=> $params{"customerId"}."-".$params{"contractId"}."-".$params{"workingGroupCode"}."-".$params{"privatePermitDescription"}
					, PROPERTIES		=> {
						"privatePermitDescription"	=> $params{"privatePermitDescription"}
						,"targetAsset"			=> $params{"targetAsset"}
					}
					, SYSTEM_ID 		=> $sys_permit->id()
				);
                for my $key ( 'privatePermitCategory', 'privatePermitType') {
                    $create_params{PROPERTIES}->{$key}= $params{$key} if defined $params{$key};
                }
				for my $ref (keys %{$params{reference}}){
					$create_params{PROPERTIES}->{$ref} = $params{reference}->{$ref};
				}

				if ($params{targetAsset} =~/^(PTE)$/){
					$create_params{PROPERTIES}->{maker} = $params{maker};
					if ($params{maker} eq 'Team'){
						$create_params{PROPERTIES}->{teamId} = $params{team}->{teamId};
						$create_params{PROPERTIES}->{teamName} = $params{team}->{teamName};
					} else {
						$create_params{PROPERTIES}->{subContractName} = $params{"subcontractInfo"}->{subContractName};
						$create_params{PROPERTIES}->{subContractCode} = $params{"subcontractInfo"}->{subContractCode};
					}
					$create_params{PROPERTIES}->{invalidatePreviousReferences} = $params{invalidatePreviousReferences}
						if $params{invalidatePreviousReferences};
				}
				$create_params{PROPERTIES}->{projectId} = $params{networkId} if defined $params{networkId};
				
				$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
				
				if (exists $create_params{ATTACHMENTS}){
					my $meta = {
						activityId => $create_params{ID_CUSTOM},
						activityType => $create_params{ACTIVITY_TYPE_NAME},
					};
					$meta->{networkId} = $create_params{PROPERTIES}->{projectId} if exists $create_params{PROPERTIES}->{projectId} && defined $create_params{PROPERTIES}->{projectId};
					for my $asset (@{$params{assetId}}){
						$meta->{"assetId-".$asset} = 1;
					}
					for my $attach (@{$create_params{ATTACHMENTS}}){
						unless (ref($attach)){
							$attach = {
								FILENAME => $attach
							};
						}
						$attach->{META} = $meta;
					}
				}
				$create_params{CREATION_USER} = $params{operatorLogin}
					if defined $params{operatorLogin};
				
				$permesso = $self->_get_coll_activity()->create(
					%create_params
				);
				
				unless (defined $permesso){
					$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
				if ($params{targetAsset} =~/^(PTE)$/){
					if (defined $params{subcontractInfo}->{subContractCode}){
						my $serviceGroupName = 'SERVICE_'.$params{subcontractInfo}->{subContractCode};
						# se il gruppo esiste già sull'istanza lo aggiungo alla visibilità della network
						if ($self->_art()->test_group_name($serviceGroupName)){
							$sys_permit->set_groups($serviceGroupName);
						}
					}
				}

				$self->_logger()->info(__x("Activity of type {type} created with id {id} and assetId => {assetId}", type => $permesso->info('ACTIVITY_TYPE_NAME'), id => $permesso->id(), assetId => join (',', @{$permesso->system_property('assetId')})));
				if ($params{oneForAll}){
					$result = $permesso;
				} else {
					push @{$result}, $permesso;
				}
			}
		}
	}
	
	return $result;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"		=> { isa => 'SCALAR' }
					
				}
				,OPTIONAL	=> {
					"contractId"		=> { isa => 'SCALAR' }
					,"targetAsset"		=> { isa => 'ARRAY', min => 1 }
					,"projectId"		=> { isa => 'SCALAR' }
					,"ref00"			=> { isa => 'SCALAR' }
					,"ref01"			=> { isa => 'SCALAR' }
					,"assetId"			=> { isa => 'SCALAR' }
					,"STATUS_IN"		=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		 ACTIVITY_TYPE_NAME_EQUAL	=> 'PERMESSO_BUILDING'
		,SYSTEM_PROPERTIES_EQUAL	=> {
			customerId		=> $params{customerId},
		}
	};
	
	for my $k ("targetAsset") {
		$searchParam->{ACTIVITY_PROPERTIES_IN}->{$k} = $params{$k} if defined $params{$k};
	}

	for my $k ("ref00","ref01","projectId") {
		$searchParam->{ACTIVITY_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}

	for my $k ("assetId", "contractId") {
		$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}

	for my $k ("STATUS_IN") {
		$searchParam->{$k} = $params{$k} if defined $params{$k};
	}
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $permits = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $permits){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $permits;
}

if (__FILE__ eq $0) {

	use API::ART;
	eval "use WPSOAP::Collection::Activity::BUILDING_LC";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $building_lc = API::ART::APP::Activity::BUILDING_LC->new(ART => $art, ID => 5972);
	
	if( defined $building_lc) {
		get_logger()->info("OK: ".$building_lc->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	my $coll = WPSOAP::Collection::Activity::PERMESSO_BUILDING->new(ART => $art);
	
	my $permesso = $coll->crea(
		'targetAsset' => 'PTE',
          'workingGroupCode' => 106656,
          'privatePermitType' => 'Scavo civile',
          'privatePermitCategory' => 'Realizzazione civile',
          'contractId' => 'FTTH',
          'customerId' => 'TIM',
          'privatePermitDescription' => 'test Fibercop',
          'ATTACHMENTS' => [],
          'reference' => {
                           'ref01' => '015',
                           'ref00' => '74401F'
                         },
          'assetId' => [
                         '62262'
                       ],
          'type' => 'private',
          'oneForAll' => 0,
          'authority' => 'Comune'

	);
	
	if( defined $permesso) {
		get_logger()->info("OK: ".$permesso->id(). "(".$permesso->get_current_status_name().")");
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
