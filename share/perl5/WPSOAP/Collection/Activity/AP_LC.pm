package WPSOAP::Collection::Activity::AP_LC;

use strict;
use warnings;

use JSON;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;

use WPSOAP::Collection::System::AP;
use WPSOAP::Collection::System::PROJECT;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystemProgetto} = WPSOAP::Collection::System::PROJECT->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system_progetto{shift->{CollSystemProgetto}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"permitsAreaId"			=> { isa => 'SCALAR' }
					,"projectId"			=> { isa => 'SCALAR' }
					,"polygon"				=> { isa => 'HASH' }
					,"requestDate"			=> { isa => 'SCALAR' }
					,"name"					=> { isa => 'SCALAR' }
					,"customerId"			=> { isa => 'SCALAR' }
					,"contractId"			=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"infrastructureLength"	=> { isa => 'SCALAR' }
					,"note"	=> { isa => 'SCALAR' }
					,"ATTACHMENTS"				=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$params{infrastructureLength} = 0 unless defined $params{infrastructureLength};
	
	# verifico che la polygon sia un JSON
	eval {
		$params{polygon} = encode_json($params{polygon});
	};
	if ($@){
		my $msg = __x('Invalid param {name}: {error}', name => __('polygon'), error => $@ );
		$self->_art()->last_error($msg);
		$self->_logger()->error($msg);
		return undef;	
	}
	
	
	### impostare checks
	# cerco se esiste un sistema aperto per il progetto
	my $sistemiProgetto = $self->_get_coll_system_progetto()->cerca(
		customerId		=> $params{customerId},
		contractId		=> $params{contractId},
		projectId		=> $params{projectId},
	);
	
	unless (defined $sistemiProgetto){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se ho gia' il sistema si tratta di un'anomalia 
	if (scalar @{$sistemiProgetto} == 0){
		$self->_art()->last_error(__x("Project {project} for customer_id {customer_id} and contract_id {contract_id} not found!", project => $params{projectId}, customer_id => $params{customerId}, contract_id => $params{contractId}) );
		return undef
	}
	
	my $get_coll_system_ap = eval{
		WPSOAP::Collection::System::AP->new(
			ART => $self->_art(),
			PROJECT => $sistemiProgetto->[0]
		)
	};
	
	$self->_logger()->error($@||$self->_art()->last_error)
		&& return undef
			if ($@ || ! defined $get_coll_system_ap);
	
	# cerco se esiste un sistema già aperto con le stesse info
	my $sistemi = $get_coll_system_ap->cerca(
		permitsAreaId	=> $params{permitsAreaId},
		projectId		=> $params{projectId},
		customerId		=> $params{customerId},
		contractId		=> $params{contractId},
	);
	
	unless (defined $sistemi){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	#se ho gia' il sistema si tratta di un'anomalia 
	if (scalar @{$sistemi} > 0){
		$self->_art()->last_error(__x("Permission Area for {param}={value} and {param1}={value1} already created!", param => 'permitsAreaId', value => $params{permitsAreaId}, param1 => 'projectId', value1 => $params{projectId}) );
		return undef
	}
	
	$self->get_db()->do( "savepoint WPSOAP_Cll_Act_AP_LC_crea" );
	
	my $sistema = $get_coll_system_ap->crea(%params);
	
	unless (defined $sistema){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_AP_LC_crea" );
		return undef;
	}
	
	my %create_params = (
		ACTIVITY_TYPE_NAME	=> 'AP_LC'
		, DESCRIPTION		=> $params{customerId}."-".$params{contractId}."-".$params{projectId}."-".$params{"permitsAreaId"}
		, PROPERTIES		=> {
			 "permitsAreaId"		=> $params{"permitsAreaId"}
			,"projectId"			=> $params{"projectId"}
			,"polygon"				=> $params{"polygon"}
			,"requestDate"			=> $params{"requestDate"}
			,"name"					=> $params{"name"}
			,"infrastructureLength" => $params{"infrastructureLength"}
		}
		, SYSTEM_ID => $sistema->id()
	);
	
	$create_params{PROPERTIES}->{note} = $params{"note"} if defined $params{"note"};
	
	$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
	
	my $activity = $self->_get_coll_activity()->create (%create_params);

	unless (defined $activity){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_AP_LC_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	
	# inizializzo il conteggio sui permessi
	my $ups = $activity->update_permits_count();
	
	# inizializzo il numero di TT aperti
	$activity->system()->set_property(PROPERTIES => { ongoingTT => 0 });
	
	unless (defined $ups){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_AP_LC_crea" );
		return undef;
	}
	
	return $activity;
}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"projectId"		=> { isa => 'SCALAR' }
					,"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"permitsAreaId"	=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		ACTIVITY_TYPE_NAME_EQUAL => 'AP_LC'
		,SYSTEM_PROPERTIES_EQUAL => {
			customerId	=> $params{customerId},
			contractId	=> $params{contractId},
			projectId	=> $params{projectId},
		}
	};
	
	if (defined $params{permitsAreaId}){
		$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{permitsAreaId} = $params{permitsAreaId} if defined $params{permitsAreaId};
		$searchParam->{LIMIT} = 1; #per efficienza in quanto attesa sola una attività in questa situazione
	} 
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $aps = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $aps){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $aps;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOAP::Collection::Activity::AP_LC->new(ART => $art);
	
	my $ap_lc = $coll->crea(
		"projectId" => "862"
		, "permitsAreaId" => '4'
		, "polygon" => {"coordinates" => [[[38.097943,13.369353],[38.09768,13.369102],[38.097831,13.368853]]],"type" => "Polygon"}
		, "requestDate" => '2012-07-14T01:00:00+01:00'
		, "name" => 'test AP'
		, "contractId" => 'FTTH'
		, "customerId" => 'ENEL'
	);
	
	if( defined $ap_lc) {
		get_logger()->info("OK: ".$ap_lc->id(). "(".$ap_lc->get_current_status_name().")");
		print STDERR Dumper $ap_lc->property();
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
#	my $aps = $coll->cerca(
#		"projectId" => "861"
#		, "permitsAreaId" => '3'
#		, "contractId" => 'FTTH'
#		, "customerId" => 'ENEL'
#	);
#	
#	if( defined $aps) {
#		get_logger()->info("OK: trovate ".scalar (@{$aps}). " attività");
#		for my $ap (@{$aps}){
#			get_logger()->info("OK: ".$ap->id(). "(".$ap->get_current_status_name().")");
#		}
#		#print STDERR Dumper $ap_lc->property();
#	} else {
#		get_logger()->error("Error: " . $art->last_error());
#		die;
#	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
