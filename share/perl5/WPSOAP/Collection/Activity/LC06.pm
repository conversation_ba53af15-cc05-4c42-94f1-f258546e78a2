package WPSOAP::Collection::Activity::LC06;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;

use WPSOAP;
use WPSOAP::Collection::System::EL06;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystemEL06} = WPSOAP::Collection::System::EL06->new(ART => $self->{ART});

	$self->{WPSOAP} = WPSOAP->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system_el06{shift->{CollSystemEL06}}

sub _wpsoap{shift->{WPSOAP}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
					,"customerType"	=> { isa => 'SCALAR', list => ["PNRR5G"] }
					,"siteId"		=> { isa => 'SCALAR' }
					,"investor"		=> { isa => 'SCALAR', list => ["Infratel"]}
					,"siteType"		=> { isa => 'SCALAR', list => ["Antenna 5G"] }
					,"centralId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"regionId"			=> { isa => 'SCALAR' }
					,"cityId"			=> { isa => 'SCALAR' }
					,"centralCLLI"		=> { isa => 'SCALAR' }
					,"central"			=> { isa => 'SCALAR' }
					,"FOL"				=> { isa => 'SCALAR' }
					,"primaryWorks"		=> { isa => 'SCALAR', list => ['Y','N']  }
					,"restorationWorks"	=> { isa => 'SCALAR', list => ['Y','N']  }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);

	if ($params{siteId} eq ''){
		$self->_art()->last_error(__x("Param {param} must be defined", param => 'siteId'));
		return undef;
	}
	
	for ('primaryWorks','restorationWorks'){
		if (defined $params{$_}){
			$params{$_} = $params{$_} eq 'Y' ? '1' : '0';
		} else {
			$params{$_} = '0';
		}
	}

	# verifico che non esista già un LC aperta
	my $cerca = $self->cerca(
		customerId => $params{customerId},
		contractId => $params{contractId},
		siteId		=> $params{siteId},
	);
	return undef unless defined $cerca;
	if (scalar @{$cerca}){
		$self->_art()->last_error(__x("Site already present: found activity with id {id}", id => $cerca->[0]->id()));
		return undef;
	}

	# Normalizzare regionId e cityId
    if (defined $params{regionId} || defined $params{cityId}){
        my $geo = $self->_wpsoap->get_geo_object;
        return unless $geo;

        # costruisco l'indirizzo in funzione dei parametri che ho in input
        my $address;
        $address = $params{cityId} if defined $params{cityId};
		if (defined $address && defined $params{regionId}){
			$address .= " ".$params{regionId};
		} else {
			# se non c'è il ciytId  c'è sicuramente regionId
			$address = $params{regionId};
		}

        my $location = $geo->query_by_address($address);

        my %map = (
            #country                     => 'country',
            administrative_area_level_1 => 'regionId',
            # administrative_area_level_2 => 'province',
            administrative_area_level_3 => 'cityId',
            # route                       => 'address',
            # street_number               => 'streetNumber',
            # postal_code                 => 'zipCode',
        );

        if (defined $location){
			$self->_logger->trace( Dumper({
                LOCATION => $location,
            }));
            if ($location->{partial_match}){
				$self->_art()->last_error("Unable to find info for region and/or city");
                $self->_logger()->error( $self->_art()->last_error() );
				return undef;
            }
            for my $comp ( @{$location->{address_components}} ) {
                for my $k ( keys %map ) {
                    if ( grep { $k eq $_ } @{$comp->{types}} ) {
                        $params{$map{$k}} = $comp->{short_name};
                    }
                }
            }
        } else {
            $self->_art()->last_error("Unable to find info for region and/or city");
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
        }
    }

	# verifico se esiste già un sistema per il sito
	my $el06s = $self->_get_coll_system_el06()->cerca(
		"customerId"	=> $params{"customerId"},
		"contractId"	=> $params{"contractId"},
		"siteId"		=> $params{siteId},
	);
	unless (defined $el06s){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}

	$self->get_db()->do( "savepoint WPSOAP_Cll_Act_LC06_crea" );
	
	my $sys_el06;

	unless (scalar @{$el06s}){
		my $crea_sys = {};

		for (
			"customerId"
			,"contractId"
			,"customerType"
			,"siteId"
			,"investor"
			,"siteType"
			,"centralId"
		){
			$crea_sys->{$_} = $params{$_};
		}

		for (
			"regionId"
			,"cityId"
			,"centralCLLI"
			,"central"
			,"FOL"
			,"primaryWorks"
			,"restorationWorks"
		){
			$crea_sys->{$_} = $params{$_} if defined $params{$_};
		}

		$sys_el06 = $self->_get_coll_system_el06()->crea(%$crea_sys);
		unless (defined $sys_el06){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_LC06_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
	} else {
		$sys_el06 = $el06s->[0];
	}
	
	my %create_params = (
		ACTIVITY_TYPE_NAME	=> 'LC06'
		, ID_CUSTOM			=> $self->_art()->get_activity_next_id()
		, DESCRIPTION		=> $params{"customerId"}."-".$params{"contractId"}."-".$params{"siteId"}
		, PROPERTIES		=> {}
		, SYSTEM_ID 		=> $sys_el06->id()
	);
	
	for (
		"customerId"
		,"contractId"
		,"customerType"
		,"siteId"
		,"investor"
		,"siteType"
		,"centralId"
	){
		$create_params{PROPERTIES}->{$_} = $params{$_};
	}

	for (
		"regionId"
		,"cityId"
		,"centralCLLI"
		,"central"
		,"FOL"
		,"primaryWorks"
		,"restorationWorks"
	){
		$create_params{PROPERTIES}->{$_} = $params{$_} if defined $params{$_};
	}
	
	my $lc06 = $self->_get_coll_activity()->create(
		%create_params
	);
	
	unless (defined $lc06){
		$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_LC06_crea" );
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}
	$self->_logger()->info(__x("Activity of type {type} created with id {id}", type => $lc06->info('ACTIVITY_TYPE_NAME'), id => $lc06->id()));

	return $lc06;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"contractId"				=> { isa => 'SCALAR' },
					"siteId"					=> { isa => 'SCALAR' },
					"__FIND_ALL__"				=> { isa => 'SCALAR', list => [1] },
					'siteConstructionNetwork'	=> { isa => 'SCALAR' },
					'siteOtherNetwork'			=> { isa => 'SCALAR' },
					'sitePrimaryNetwork'		=> { isa => 'SCALAR' },
					'siteRestorationNetwork'	=> { isa => 'SCALAR' },
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		 ACTIVITY_TYPE_NAME_EQUAL	=> 'LC06'
		,ACTIVITY_PROPERTIES_EQUAL	=> {
			customerId	=> $params{customerId}
		}
	};

	for ('contractId','siteId','siteConstructionNetwork','siteOtherNetwork','sitePrimaryNetwork','siteRestorationNetwork'){
		$searchParam->{ACTIVITY_PROPERTIES_EQUAL}->{$_} = $params{$_} if defined $params{$_};
	}
	unless($params{__FIND_ALL__}){
		$searchParam->{ACTIVE} = 1;
	}
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $sites = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $sites){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $sites;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOAP::Collection::Activity::LC06->new(ART => $art);
	
	my $sito = $coll->crea(
		"customerId"	=> 'TIM'
		,"contractId"	=> 'CREATION'
		,"customerType"	=> "PNRR5G"
		,"siteId"		=> "site002"
		,"investor"		=> "Infratel"
		,"siteType"		=> "Antenna 5G"
		,"centralId"	=> "53300F"
		#OPTIONAL
		,"regionId"			=> "LOMBARDIA"
		,"cityId"			=> "Bergamo"
		,"centralCLLI"		=> "CLLIc001"
		,"central"			=> "Bergamo Loreto"
		,"FOL"				=> "FOL001"
		,"primaryWorks"		=> 'Y'
		,"restorationWorks"	=> 'N'
	);
	
	if( defined $sito) {
		get_logger()->info("OK: ".$sito->id(). "(".$sito->get_current_status_name().")");
		get_logger()->info("activity_property: ".Dumper $sito->activity_property());
		get_logger()->info("system_property: ".Dumper $sito->system_property());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
