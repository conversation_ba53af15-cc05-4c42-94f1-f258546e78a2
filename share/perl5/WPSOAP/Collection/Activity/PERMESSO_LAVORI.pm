package WPSOAP::Collection::Activity::PERMESSO_LAVORI;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;

use WPSOAP::Collection::System::NETWORK;
use WPSOAP::Collection::System::PERMIT;
use WPSOAP::Profilo;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystemNetwork} = WPSOAP::Collection::System::NETWORK->new(ART => $self->{ART});
	
	$self->{CollSystemPermit} = WPSOAP::Collection::System::PERMIT->new(ART => $self->{ART});
	
	$self->{Profilo} = WPSOAP::Profilo->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system_network{shift->{CollSystemNetwork}}

sub _get_coll_system_permit{shift->{CollSystemPermit}}

sub _profilo{shift->{Profilo}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"publicPermitDescription"	=> { isa => 'SCALAR' }
					,"publicPermitCategory"		=> { isa => 'SCALAR', list => ["Realizzazione civile","Sottotubazione","Posa aerea"] }
					,"authority"				=> { isa => 'SCALAR', list => ["Comune","Provincia","ANAS","FF.SS.","Autostrade","Altro Ente"] }
				}
				,OPTIONAL	=> {
					"publicPermitType"	=> { isa => 'SCALAR', list => ["Scavo tradizionale","No-Dig","Minitrincea"] }
					,"reference"		=> { isa => 'HASH' }
					,"ATTACHMENTS"		=> { isa => 'ARRAY' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);

	$params{reference} = {} unless exists $params{reference};
	
	#verifico che l'utente abbia i permessi per aprire un'attività PERMESSO_LAVORI
	$self->_art(__x("User {username} can not open {type}", username => $self->_profilo()->nome(), type => 'PERMESSO_LAVORI'))
		&& return undef
			if (
				!$self->_profilo()->is_assistente_tecnico_civile()
				&&
				!$self->_profilo()->is_referente_permessi_ap()
				&&
				!$self->_profilo()->is_coordinatore_at_civile()
				&&
				!$self->_profilo()->is_assistente_tecnico()
			);
	
	my $result;
	
	my $permesso_lavori;
	
	if (defined $params{AP_LC}){ # gestione OF
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"AP_LC"				=> { isa => undef, inherits => [ 'API::ART::APP::Activity::AP_LC' ] }
						,"publicPermitType"	=> { isa => 'SCALAR' }
					}
					,OPTIONAL	=> {
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		
		my $ap_lc = $params{AP_LC};
		
		# se l'attività AP_LC non è nello stato ACQUISITA non è possibile aprire un permesso lavori
		$self->_art->last_error(__x("Permission Area {id} must be in the status {status}!", id => $ap_lc->id(), status => 'ACQUISITA'))
			&& return undef
				if $ap_lc->get_current_status_name() ne 'ACQUISITA';
		
		my $ancestor = $ap_lc->system()->ancestor();
		
		my %create_params = (
			ACTIVITY_TYPE_NAME	=> 'PERMESSO_LAVORI'
			, DESCRIPTION		=> $ancestor->property('customerId')."-".$ancestor->property('contractId')."-".$ancestor->property('projectId')."-".$ap_lc->activity_property('permitsAreaId')."-".$params{"publicPermitDescription"}
			, PROPERTIES		=> {
				 "permitsAreaId"			=> $ap_lc->activity_property("permitsAreaId")
				,"projectId"				=> $ap_lc->activity_property("projectId")
				,"publicPermitCategory"		=> $params{"publicPermitCategory"}
				,"authority"				=> $params{"authority"}
				,"publicPermitType"			=> $params{"publicPermitType"}
				,"publicPermitDescription"	=> $params{"publicPermitDescription"}
			}
			, SYSTEM_ID 		=> $ap_lc->system()->id()
		);
		
		$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
		
		$self->get_db()->do( "savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
		
		$permesso_lavori = $ap_lc->add_child(
			%create_params
		);
		
		unless (defined $permesso_lavori){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		my $ups = $ap_lc->update_permits_count();
		
		unless (defined $ups){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		$self->_logger()->info(__x("Activity of type {type} created with id {id}", type => $permesso_lavori->info('ACTIVITY_TYPE_NAME'), id => $permesso_lavori->id()));
		$result = $permesso_lavori;
	} else { # gestione FC
		$self->_art()->last_error($errmsg)
			and return undef
				unless $self->_art()->check_named_params(
					 ERRMSG		=> \$errmsg
					,PARAMS		=> \%params
					,MANDATORY	=> {
						"customerId"	=> { isa => 'SCALAR' }
						,"contractId"	=> { isa => 'SCALAR' }
						,"targetAsset"	=> { isa => 'SCALAR', list => ['Network', 'NetworkFibercop', 'ROE', 'PTE']}
					}
					,OPTIONAL	=> {
						"expectedAuthorizationDate"	=> { isa => 'SCALAR' },
						"expectedEndAuthorizationDate"	=> { isa => 'SCALAR' },
					}
					,IGNORE_EXTRA_PARAMS => 1
		);
		
		if (!defined $params{"assetId"}){
			$self->_art()->last_error(__x("Missing mandatory param {param}", param => 'assetId'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		if ($params{targetAsset} =~/^(Network|NetworkFibercop)$/ && ref ($params{"assetId"}) eq 'ARRAY'){
			$self->_art()->last_error(__x("For {param} {value} {param1} can have only one value", param => 'targetAsset', value => $params{targetAsset}, param1 => 'assetId'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		if ($params{targetAsset} =~ /^(ROE|PTE)$/ && ref ($params{"assetId"}) ne 'ARRAY'){
			$self->_art()->last_error(__x("For {param} {value} {param1} must be an array", param => 'targetAsset', value => $params{targetAsset}, param1 => 'assetId'));
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}

		if ($params{targetAsset} =~/^(Network|NetworkFibercop)$/){
			
			my %create_params = (
				ACTIVITY_TYPE_NAME	=> 'PERMESSO_LAVORI'
				, ID_CUSTOM			=> $self->_art()->get_activity_next_id()
				, DESCRIPTION		=> $params{"customerId"}."-".$params{"contractId"}."-".$params{assetId}."-".$params{"publicPermitDescription"}
				, PROPERTIES		=> {
					"projectId"					=> $params{assetId}
					,"publicPermitCategory"		=> $params{"publicPermitCategory"}
					,"authority"				=> $params{"authority"}
					,"publicPermitDescription"	=> $params{"publicPermitDescription"}
					,"targetAsset"				=> $params{targetAsset}
				}
			);

			if ($params{targetAsset} =~/^(NetworkFibercop)$/){
				$self->_art()->last_error($errmsg)
					and return undef
						unless $self->_art()->check_named_params(
							ERRMSG		=> \$errmsg
							,PARAMS		=> \%params
							,MANDATORY	=> {
								"maker"	=> { isa => 'SCALAR', list => ['Team', 'Subcontract'] },
								"expectedAuthorizationDate"	=> { isa => 'SCALAR' },
								"expectedEndAuthorizationDate"	=> { isa => 'SCALAR' },
							}
							,IGNORE_EXTRA_PARAMS => 1
				);
				if (defined $params{"expectedAuthorizationDate"} && $params{"expectedEndAuthorizationDate"}){
					if ($self->_art()->get_date_from_iso_date($params{"expectedAuthorizationDate"}) > $self->_art()->get_date_from_iso_date($params{"expectedEndAuthorizationDate"})){
						$self->_art()->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => 'expectedEndAuthorizationDate', date2 => 'expectedAuthorizationDate'));
						return undef;
					}
				}

				if ($params{maker} eq 'Team'){
					$self->_art()->last_error($errmsg)
						and return undef
							unless $self->_art()->check_named_params(
								ERRMSG         => \$errmsg
								,PARAMS         => \%params
								,MANDATORY      => {
										"team"        => { isa => 'HASH' }
								}
								,OPTIONAL       => {}
								,IGNORE_EXTRA_PARAMS => 1
					);

					$self->_art()->last_error($errmsg)
						and return undef
							unless $self->_art()->check_named_params(
								ERRMSG         => \$errmsg
								,PARAMS         => \%{$params{team}}
								,MANDATORY      => {
										"teamId"        => { isa => 'SCALAR' }
										,"teamName"     => { isa => 'SCALAR' }
								}
								,OPTIONAL       => {}
								,IGNORE_EXTRA_PARAMS => 1
					);
					$create_params{PROPERTIES}->{maker} = $params{maker};
					$create_params{PROPERTIES}->{teamId} = $params{team}->{teamId};
					$create_params{PROPERTIES}->{teamName} = $params{team}->{teamName};
				} else {
					$self->_art()->last_error($errmsg)
					and return undef
						unless $self->_art()->check_named_params(
							ERRMSG         => \$errmsg
							,PARAMS         => \%params
							,MANDATORY      => {
								"subcontractInfo"	=> { isa => 'HASH' }
							}
							,OPTIONAL       => {}
							,IGNORE_EXTRA_PARAMS => 1
					);

					$self->_art()->last_error($errmsg)
						and return undef
							unless $self->_art()->check_named_params(
								ERRMSG         => \$errmsg
								,PARAMS         => \%{$params{"subcontractInfo"}}
								,MANDATORY      => {
									"subContractName"      => { isa => 'SCALAR' }
									,"subContractCode"      => { isa => 'SCALAR' }
								}
								,OPTIONAL       => {}
								,IGNORE_EXTRA_PARAMS => 1
					);

					$create_params{PROPERTIES}->{maker} = $params{maker};
					$create_params{PROPERTIES}->{subContractName} = $params{"subcontractInfo"}->{subContractName};
					$create_params{PROPERTIES}->{subContractCode} = $params{"subcontractInfo"}->{subContractCode};
				}

			}

			# recupero la network associata
			my $networks = $self->_get_coll_system_network()->cerca(
				"customerId"	=> $params{"customerId"},
				"contractId"	=> $params{"contractId"},
				"networkId"		=> $params{assetId},
			);
			unless (defined $networks){
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			unless (scalar @{$networks}){
				$self->_art()->last_error(__x("No network found for customer {customerId}, contract {contractId} and networkId {networkId}", customerId => $params{"customerId"}, contractId => $params{"contractId"}, networkId => $params{assetId}));
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			# se la trovo ne trovo solo una per costruzione
			$create_params{SYSTEM_ID} = $networks->[0]->id();

			for my $ref (keys %{$params{reference}}){
				$create_params{PROPERTIES}->{$ref} = $params{reference}->{$ref};
			}
			for ('expectedAuthorizationDate','expectedEndAuthorizationDate'){
				$create_params{PROPERTIES}->{$_} = $params{$_} if defined $params{$_};
			}
			
			$create_params{PROPERTIES}->{publicPermitType} = $params{"publicPermitType"} if defined $params{"publicPermitType"};
			
			$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
			
			if (exists $create_params{ATTACHMENTS}){
				my $meta = {
					activityId => $create_params{ID_CUSTOM},
					activityType => $create_params{ACTIVITY_TYPE_NAME},
				};
				$meta->{networkId} = $create_params{PROPERTIES}->{projectId} if exists $create_params{PROPERTIES}->{projectId} && defined $create_params{PROPERTIES}->{projectId};
				
				for my $attach (@{$create_params{ATTACHMENTS}}){
					unless (ref($attach)){
						$attach = {
							FILENAME => $attach
						};
					}
					$attach->{META} = $meta;
				}
			}
			
			$self->get_db()->do( "savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
			
			$permesso_lavori = $self->_get_coll_activity()->create(
				%create_params
			);

			# aggiungo l'eventuale gruppo del subappalto
			if (defined $params{subcontractInfo}->{subContractCode}){
				my $serviceGroupName = 'SERVICE_'.$params{subcontractInfo}->{subContractCode};
				# se il gruppo esiste già sull'istanza lo aggiungo alla visibilità della network
				if ($self->_art()->test_group_name($serviceGroupName)){
					$networks->[0]->set_groups($serviceGroupName);
				}
			}
			
			unless (defined $permesso_lavori){
				$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
				$self->_logger()->error( $self->_art()->last_error() );
				return undef;
			}
			
			$self->_logger()->info(__x("Activity of type {type} created with id {id}", type => $permesso_lavori->info('ACTIVITY_TYPE_NAME'), id => $permesso_lavori->id()));
			$result = $permesso_lavori;
		} elsif ($params{targetAsset} =~ /^(ROE|PTE)$/){
			$result = [];
			$self->_art()->last_error($errmsg)
				and return undef
					unless $self->_art()->check_named_params(
						 ERRMSG		=> \$errmsg
						,PARAMS		=> \%params
						,MANDATORY	=> {
							"workingGroupCode"	=> { isa => 'SCALAR' },
							"oneForAll"			=> { isa => 'SCALAR', list => [0,1] },
						}
						,OPTIONAL	=> {
							"networkId"	=> { isa => 'SCALAR' },
						}
						,IGNORE_EXTRA_PARAMS => 1
			);
			
			# verifico che tutti gli id passati siano effettivamente solo numerici
			for my $as (@{$params{assetId}}){
				$self->_art()->last_error(__x("assetId must be an integer"))
					&& return undef
						if $as !~ /^\d+$/;
			}
			
			# recupero la network associata se definito
			if (defined $params{networkId}){
				my $networks = $self->_get_coll_system_network()->cerca(
					"customerId"	=> $params{"customerId"},
					"contractId"	=> $params{"contractId"},
					"networkId"		=> $params{networkId},
				);
				unless (defined $networks){
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
				
				unless (scalar @{$networks}){
					$self->_art()->last_error(__x("No network found for customer {customerId}, contract {contractId} and networkId {networkId}", customerId => $params{"customerId"}, contractId => $params{"contractId"}, networkId => $params{assetId}));
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
			}
			
			my $runs;
			if ($params{oneForAll}){
				$runs = 1;
			} else {
				$runs = scalar @{$params{assetId}};
				delete $params{"ATTACHMENTS"} if $runs > 1;
			}
			
			$self->get_db()->do( "savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
			
			for (my $i = 0; $i<$runs; $i++){
				
				# viene creato un nuovo sistema PERMIT
				my $sys_permit = $self->_get_coll_system_permit()->crea(
					"customerId"		=> $params{"customerId"},
					"contractId"		=> $params{"contractId"},
					"workingGroupCode"	=> $params{"workingGroupCode"},
					"assetId"			=> $params{"oneForAll"} ? $params{"assetId"} : [$params{"assetId"}->[$i]],
					"targetAsset"		=> $params{"targetAsset"}
				);
				unless (defined $sys_permit){
					$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
				
				my %create_params = (
					ACTIVITY_TYPE_NAME	=> 'PERMESSO_LAVORI'
					, ID_CUSTOM			=> $self->_art()->get_activity_next_id()
					, DESCRIPTION		=> $params{"customerId"}."-".$params{"contractId"}."-".$params{"workingGroupCode"}."-".$params{"publicPermitDescription"}
					, PROPERTIES		=> {
						"publicPermitCategory"		=> $params{"publicPermitCategory"}
						,"authority"				=> $params{"authority"}
						,"publicPermitDescription"	=> $params{"publicPermitDescription"}
						,"targetAsset"				=> $params{"targetAsset"}
					}
					, SYSTEM_ID 		=> $sys_permit->id()
				);
				
				$create_params{PROPERTIES}->{projectId} = $params{networkId} if defined $params{networkId};
				$create_params{PROPERTIES}->{publicPermitType} = $params{publicPermitType} if defined $params{publicPermitType};
				
				$create_params{ATTACHMENTS} = $params{"ATTACHMENTS"} if defined $params{"ATTACHMENTS"};
				
				if (exists $create_params{ATTACHMENTS}){
					my $meta = {
						activityId => $create_params{ID_CUSTOM},
						activityType => $create_params{ACTIVITY_TYPE_NAME},
					};
					$meta->{networkId} = $create_params{PROPERTIES}->{projectId} if exists $create_params{PROPERTIES}->{projectId} && defined $create_params{PROPERTIES}->{projectId};
					for my $asset (@{$params{assetId}}){
						$meta->{"assetId-".$asset} = 1;
					}
					
					for my $attach (@{$create_params{ATTACHMENTS}}){
						unless (ref($attach)){
							$attach = {
								FILENAME => $attach
							};
						}
						$attach->{META} = $meta;
					}
				}
				
				$permesso_lavori = $self->_get_coll_activity()->create(
					%create_params
				);
				
				unless (defined $permesso_lavori){
					$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_PRM_LVR_crea" );
					$self->_logger()->error( $self->_art()->last_error() );
					return undef;
				}
				$self->_logger()->info(__x("Activity of type {type} created with id {id} and assetId => {assetId}", type => $permesso_lavori->info('ACTIVITY_TYPE_NAME'), id => $permesso_lavori->id(), assetId => join (',', @{$permesso_lavori->system_property('assetId')})));
				if ($params{oneForAll}){
					$result = $permesso_lavori;
				} else {
					push @{$result}, $permesso_lavori;
				}
			}
		}
	}
	
	return $result;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"		=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					"contractId"		=> { isa => 'SCALAR' }
					,"projectId"		=> { isa => 'SCALAR' }
					,"targetAsset"		=> { isa => 'ARRAY', min => 1 }
					,"ref00"			=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		 ACTIVITY_TYPE_NAME_EQUAL	=> 'PERMESSO_LAVORI'
		,SYSTEM_PROPERTIES_EQUAL	=> {
			customerId		=> $params{customerId},
		}
	};

	for my $k ("contractId") {
		$searchParam->{SYSTEM_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}
	
	for my $k ("targetAsset") {
		$searchParam->{ACTIVITY_PROPERTIES_IN}->{$k} = $params{$k} if defined $params{$k};
	}

	for my $k ("ref00", "projectId") {
		$searchParam->{ACTIVITY_PROPERTIES_EQUAL}->{$k} = $params{$k} if defined $params{$k};
	}
	
	#cerco il sistema progetto e verifico se su quel progetto c'è l'area permessi
	my $permits = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $permits){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $permits;
}

if (__FILE__ eq $0) {

	use API::ART;
	eval "use API::ART::APP::Activity::AP_LC";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $ap_lc = API::ART::APP::Activity::AP_LC->new(ART => $art, ID => 10175);
	
	if( defined $ap_lc) {
		get_logger()->info("OK: ".$ap_lc->id());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	my $coll = WPSOAP::Collection::Activity::PERMESSO_LAVORI->new(ART => $art);
	
	my $permesso = $coll->crea(
          'targetAsset' => 'NetworkFibercop',
          'networkId' => '000500651379',
          'publicPermitCategory' => 'Realizzazione civile',
          'workingGroupCode' => 106656,
          'maker' => 'Subcontract',
          'ATTACHMENTS' => [],
          'reference' => {
                           'ref01' => '62781',
                           'ref00' => '74401F'
                         },
          'assetId' => '000500651379',
          'oneForAll' => 0,
          'publicPermitType' => 'Scavo tradizionale',
          'expectedEndAuthorizationDate' => '2021-03-26T10:00:00.000000000+02:00',
          'subcontractInfo' => {
                                 'subContractCode' => '113532',
                                 'subContractName' => 'ARCIPELAGO SCARL'
                               },
          'contractId' => 'FTTH',
          'expectedAuthorizationDate' => '2021-03-26T02:00:00.000000000+02:00',
          'customerId' => 'TIM',
          'type' => 'public',
          'publicPermitDescription' => 'test Fibercop',
          'authority' => 'Comune'
	);
	
	if( defined $permesso) {
		get_logger()->info("OK: ".$permesso->id(). "(".$permesso->get_current_status_name().")");
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	#print STDERR Dumper $ap_lc->get_totals_ap();
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
