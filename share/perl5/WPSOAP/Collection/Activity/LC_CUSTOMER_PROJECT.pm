package WPSOAP::Collection::Activity::LC_CUSTOMER_PROJECT;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use API::ART::Collection::Activity;

use WPSOAP;
use WPSOAP::Collection::System::CUSTOMER_PROJECT;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});
	
	$self->{CollSystem} = WPSOAP::Collection::System::CUSTOMER_PROJECT->new(ART => $self->{ART});
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_system{shift->{CollSystem}}

sub _wpsoap{shift->{WPSOAP}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub crea{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"		=> { isa => 'SCALAR' },
					"projectCode"		=> { isa => 'SCALAR' },
					"central"			=> { isa => 'SCALAR' },
					"assignmentDate"	=> { isa => 'SCALAR' },
					"projectTypology"	=> { isa => 'SCALAR', list => ['Anticipato', 'Inoltrato'] },
					"ATTACHMENTS"		=> { isa => 'ARRAY' }
				}
				,OPTIONAL	=> {
					"networkIds"	=> { isa => 'ARRAY' },
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	$params{RO} = substr($params{"projectCode"}, 0,2);
	# recupero workingGroupCodeList a partire dal RO
	$params{workingGroupCodeList} = $self->_wpsoap()->get_cdl_by_RO_central(RO => $params{RO});
	unless (defined $params{workingGroupCodeList}){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;
	}

	my $lc;
	# check per verificare che LC_CUSTOMER_PROJECT esista già
	my $cerca = $self->cerca(
		"customerId"			=> $params{"customerId"},
		"projectCode"			=> $params{"projectCode"},
	);
	return undef unless defined $cerca;

	$self->get_db()->do( "savepoint WPSOAP_Cll_Act_LC_CM_PROJ_crea" );
	if (scalar @{$cerca}){ # se la trovo mi aggancio ed eseguo azione
		$lc = $cerca->[0];
		my $lc_params = {
			PROPERTIES => {
				"central"			=> $params{"central"},
				"assignmentDate"	=> $params{"assignmentDate"},
				"projectTypology"	=> $params{"projectTypology"},
			},
			ATTACHMENTS => $params{ATTACHMENTS},
		};
		$lc_params->{PROPERTIES}->{networkIds} = $params{networkIds}
			if exists $params{networkIds};
		if ($lc->get_current_status_name eq 'ATTESA_DOCUMENTAZIONE') {
			# eseguo solo loop di tracciamento in quanto c'è già una richiesta in corso e
			# sarebbe inutile farne un'altra perchè darebbe lo stesso risultato
			$lc_params->{ACTION} = 'RICEZIONE_NOTIFICA';
			$lc_params->{DESCRIPTION} = 'Ricezione notifica su ricerca in corso';
		} else {
			# eseguo nuova richiesta di documentazione per vedere se ci sono altri documenti
			$lc_params->{ACTION} = 'RICHIESTA_DOCUMENTAZIONE';
			$lc_params->{DESCRIPTION} = 'Nuova richiesta documentazione';
		}

		unless($lc->step(%$lc_params)){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_LC_CM_PROJ_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		$self->_logger()->info(__x("Activity of type {type} with id {id} updated (projectCode => {projectCode}, status => {status}", type => $lc->info('ACTIVITY_TYPE_NAME'), id => $lc->id(), projectCode => $params{projectCode}, status => $lc->get_current_status_name()));
	} else {

		# viene creato un nuovo sistema
		my $sys = $self->_get_coll_system()->crea(
			"customerId"			=> $params{"customerId"},
			"projectCode"			=> $params{"projectCode"},
			"networkIds"			=> $params{"networkIds"},
			"RO"					=> $params{"RO"},
			"workingGroupCodeList"	=> $params{"workingGroupCodeList"},
			"central"				=> $params{"central"},
			"assignmentDate"		=> $params{"assignmentDate"},
			"projectTypology"		=> $params{"projectTypology"},
		);
		unless (defined $sys){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_LC_CM_PROJ_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		
		my %create_params = (
			ACTIVITY_TYPE_NAME	=> 'LC_CUSTOMER_PROJECT'
			, DESCRIPTION		=> $params{"customerId"}."-".$params{"projectCode"}
			, PROPERTIES		=> {
				"customerId"		=> $params{"customerId"},
				"projectCode"		=> $params{"projectCode"},
				"RO"				=> $params{"RO"},
				"central"			=> $params{"central"},
				"assignmentDate"	=> $params{"assignmentDate"},
				"projectTypology"	=> $params{"projectTypology"},
			}
			, SYSTEM_ID 		=> $sys->id()
			, ATTACHMENTS		=> $params{"ATTACHMENTS"}
		);
		
		my $meta = {
			activityId => $create_params{ID_CUSTOM},
			activityType => $create_params{ACTIVITY_TYPE_NAME},
		};
		$meta->{projectCode} = $create_params{PROPERTIES}->{projectCode};
		
		for my $attach (@{$create_params{ATTACHMENTS}}){
			unless (ref($attach)){
				$attach = {
					FILENAME => $attach
				};
			}
			$attach->{META} = $meta;
		}
		
		$lc = $self->_get_coll_activity()->create(
			%create_params
		);
		
		unless (defined $lc){
			$self->get_db()->do( "rollback to savepoint WPSOAP_Cll_Act_LC_CM_PROJ_crea" );
			$self->_logger()->error( $self->_art()->last_error() );
			return undef;
		}
		$self->_logger()->info(__x("Activity of type {type} created with id {id} and projectCode => {projectCode}", type => $lc->info('ACTIVITY_TYPE_NAME'), id => $lc->id(), projectCode => $params{projectCode}));
	}

	return $lc;
}

sub cerca{

	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"		=> { isa => 'SCALAR' }
					,"projectCode"		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		 ACTIVITY_TYPE_NAME_EQUAL	=> 'LC_CUSTOMER_PROJECT'
		,ACTIVITY_PROPERTIES_EQUAL	=> {
			customerId		=> $params{customerId},
			projectCode		=> $params{projectCode}
		}
	};
	
	#cerco se esiste già un'attività di questo tipo per il codice progetto
	my $lcs = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $lcs){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $lcs;
}

if (__FILE__ eq $0) {

	use API::ART;
	eval "use API::ART::APP::Activity::AP_LC";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $coll = WPSOAP::Collection::Activity::LC_CUSTOMER_PROJECT->new(ART => $art);
	
	my $lc = $coll->crea(
          'customerId' => 'TIM',
		  'projectCode' => 'SUSIETEPT21W00488855',
		  'assignmentDate' => '2020-09-09T00:00:00.000000000+02:00',
		  'central' => 'Bergamo-Alta',
		  'projectTypology' => 'Anticipato',
		  'networkIds' => [
			  '000500651379',
			  '000500651380'
			],
		  'ATTACHMENTS' => [
			  {
			  	FILENAME => '/dev/null',
				DOC_TYPE => 'SMIN',
			  }
		  ]
	);

	if( defined $lc) {
		get_logger()->info("OK: ".$lc->id(). "(".$lc->get_current_status_name().")");
		get_logger()->info(Dumper $lc->activity_property());
		get_logger()->info(Dumper $lc->system_property());
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	#print STDERR Dumper $ap_lc->get_totals_ap();
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
