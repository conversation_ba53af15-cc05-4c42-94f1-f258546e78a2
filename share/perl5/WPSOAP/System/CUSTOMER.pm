package WPSOAP::System::CUSTOMER;

use strict;
use warnings;

use Carp;  # qw/verbose croak/;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::System';

our $DEFAULT_CHILDREN_CLASS = "WPSOAP::System::CONTRACT";

sub _db { shift->art()->_dbh() }

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(%{$params});
	croak unless defined $self;
	
	croak 'Bad system type' if $self->type_name() ne 'CUSTOMER';

	$self->{ART} = $params->{ART};
	
	return $self;
}

sub art {
	shift->{ART};
}

sub _logger {
	my $self = shift;
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ ) unless defined $self->{LOGGER};
	
	return $self->{LOGGER};
}

sub _wpsoap {
	my $self = shift;
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art()) unless defined $self->{WPSOAP};
	
	return $self->{WPSOAP};
}

sub add_context {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 context		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $groupToAdd = $self->_wpsoap()->get_ref_group_by_context(context => $params{context});
	return undef unless defined $groupToAdd;
	
	unless ($self->set_groups($groupToAdd)){
		$self->art()->last_error( "Impossibile aggiungere il gruppo al sistema: ". $self->art()->last_error());
		return undef;
	}
	
	return 1;
}

if (__FILE__ eq $0) {

	use API::ART;
	eval "use WPSOAP::Collection::System::CUSTOMER;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

=pod	
	my $coll = WPSOAP::Collection::System::CITY->new(ART => $art);
	
	my $project = $coll->cerca(contractId => "FTTH", customerId => 'ENEL');
	
	unless (defined $project) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($project->[0]));
		#die;
	}
	
	$project = eval { WPSOAP::System::PROJECT->new(ART => $art, ID => 2289); };
	
	unless (defined $project || $@) {
		get_logger()->error("Error: " . $art->last_error() || $@);
		die;
	} else {
		print STDERR Dumper $project->property();
		get_logger()->info("OK: " . ref($project));
	}
	
#	$art->save();

=cut

}

1;
