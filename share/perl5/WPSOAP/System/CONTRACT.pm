package WPSOAP::System::CONTRACT;

use strict;
use warnings;

use Carp;  # qw/verbose croak/;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::System';

our $DEFAULT_PARENT_CLASS = "WPSOAP::System::CUSTOMER";

sub _db { shift->art()->_dbh() }

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(%{$params});
	croak unless defined $self;
		
	$self->{ART} = $params->{ART};
	
	return $self;
}

sub art {
	shift->{ART};
}

sub _logger {
	my $self = shift;
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ ) unless defined $self->{LOGGER};
	
	return $self->{LOGGER};
}

sub _wpsoap {
	my $self = shift;
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art()) unless defined $self->{WPSOAP};
	
	return $self->{WPSOAP};
}

sub get_manageable_team {
	my $self = shift;
	my $api = $self->art();
	
	#recupero tutti i profili
	my $profili = $api->enum_role(EXTENDED_OUTPUT => 1);
	return undef unless defined $profili;
	
	my $ret = [];
	#per ogni ruolo recupero i gruppi e cerco gli utenti associati
	for my $p (@{$profili}){
		my @groups = map {$api->get_group_name($_)} @{$p->{GROUPS}};
		my $users = $api->enum_user(EXTENDED_OUTPUT => 1,FILTER => {GROUPS => \@groups});
		return undef unless $users;
		
		push @{$ret}, {
			ID => $p->{ID},
			NAME => $p->{NAME},
			DESCRIPTION => $p->{DESCRIPTION},
			USERS => $users
		}
	}
	
	return $ret;
}

sub update_contract {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->art()->last_error($errmsg)
		&& return undef
			unless $self->art()->check_named_params(
				 ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					 PROPERTIES	=> { isa => 'HASH' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	unless ($self->set_property(PROPERTIES => $params{PROPERTIES})){
		$self->art()->last_error(__x("Unable to update system: {error}", error => $self->art()->last_error()));
		return undef;
	} 
	
	my $groupParent = $self->parent()->info('GROUPS');
	return undef unless defined $groupParent;
	
	my $groups = $self->info('GROUPS');
	
	push @{$groups}, @{$groupParent};
	
	# tolgo il gruppo ROOT in quanto non può mai essere gestito dalle API
	my @groupsToAdd = grep {$_ ne 'ROOT'} @{$groups};
	
	unless ($self->set_groups(@groupsToAdd)){
		$self->art()->last_error( __x("Unable to set groups: {error}", error => $self->art()->last_error()));
		return undef;
	}
	
	return 1;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $contract = eval { WPSOAP::System::CONTRACT->new(ART => $art, ID => 2237); };
	
	unless (defined $contract || $@) {
		get_logger()->error("Error: " . $art->last_error() || $@);
		die;
	} else {
		print STDERR Dumper $contract->get_manageable_team();
		get_logger()->info("OK: " . ref($contract));
	}
	
#	$art->save();

}

1;
