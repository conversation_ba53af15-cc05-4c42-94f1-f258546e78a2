package WPSOAP::System::AP;

use strict;
use warnings;

use Carp;  # qw/verbose croak/;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::System';

sub _db { shift->art()->_dbh() }

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(%{$params});
	croak unless defined $self;
	
	$self->{ART} = $params->{ART};
	
	return $self;
}

sub art {
	shift->{ART};
}

sub _logger {
	my $self = shift;
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ ) unless defined $self->{LOGGER};
	
	return $self->{LOGGER};
}

sub _wpsoap {
	my $self = shift;
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art()) unless defined $self->{WPSOAP};
	
	return $self->{WPSOAP};
}

if (__FILE__ eq $0) {

	use API::ART;
	eval "use WPSOAP::Collection::System::AP;";
	eval "use WPSOAP::System::PROJECT;";
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $project = WPSOAP::System::PROJECT->new(ART => $art, ID => 2309);
	
	my $coll = WPSOAP::Collection::System::AP->new(ART => $art, PROJECT => $project);
	
	my $ap = $coll->cerca(permitsAreaId => "Il Riz");
	
	unless (defined $ap) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($ap->[0]));
		#die;
	}
	
	$ap = WPSOAP::System::AP->new(ART => $art, ID => 2313);
	
	unless (defined $ap) {
		get_logger()->error("Error: " . $art->last_error());
		die;
	} else {
		get_logger()->info("OK: " . ref($ap));
	}
	
#	$art->save();
}

1;
