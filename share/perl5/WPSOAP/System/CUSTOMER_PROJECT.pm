package WPSOAP::System::CUSTOMER_PROJECT;

use strict;
use warnings;

use Carp;  # qw/verbose croak/;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOAP;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use base 'API::ART::System';

sub _db { shift->art()->_dbh() }

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self = $self->SUPER::new(%{$params});
	croak unless defined $self;
		
	$self->{ART} = $params->{ART};
	
	return $self;
}

sub art {
	shift->{ART};
}

sub _logger {
	my $self = shift;
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOCORE::LIB::' . __PACKAGE__ ) unless defined $self->{LOGGER};
	
	return $self->{LOGGER};
}

sub _wpsoap {
	my $self = shift;
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art()) unless defined $self->{WPSOAP};
	
	return $self->{WPSOAP};
}

# effettuiamo ovverride metodo set_property delle API::ART per fare in modo che vengano gestite solo le property configurate
sub set_property	{
	
	my $self = shift;
	my %params = @_;
	
	# di tutti i parametri passati gestico solo quelli che sono associati al TIPO_SISTEMA per evitare di dover cablare
	# tutti i dati_tecnici
	my $props = {
		DATE => $params{DATE}||$self->_db()->get_sysdate(),
		DATE_FORMAT => $params{DATE_FORMAT}||$self->art()->get_default_date_format()
	};
	for my $p (@{$self->info('PROPERTIES')}){
		$props->{PROPERTIES}->{$p} = $params{PROPERTIES}->{$p} if $params{PROPERTIES}->{$p};
	}
	
	# Chiamata SUPER della set_property
	
	my $r = $self->SUPER::set_property(%{$props});
	
	return undef unless $r;
	
	return 1;
	
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $obj = eval { WPSOAP::System::CUSTOMER_PROJECT->new(ART => $art, ID => $ARGV[0]); };
	
	if (!defined $obj || $@) {
		get_logger()->error("Error: " . $art->last_error() || $@);
		die;
	} else {
		get_logger()->info(Dumper $obj->property());
		get_logger()->info("OK: " . ref($obj));
	}
	
}

1;
