package WPSOAP::MQ::Consumer::PERMESSO_LAVORI::UpdateStatoAreaPermessi;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use WPSOAP;
use API::ART::APP::Activity::PERMESSO_LAVORI;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'UPDATE_STATO_AREA_PERMESSI'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $activity = API::ART::APP::Activity::PERMESSO_LAVORI->new(ART => $art, ID => $event->get_source_ref());

	unless (defined $activity){
		$message = __x("Unable to retrieve activity: {error}", error => $self->art()->last_error());
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}
	
	if ($activity->activity_property('permitsAreaId') ne $event->get_data()->{permitsAreaId}){
		$message = __x("Mismatch bewteen permit {permitsId} and permitsAreaIdy {permitsAreaId}", permitsId => $event->get_source_ref(), permitsAreaId => $event->get_data()->{permitsAreaId});
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}
	
	my $ap_lc = $activity->parent();
	my $ancestor = $activity->system()->ancestor();
	my $ancestor_property = $ancestor->property();
	
	my $sinfo = $self->{WPSOAP}->invoke_sinfo_rest(
		METHOD => 'PUT'
		, RESOURCE => sprintf($ENV{SINFO_PUT_AREA_PERMESSI_MASK_RESOURCE}, $ancestor_property->{'customerId'}, $ancestor_property->{'contractId'}, $ancestor_property->{'projectId'}, $ap_lc->system_property('permitsAreaId'))
		, BODY => $ap_lc->get_totals_ap()
	);
	unless (defined $sinfo){
		$message = __x("Error calling WS Sinfo: {error}", error => $self->art()->last_error());
		$self->logger()->error( $message );
		return $self->skip(REASON => $message);
	}
	
	$message = __x("Update permits area {permitsAreaId} for permit {permitId} done!", permitsAreaId => $event->get_data()->{permitsAreaId}, permitId => $activity->id());
	$self->logger()->info($message);
	return $self->consume( FETCH_RESULT => 1, STATUS => 0, TARGET_REF => $activity->id(), REASON => $message );
}

sub finish
{

}
1;

