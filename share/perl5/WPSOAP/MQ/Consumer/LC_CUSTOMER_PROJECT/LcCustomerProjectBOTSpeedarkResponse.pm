package WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectBOTSpeedarkResponse;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use API::ART::APP::Activity::LC_CUSTOMER_PROJECT;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
    return [
        'RESPONSE_DOCUMENTATION',
    ];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
    return [];
}

sub init
{
    my $self = shift;
    my $db   = $self->db;

    return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event {

    my $self = shift;
    my %params = @_;
    my $event   = $params{EVENT};
    my $art     = $self->art();

    $self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );

    my $message;

    my $shared_resources = $art->shared_resources();
    unless (defined $shared_resources){
        $message = __x("Error retrieving shared_resources object: {error}", error => $art->last_error);
        $self->logger->error($message);
        return $self->skip(REASON => $message);
    }

    my $data = $event->get_data;

    my $activity_id = $event->get_source_ref();

    my $activity = API::ART::APP::Activity::LC_CUSTOMER_PROJECT->new( ART => $art , ID => $activity_id);

    unless ($activity){
        $message = __x("Error retrieving activity {id}: {error}", id => $activity_id, error => $art->last_error);
        $self->logger->error($message);
        return $self->skip(REASON => $message);;
    }

    my $resources = [];    
    my $status = 0;
    MAIN: {

        my $attachments = $data->{FILES};
        my $attachs = [];

        for my $at (@{$attachments}){
            my $structure = from_json($at);
            push @{$resources}, $structure->{ID};
            my $fh = $shared_resources->get(RESOURCE_ID => $structure->{ID});
            unless (defined $fh){
                $message = __x("Error retrieving shared resource file {id}: {error}", id => $structure->{ID}, error => $shared_resources->last_error);
                $self->logger->error($message);
                return $self->skip(REASON => $message);
            }
            # apro file temporaneo in $ART_REPOSITORY_TMP
            my $tmp_file = $ENV{ART_REPOSITORY_TMP}.'/'.$structure->{NOME_FILE};
            my $res_attachs = {
                FILENAME => $tmp_file
            };
            if ($structure->{TIPO_ALLEGATO} eq 'DISTINTA_MATERIALI'){
                $res_attachs->{DOC_TYPE} = 'SDIS';
            } elsif ($structure->{TIPO_ALLEGATO} eq 'DOCUMENTAZIONE'){
                $res_attachs->{DOC_TYPE} = 'SDOC';
            } else {
                $message = __x(q{ACK OK for activity {id} error: invalid param TIPO_ALLEGATO}, id => $activity->id);
                $self->logger->error($message);
                $status = 2;
                last MAIN;
            }
            unless(open(FH, '>', $tmp_file)){
                $message = __x("Error opening tmpfile for writing file {file}: {error}", file => $tmp_file, error => $!);
                $self->logger->error($message);
                return $self->skip(REASON => $message);;
                close $fh;
            }
            while (<$fh>){
                print FH $_;
            }
            close FH;
            close $fh;
            push @{$attachs}, $res_attachs
        }
        unless ($activity->can_do_action(NAME => 'DOCUMENTAZIONE_RECUPERATA')){
            $message = __x("Activity with id {id} already managed", id => $activity->id());
            $self->logger()->info($message);
            $status = 1;
            last MAIN;
        }
        unless ( $activity->step(
                ACTION => 'DOCUMENTAZIONE_RECUPERATA',
                ATTACHMENTS => $attachs,
            )
        ) {
            $message = $art->last_error();
            $self->logger->error($art->last_error);
            return $self->skip(REASON => $message);;
        }
    }
    for my $r (@{$resources}){
        unless (defined $shared_resources->delete(RESOURCE_ID => $r)){
            $message = __x("Error deleting shared resource file {id}: {error}", id => $r, error => $art->last_error);
            $self->logger->error($message);
            return $self->skip(REASON => $message);;
        }
    }
    $message = __x(q{Activity {id} correctly managed}, id => $activity->id);
    $self->logger->info($message);
    return $self->consume( STATUS => $status, TARGET_REF => $activity_id, REASON => $message);
}

sub finish
{

}
1;
