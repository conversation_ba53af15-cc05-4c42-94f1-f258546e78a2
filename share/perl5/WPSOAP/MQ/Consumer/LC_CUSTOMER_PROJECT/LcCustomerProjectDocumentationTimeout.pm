package WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectDocumentationTimeout;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use utf8;

use API::ART::APP::Activity::LC_CUSTOMER_PROJECT;
use SpeedArkBot::Support::RA;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_managed_event_types
{
	return [
		'TIMEOUT_REQUEST_DOCUMENTATION'
	];
}

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	return $self;
}


sub _mail_cdl_cfg { shift->{MAIL_CDL_CFG} }

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	
	my $activity_id = $event->get_source_ref();
	
	my $activity = API::ART::APP::Activity::LC_CUSTOMER_PROJECT->new( ART => $art , ID => $activity_id);

	unless ($activity){
        	my $message = __x("Error retrieving activity {id}: {error}", id => $activity_id, error => $art->last_error);
	        $self->logger->error($message);
	        return $self->skip;
	}
	my $action = 'TIMEOUT_DOCUMENTAZIONE_PROGETTO';

	if ($activity->can_do_action(NAME => $action)){

		my $ra_data = $event->get_data;

		$self->logger->debug(Dumper($ra_data));

		my $ra_SpeedArk = SpeedarkBot::Support::RA->new(
			art => $self->art,
			id  => $activity_id,
		);

		unless (defined $activity->step(
			ACTION => 'DOCUMENTAZIONE_NON_RECUPERATA',
			DESCRIPTION => 'Timeout documentazione',
		)){
			my $message = $art->last_error();
			$self->logger()->warn($message);
			return $self->skip(REASON => $message);
		}

		unless($ra_SpeedArk->fetch_all(0,'Gestito timeout')){
			my $message = $art->last_error();
			$self->logger()->warn($message);
			return $self->skip(REASON => $message);
		};
		
		$message = __x("Activity with id {id} successfully worked ({status})", id => $activity->id(), status => $activity->get_current_status_name());
		$self->logger()->info($message);

		return $self->consume( STATUS => 0, TARGET_REF => $activity->id(), REASON => $message );
	} else {
		$message = __x("Activity with id {id} already managed", id => $activity->id());
		$self->logger()->debug($message);

		return $self->consume( STATUS => 1, TARGET_REF => $activity->id(), REASON => $message );
	}
	
}

sub finish{}

1;
