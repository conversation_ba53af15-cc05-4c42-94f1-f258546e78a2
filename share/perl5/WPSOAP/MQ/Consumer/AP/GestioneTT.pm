package WPSOAP::MQ::Consumer::AP::GestioneTT;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOAP::Collection::System::PROJECT;
use WPSOAP::Collection::System::AP;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'NEW',
		'CLOSE'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{CollProjects} = WPSOAP::Collection::System::PROJECT->new(ART => $self->art());
	$self->{CollAP} = {};
	
	return $self;
}

sub _get_coll_ap {
	my $self = shift;
	my %params = @_;
	
	my $projects = $self->{CollProjects}->cerca(
		 customerId	=> $params{customerId}
		,contractId	=> $params{contractId}
		,projectId	=> $params{projectId}
	);
	croak __x("Unable to search project: {error}", error => $self->art()->last_error())
		unless defined $projects;
	
	$self->art()->last_error("Unable to find project")
		&& return undef
			unless scalar $projects;
	
	return WPSOAP::Collection::System::AP->new(ART => $self->art(), PROJECT => $projects->[0]);
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	for my $p ('customerId', 'contractId', 'projectId', 'permitsAreaId') {
		unless(defined $data->{$p}) {
			$message = __x("Missing mandatory parameter {param}", param => $p);
			$self->logger()->error($message);
			return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message );
		}
	}
	
	my $coll_ap = $self->_get_coll_ap(%{$data});
	unless(defined $coll_ap) {
		$self->logger()->error($self->art()->last_error());
		return $self->consume( STATUS => 2, TARGET_REF => 'KO', REASON => $self->art()->last_error() );
	}
	
	my $permitsAreaId = $event->get_data()->{permitsAreaId};
	
	$self->logger()->info( __x("Verifing if permits area {permitsAreaId} exists", permitsAreaId => $permitsAreaId ) );
	my $aps = $coll_ap->cerca(permitsAreaId => $permitsAreaId);
	croak(__("Unable to search permitsAreaId"))
		unless defined $aps;
	
	unless(scalar @{$aps}) {
		$message = __x("permitsAreaId {permitsAreaId} not found", permitsAreaId => $permitsAreaId);
		$self->logger()->error($message);
		return $self->consume( STATUS => 3, TARGET_REF => 'KO', REASON => $message );
	}
	
	# effettuo l'azione APERTURA_TT o CHIUSURA_TT sull'attività di tipo AP_LC con dato tecnico ttId (SOURCE_REF)
	
	my $action = ($event->get_event_name() eq 'NEW' ? 'APERTURA_TT' : 'CHIUSURA_TT');
	my $description = ($event->get_event_name() eq 'NEW' ? 'Apertura nuovo TT' : 'Chiusura TT');
	
	my $acts = $aps->[0]->get_activities_object(ACTIVITY_TYPE_NAME_EQUAL => 'AP_LC', ACTIVE => 1);
	croak(__("Unable to find AP_LC activity"))
		unless defined $acts;

	croak(__x("Found AP_LC active activity {number}", number => @{$acts}))
		unless scalar @{$acts} == 1;
	
	unless($acts->[0]->step(
		 ACTION => $action
		,DESCRIPTION => $description
		,PROPERTIES => {
			ttId => $event->get_source_ref()
		}
	)) {
		$message = __x("Unable to step AP_LC activity: {error}", error => $self->art()->last_error());
		$self->logger()->error($message);
		return $self->consume( STATUS => 4, TARGET_REF => 'KO', REASON => $message );
	}
	
	$message = __x("permitsAreaId {permitsAreaId} found", permitsAreaId => $permitsAreaId);
	$self->logger()->info($message);
	return $self->consume( STATUS => 0, TARGET_REF => $aps->[0]->id(), REASON => $message );
	
}

sub finish
{

}
1;

