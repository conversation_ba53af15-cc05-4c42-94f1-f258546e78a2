package WPSOAP::MQ::Consumer::PERMESSO_BUILDING::LC02Booking;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOAP::Collection::Activity::PERMESSO_BUILDING;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'LC02_CREATE',
		'LC02_STEP',
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{CollPermesso} = WPSOAP::Collection::Activity::PERMESSO_BUILDING->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	for my $p ('DATA') {
		unless(defined $data->{$p}) {
			$message = __x("Missing mandatory parameter {param}", param => $p);
			$self->logger()->error($message);
			#return $self->skip( REASON => $message );
			return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
		}
	}

	my $datas = eval{from_json($data->{DATA})};
	if ($@){
		$message = __x("Bad JSON {err}", err => $@);
		$self->logger()->error($message);
		#return $self->skip( REASON => $message );
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}

	if ($event->get_event_name() eq 'LC02_CREATE'){
		$datas->{oneForAll} = 0;
		# cerco tutti i lavori attivi
		my $permesso = $self->{CollPermesso}->crea(%$datas);
		
		unless (defined $permesso){
			$message = $art->last_error;
			$self->logger()->error($message);
			#return $self->skip( REASON => $message );
			return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
		}

		$message = __x("Successfully created activity {id}: current status {status}", id => $permesso->[0]->id(), status => $permesso->[0]->get_current_status_name());
		$self->logger()->info($message);
	} else {
	
		# cerco tutti i lavori attivi
		my $permessi = $self->{CollPermesso}->cerca(
			customerId	=>	$datas->{"customerId"},
			contractId	=>	$datas->{"contractId"},
			targetAsset	=> ['PTE'],
			ref00		=>	$datas->{"ref00"},
			ref01		=>	$datas->{"ref01"},
		);
		
		unless (defined $permessi){
			$message = $art->last_error;
			$self->logger()->error($message);
			return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
		}
		
		$art->_dbh()->do( "savepoint WPSOAP_MQ_Cons_PBG_Book" );

		for my $ch (@{$permessi}){
			next unless $ch->is_active(); # se già in uno stato finale non devo fare nulla
			
			unless (defined $art->test_activity_action_name($datas->{action})){
				$art->_dbh()->do( "rollback to savepoint WPSOAP_MQ_Cons_PBG_Book" );
				$message = __x("Unknown action {action}", action => $datas->{action});
				$self->logger()->error($message);
				return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
			}

			unless ($ch->can_do_action(NAME => $datas->{action})){
				$art->_dbh()->do( "rollback to savepoint WPSOAP_MQ_Cons_PBG_Book" );
				$message = __x("Unable to execute action {action} for activity {id}", action => $datas->{action}, id => $ch->id());
				$self->logger()->error($message);
				return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
			}

			my $step_properties = exists $datas->{properties} ? {%{$datas->{properties}}} : {};
			my $step = $ch->step(
				ACTION => $datas->{action},
				PROPERTIES => $step_properties,
				USER => $datas->{operatorLogin},
			);

			unless (defined $step){
				$art->_dbh()->do( "rollback to savepoint WPSOAP_MQ_Cons_PBG_Book" );
				$message = $art->last_error();
				$self->logger()->error($message);
				return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
			}
			$message = __x("Successfully worked activity {id}: current status {status}", id => $ch->id(), status => $ch->get_current_status_name());
			$self->logger()->info($message);
		}
		$message = __("All permits has been worked");
		$self->logger()->info($message);

	}
	
	return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	
}

sub finish
{

}
1;

