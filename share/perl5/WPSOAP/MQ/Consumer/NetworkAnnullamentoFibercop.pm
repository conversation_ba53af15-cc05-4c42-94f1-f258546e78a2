package WPSOAP::MQ::Consumer::NetworkAnnullamentoFibercop;

use strict;
use warnings;
use Carp;
use Data::Dumper;

use WPSOAP::Collection::Activity::PERMESSO_LAVORI;
use WPSOAP::Collection::Activity::PERMESSO_BUILDING;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'ANNULLAMENTO_FIBERCOP'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{CollPermessoLavori} = WPSOAP::Collection::Activity::PERMESSO_LAVORI->new(ART => $self->art());
	$self->{CollPermessoBuilding} = WPSOAP::Collection::Activity::PERMESSO_BUILDING->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	for my $p ('customerId', 'contractId', 'cabinetId') {
		unless(defined $data->{$p}) {
			$message = __x("Missing mandatory parameter {param}", param => $p);
			$self->logger()->error($message);
			return $self->skip( REASON => $message );
		}
	}
	
	my $cont_permessi = 0;
	for my $coll ($self->{CollPermessoLavori}, $self->{CollPermessoBuilding}){
		# cerco tutti i permessi
		my $permessi = $coll->cerca(
			customerId	=>	$data->{"customerId"},
			contractId	=>	$data->{"contractId"},
			targetAsset	=> ['PTE', 'NetworkFibercop'],
			ref00		=>	$data->{"cabinetId"},
		);
		
		unless (defined $permessi){
			$self->logger()->error($art->last_error());
			return $self->skip( REASON => $art->last_error() );
		}

		$permessi = [grep {$_->is_active()} @{$permessi}];

		$cont_permessi +=scalar @{$permessi};
		
		$art->_dbh()->do( "savepoint WPSOAP_MQ_Cons_NaF" );

		for my $ch (@{$permessi}){
			my $step = $ch->step(
				ACTION => 'ANNULLAMENTO'
			);

			unless (defined $step){
				$art->_dbh()->do( "rollback to savepoint WPSOAP_MQ_Cons_NaF" );
				$message = $art->last_error();
				$self->logger()->error($message);
				return $self->skip( REASON => $message );
			}
			$message = __x("Successfully worked activity {id} ({type}): current status {status}", id => $ch->id(), type => $ch->info('ACTIVITY_TYPE_NAME'), status => $ch->get_current_status_name());
			$self->logger()->info($message);
		}
	}
	$message = __x("Successfully worked {s} activities", s => $cont_permessi);
	$self->logger()->info($message);
	return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	
}

sub finish
{

}
1;

