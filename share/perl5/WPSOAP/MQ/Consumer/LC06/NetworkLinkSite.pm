package WPSOAP::MQ::Consumer::LC06::NetworkLinkSite;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;

use WPSOAP::Collection::Activity::LC06;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types
{
	return [
		'LINK_NETWORK'
	];
}

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{CollLC06} = WPSOAP::Collection::Activity::LC06->new(ART => $self->art());
	
	return $self;
}

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $data = $event->get_data();
	for my $p ('SITE', 'LOGIN_OPERATORE') {
		unless(defined $data->{$p}) {
			$message = __x("Missing mandatory parameter {param}", param => $p);
			$self->logger()->error($message);
			return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
		}
	}
	if (!defined $data->{ACTIVITY} && !defined $data->{OLD_ACTIVITY}){
		$message = __x("At least one param betwen {param} and {param1} must be defined", param => 'ACTIVITY', param1 => 'OLD_ACTIVITY');
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}
	my $manage_type = defined $data->{ACTIVITY} ? 'ASSOCIATE' : 'DEASSOCIATE';

	my $datas = $data;
	my $network = $manage_type eq 'ASSOCIATE' ? eval{from_json($datas->{ACTIVITY})} : eval{from_json($datas->{OLD_ACTIVITY})};
	if ($@){
		$message = __x("Bad JSON {err}", err => $@);
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}

	# cerco tutti i siti attivi
	my $sites = $self->{CollLC06}->cerca(
		customerId	=> $network->{system}->{properties}->{"customerId"},
		contractId	=> $network->{system}->{properties}->{"contractId"},
		siteId		=> $datas->{SITE}
	);
	
	unless (defined $sites){
		$message = $art->last_error;
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}
	
	unless (scalar @{$sites}){
		$message = __x("Unable to find site {siteId} for customer {customerId} and contract {contractId}", siteId => $datas->{SITE}, customerId => $network->{system}->{properties}->{"customerId"}, contractId => $network->{system}->{properties}->{"contractId"});
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}
	
	my $ch = $sites->[0];

	# se la devo deassociare
	# verifico che la coppia tipo/network sia esattamente ancora quella
	# in quanto potrebbe essere stata sostituita da qualche altra network
	my $map_site_type = {
		'Primaria'							=> ['sitePrimaryNetwork'],
		'Realizzazione sito'				=> ['siteConstructionNetwork'],
		'Ripristino'						=> ['siteRestorationNetwork'],
		'Realizzazione sito + Ripristino'	=> ['siteConstructionNetwork','siteRestorationNetwork'],
		'Altro'								=> ['siteOtherNetwork'],
	};
	my $deassociate = 1;
	if ($manage_type eq 'DEASSOCIATE'){
		my $networks_list = $map_site_type->{$datas->{SITE_TYPE}};
		SITE: for my $l (@$networks_list){
			my $nl = $ch->activity_property($l);
			if (!defined $nl || (defined $nl && $nl ne $network->{system}->{properties}->{networkId})){
				$deassociate = 0;
				last SITE;	
			}
		}
	}
	unless($deassociate){
		$message = __x("Deassociation not necessary for id {id} and network {network}", id => $ch->id(), network => $network->{system}->{properties}->{networkId});
		$self->logger()->warn($message);
		return $self->consume( STATUS => 2, TARGET_REF => 'OK', REASON => $message, ACK_DATA => { ERROR => $message } );
	}

	my $action = 'AGGANCIO_NETWORK';
	
	unless ($ch->can_do_action(NAME => $action)){
		$art->_dbh()->do( "rollback to savepoint WPSOAP_MQ_Cons_LC05_NL" );
		$message = __x("Unable to execute action {action} for activity {id}", action => $action, id => $ch->id());
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}

	$art->_dbh()->do( "savepoint WPSOAP_MQ_Cons_LC05_NL" );

	my $step_properties = {
		networkSiteType => $datas->{SITE_TYPE}
	};
	if ($manage_type eq 'ASSOCIATE'){
		$step_properties->{networkId} = $network->{system}->{properties}->{networkId};
	} else {
		$step_properties->{oldNetworkId} = $network->{system}->{properties}->{networkId};
	}

	my $step = $ch->step(
		ACTION => $action,
		PROPERTIES => $step_properties,
		USER => $datas->{LOGIN_OPERATORE},
	);

	unless (defined $step){
		$art->_dbh()->do( "rollback to savepoint WPSOAP_MQ_Cons_LC05_NL" );
		$message = $art->last_error();
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message, ACK_DATA => { ERROR => $message } );
	}
	$message = __x("Successfully worked activity {id}: current status {status}", id => $ch->id(), status => $ch->get_current_status_name());
	$self->logger()->info($message);
	
	return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => $message );
	
}

sub finish
{

}
1;

