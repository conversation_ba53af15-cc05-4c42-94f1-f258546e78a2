package WPSOAP::MQ::Consumer::ServiceManagement;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;
use API::ART::Collection::Activity;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_event_types { return [ 'NEW_GROUP' ]; }

#
# override
#
# return:
# arrayref che indica il tipo di eventi che devono essere passati al consumer
#

sub get_managed_source_refs { return []; }

sub init {
    my $self = shift;
    $self->{collActivity} = API::ART::Collection::Activity->new( ART => $self->art );
    return $self;
}

sub _get_coll_activity { shift->{collActivity}; }


#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error) in caso di problemi fatali
#
sub consume_event
{
    my $self    = shift;
    my %params  = @_;
    my $event    = $params{EVENT};
    my $art  = $self->art;
    my $errmsg;
    my $message;

    $self->logger->info( __x('EVENT_NAME: {event_name}', event_name => $event->get_event_name ) );
    $self->logger->info( __x('SOURCE_REF: {source_ref}', source_ref => $event->get_source_ref ) );

    my $data = $event->get_data;

    # normalizzo ad ARRAY
    $data->{'__wgGroupName__'} = ref $data->{'__wgGroupName__'} ? $data->{'__wgGroupName__'} : [$data->{'__wgGroupName__'}];
    if (exists $data->{'__serviceGroupName__'}){
        $data->{'__serviceGroupName__'} = ref $data->{'__serviceGroupName__'} ? $data->{'__serviceGroupName__'} : [$data->{'__serviceGroupName__'}];
    }

    unless (defined $data->{groups}){
        $message = __(q{Missing required data 'groups'});
        $self->logger->warn( $message );
        return $self->skip( REASON => $message );
    }

    my $groups = eval{ from_json($data->{groups}) };
    if ($@){
        $message = __x(q{Bad data 'groups': }.$@);
        $self->logger->warn( $message );
        return $self->skip( REASON => $message );
    }

    if (ref($groups) ne 'ARRAY'){
        $message = __(q{Bad data 'groups': must be an ARRAY});
        $self->logger->warn( $message );
        return $self->skip( REASON => $message );
    }

    for my $g (@{$groups}){
        for my $c ('name', 'description'){
            unless (defined $g->{$c}){
                $message = __x(q{Missing info group: {info}}, info => $c);
                $self->logger->warn( $message );
                return $self->skip( REASON => $message );
            }
        }
        my $createParams = {
            NAME => $g->{name}
            ,DESCRIPTION => $g->{description}
            ,IS_AUTOGENERATED => 1
        };

        # Questo non dovrebbe mai accadere, ma se il gruppo esiste già _qui_ si prosegue
        my $group = $self->art->test_group_name($g->{name});
        if ($group){
            $message = __x(q{Group {groupName} retrieved with id {id}}, groupName => $g->{name}, id => $group);
            $self->logger->trace($message);
        } else {
            $message = __x('Unable to retrieve group {groupName}: {error}', groupName => $g->{name}, error => $self->art->last_error);
            $self->logger->trace( $message );
        }

        # Se il gruppo non esiste, come in effetti non dovrebbe, viene creato, in caso di errore si ritorna KO
        #TODO: verificare se invece sia più opportuno uno skip
        unless ( $group ) {
            $group = $self->art->create_group(%{$createParams});
            if ($group){
                $message = __x('Group {groupName} created with id {id}', groupName => $g->{name}, id => $group);
                $self->logger->trace($message);
            } else {
                $message = __x('Unable to create group {groupName}: {error}', groupName => $g->{name}, error => $self->art->last_error);
                $self->logger->error( $message );
                return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message );
            }
        }

        # Se il gruppo corrisponde a un fornitore viene aggiunto ai sistemi cui sono associate attività assegnate al fornitore stesso
        # e cui il gruppo non sia già stato associato (tramite sanity o altro meccanismo)
        # in caso di errore nell'associazione si ritorna KO - TODO: verificare se invece sia più opportuno uno skip
        if ( $g->{name} =~ m{^SERVICE_} ) {
            ( my $service_id = $g->{name} ) =~ s{^SERVICE_}{};
            my $coll_activity = $self->_get_coll_activity;
            my $activities = $coll_activity->find_object( PROPERTIES => { 'subContractCode' => $service_id } );
            for my $activity ( @{$activities}) {
                my $system = $activity->system;
                my $gnames = $system->info('GROUPS');
                if ( grep { $_ eq $g->{name} } @{$gnames} ) {
                    # Messaggio da non riportare come REASON nel consume
                    my $message = __x(q{Group {group} was already present for system {system}},
                        group  => $g->{name},
                        system => $system->id,
                    );
                    $self->logger->trace( $message );
                } else {
                    if ($system->set_groups($g->{name})) {
                        for my $activity ( @{$system->get_activities_object}) {
                            $activity->refresh;
                        }
                        # Messaggio da non riportare come REASON nel consume
                        my $message = __x(q{Group {group} added to system {system}},
                            group  => $g->{name},
                            system => $system->id,
                        );
                        $self->logger->trace( $message );
                    } else {
                        $message = __x(q{Unable to add group {group} to system {system}: {error}},
                            group  => $g->{name},
                            system => $system->id,
                            error  => $self->art->last_error,
                        );
                        $self->logger->error( $message );
                        return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message );
                    }
                }
            }
        }
    }
    return $self->consume( STATUS => 0, TARGET_REF => 'OK', REASON => $message );
}

sub finish {}

1;
