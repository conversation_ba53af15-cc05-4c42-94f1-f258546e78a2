package WPSOAP::MQ::Sender::LC_CUSTOMER_PROJECT;

use strict;
use warnings;

use SIRTI::ART::RemoteActivity::Source;

use base 'SIRTI::Base::Singleton';

#override metodi Class::Singleton
sub _new_instance {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	die 'Missing mandatory param DB' unless $params{DB};
	
	$self->{WPSOAP_BOT_SOURCE_SERVICE}		= 'ENFTTH_AP';
	$self->{WPSOAP_BOT_SOURCE_CONTEXT}		= 'CUSTOMER_PROJECT';
	$self->{WPSOAP_BOT_SESSION_DESCRIPTION}	= 'Invio comunicazione a BOT';
	$self->{WPSOAP_BOT_TARGET_SERVICE}		= 'BOT';
	$self->{WPSOAP_BOT_TARGET_CONTEXT}		= 'SPEEDARK';

	$self->{WPSOAP_DOC_TIMEOUT_SOURCE_CONTEXT}		= 'CUSTOMER_PROJECT';
	$self->{WPSOAP_DOC_TIMEOUT_SESSION_DESCRIPTION}	= 'Invio comunicazione per timeout documentazione';
	$self->{WPSOAP_DOC_TIMEOUT_TARGET_CONTEXT}		= 'CUSTOMER_PROJECT';

	$self->{DB} = $params{DB};
	
	return $self;	
}

sub instance{
	shift->Class::Singleton::instance(@_);
}

sub has_instance{
	shift->Class::Singleton::has_instance(@_);
}
#fine override metodi Class::Singleton

sub send_bot_speedark  {
	my $self = shift;
	my %params = @_;

	die 'Missing mandatory params RO'
		unless defined $params{RO};

	my %p = (
		EVENT 		=> 'REQUEST_DOCUMENTATION::'.$params{RO},
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
	if (defined $params{EXPIRY_DATE}){
		$p{EXPIRY_DATE} = $params{EXPIRY_DATE};
	}
	
	return $self->_get_ra_bot->insert(%p);
}

sub send_documentation_timeout  {
	my $self = shift;
	my %params = @_;

	my %p = (
		EVENT 		=> 'TIMEOUT_REQUEST_DOCUMENTATION',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA}
	);
	if (defined $params{SCHEDULE_DATE}){
		$p{SCHEDULE_DATE} = $params{SCHEDULE_DATE};
	}
	
	return $self->_get_ra_doc_timeout->insert(%p);
}

sub _get_ra_bot {
	my $self = shift;
	
	$self->{RA_BOT} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SESSION_DESCRIPTION	=> $self->{WPSOAP_BOT_SESSION_DESCRIPTION}
		,SOURCE_SERVICE			=> $self->{WPSOAP_BOT_SOURCE_SERVICE}
		,SOURCE_CONTEXT			=> $self->{WPSOAP_BOT_SOURCE_CONTEXT}
		,TARGET => {
			$self->{WPSOAP_BOT_TARGET_SERVICE} => $self->{WPSOAP_BOT_TARGET_CONTEXT},
		}
		
	) unless defined $self->{RA_BOT};
	
	return $self->{RA_BOT};
}

sub _get_ra_doc_timeout {
	my $self = shift;
	
	$self->{RA_DOC_TIMEOUT} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SESSION_DESCRIPTION	=> $self->{WPSOAP_DOC_TIMEOUT_SESSION_DESCRIPTION}
		,SOURCE_CONTEXT			=> $self->{WPSOAP_DOC_TIMEOUT_SOURCE_CONTEXT}
		,TARGET_CONTEXT			=> $self->{WPSOAP_DOC_TIMEOUT_TARGET_CONTEXT}
		
	) unless defined $self->{RA_DOC_TIMEOUT};
	
	return $self->{RA_DOC_TIMEOUT};
}

1;

