package WPSOAP::MQ::Sender::PERMESSO_BUILDING;

use strict;
use warnings;

use SIRTI::ART::RemoteActivity::Source;

use base 'SIRTI::Base::Singleton';

#override metodi Class::Singleton
sub _new_instance {
	my $class = shift;
	my $self  = bless { }, $class;
	my %params = @_;
	
	die 'Missing mandatory param DB' unless $params{DB};
	
	$self->{WPSOAP_CORE_NOTIFY_SOURCE_SERVICE}		= 'ENFTTH_AP';
	$self->{WPSOAP_CORE_NOTIFY_SOURCE_CONTEXT}		= 'PERMESSO_BUILD';
	$self->{WPSOAP_CORE_NOTIFY_TARGET_SERVICE}		= 'ENFTTH_CORE';
	$self->{WPSOAP_CORE_NOTIFY_TARGET_CONTEXT}		= 'PERMESSO_BUILD';
	$self->{WPSOAP_CORE_NOTIFY_SESSION_DESCRIPTION}	= 'WPSOAP - Comunicazioni movimentazioni permesso building';
	
	$self->{DB} = $params{DB};
	
	return $self;	
}

sub instance{
	shift->Class::Singleton::instance(@_);
}

sub has_instance{
	shift->Class::Singleton::has_instance(@_);
}
#fine override metodi Class::Singleton

sub notify_permit_open  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_wpsoap_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_OPEN',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}

sub notify_permit_close_ok  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_wpsoap_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_CLOSE_OK',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}

sub notify_permit_close_ko  {
	my $self = shift;
	my %params = @_;
	
	return $self->_get_ra_wpsoap_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_CLOSE_KO',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}

sub notify_permit_update  {
	my $self = shift;
	my %params = @_;

	return $self->_get_ra_wpsoap_core_notify->insert(
		EVENT 		=> $params{TYPE}.'_NOTIFY_UPDATE',
		SOURCE_REF	=> $params{SOURCE_REF},
		DATA		=> $params{DATA},
	);
}
sub _get_ra_wpsoap_core_notify {
	my $self = shift;
	
	$self->{RA_AP_CORE_NOTIFY} = SIRTI::ART::RemoteActivity::Source->new (
		 DB						=> $self->{DB}
		,SOURCE_SERVICE			=> $self->{WPSOAP_CORE_NOTIFY_SOURCE_SERVICE}
		,SOURCE_CONTEXT			=> $self->{WPSOAP_CORE_NOTIFY_SOURCE_CONTEXT}
		,TARGET_SERVICE			=> $self->{WPSOAP_CORE_NOTIFY_TARGET_SERVICE}
		,TARGET_CONTEXT			=> $self->{WPSOAP_CORE_NOTIFY_TARGET_CONTEXT}
		,SESSION_DESCRIPTION	=> $self->{WPSOAP_CORE_NOTIFY_SESSION_DESCRIPTION}
	) unless defined $self->{RA_AP_CORE_NOTIFY};
	
	return $self->{RA_AP_CORE_NOTIFY};
}

1;

