package WPSOAP::Controller::BUILDING;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOAP::Collection::System::PROJECT;
use WPSOAP::Collection::Activity::BUILDING_LC;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

my $building_dt_map_vs_sinfo = {
	 buildingSinfoId => [ 'buildingSinfoId' ]
	,permitsAreaId => [ 'permitsAreaId' ]
	,workZoneId => [ 'workZoneId' ]
	,placeName => [ 'address', 'placeName' ]
	,streetName => [ 'address', 'streetName' ]
	,streetNumber => [ 'address', 'streetNumber' ]
	,staircase => [ 'address', 'staircase' ]
	,city => [ 'address', 'city' ]
	,zip => [ 'address', 'zip' ]
	,province => [ 'address', 'province' ]
	,country => [ 'address', 'country' ]
	,latitude => [ 'latitude' ]
	,longitude => [ 'longitude' ]
	,buildingId => [ 'buildingId' ]
	,privateUI => [ 'privateUI' ]
	,smallBusinessUI => [ 'smallBusinessUI' ]
	,staircasesCount => [ 'staircasesCount' ]
	,floorsCount => [ 'floorsCount' ]
	,UIperFloor => [ 'UIperFloor' ]
	,owner => [ 'owner' ]
	,administratorName => [ 'administratorName' ]
	,administratorPhone => [ 'administratorPhone' ]
	,administratorEmail => [ 'administratorEmail' ]
	,verticalConstruction => [ 'verticalConstruction' ]
	,TlcColumn => [ 'TlcColumn' ]
	,excluded => [ 'excluded' ]
	,ptaId => [ 'ptaId' ]
	,pteId => [ 'pteId' ]
	,uprightColumn => [ 'uprightColumn' ]
	,locatedPte => [ 'locatedPte' ]
};

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};

	my $self = bless( {}, $class );

	# Controlli sui parametri
	
	die(__x('Missing mandatory param {paramname}', paramname => 'WPSOAP')) unless defined $params->{WPSOAP};
	die(__x('Param {paramname} must be of type {type}', paramname => 'WPSOAP', type => 'WPSOAP')) if ref($params->{WPSOAP}) ne 'WPSOAP';
	
	$self->{WPSOAP} = $params->{WPSOAP};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _wpsoap { shift->{WPSOAP} }

sub _art { shift->_wpsoap()->art() }

sub _db { shift->_art()->_dbh() }

sub _logger { shift->{LOGGER} }

sub get_buildings_from_sinfo {
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "PROJECT"	=> { isa => 'WPSOAP::System::PROJECT' }
				}
				,OPTIONAL	=> {}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->_logger()->info(__x('Getting buildings from SiNFO by WS for project {project}', project => $params{PROJECT}->property('projectId')));
	my $buildings = $self->_wpsoap()->invoke_sinfo_rest(
		RESOURCE => sprintf($ENV{SINFO_GET_BUILDINGS_MASK_RESOURCE}, $params{PROJECT}->property('customerId'), $params{PROJECT}->property('contractId'), $params{PROJECT}->property('projectId'))
	);
	return undef
		unless defined $buildings;
	
	$self->_logger()->info(__x('Found {number} buildings', number => scalar @{$buildings}));
	
	return $buildings;
	
}

sub create_or_update_buildings_from_sinfo {

	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					 "customerId"	=> { isa => 'SCALAR' }
					,"contractId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
					 "projectId"	=> { isa => 'SCALAR' },
					 "COMMIT_AFTER_EVERY_BUILDING" => { isa => 'SCALAR', list => [ 0, 1 ] }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	my $projects_collection;
	eval {
		$projects_collection = WPSOAP::Collection::System::PROJECT->new(ART => $self->_art());
	};
	$self->_art()->last_error(__x("Unable to init WPSOAP::Collection::System::PROJECT: {error}", error => $@))
		&& return undef
			if $@;
	
	$self->_logger()->info(__('Getting local projects'));
	my %p = (customerId => $params{customerId} ,contractId => $params{contractId});
	$p{projectId} = $params{projectId}
		if defined $params{projectId};
	my $projects = $projects_collection->cerca(%p);
	return undef
		unless defined $projects;
	
	$self->_logger()->info(__x('Found {number} local projects for customerId {id_cliente}, contractId {id_contratto}', number => scalar @{$projects}, id_cliente => $params{customerId}, id_contratto => $params{contractId}));
	PRJ: for my $project (@{$projects}) {
		$self->_logger()->info(__x('Working on project {project}', project => $project->property('projectId')));
		
		my $buildings_from_sinfo = $self->get_buildings_from_sinfo(
			 PROJECT => $project
		);
		unless(defined $buildings_from_sinfo) {
			$self->_logger()->error(__x("Unable to get buildings from SiNFO from project {project}: ".$self->_art()->last_error(), project => $project->property('projectId')));
			next PRJ;
		}
		
		my $num_buildings = scalar @{$buildings_from_sinfo};
		
		my $buildings_local = $project->get_children( SYSTEM_TYPE_NAME => ['BUILDING'], SHOW_ONLY_WITH_VISIBILITY => 1 );
		return undef
			unless defined $buildings_local;
		
		my $buildings_local_buildingSinfoId = {};
		for my $b (@{$buildings_local}) {
			my $building_sinfo_id = $b->property('buildingSinfoId');
			$self->_art()->last_error(__x('Found more than one system for buildingSinfoId {id_sinfo}, customerId {id_cliente}, contractId {id_contratto}, projectId {projectId}', id_sinfo => $building_sinfo_id, id_cliente => $params{customerId}, id_contratto => $params{contractId}, projectId => $project->property('projectId')))
				&& return undef
					if exists $buildings_local_buildingSinfoId->{$building_sinfo_id};
			$buildings_local_buildingSinfoId->{$building_sinfo_id} = $b
		}
		
		my $buildings_activity_local_collection = WPSOAP::Collection::Activity::BUILDING_LC->new(ART => $self->_art(), PROJECT => $project);
		return undef
			unless defined $buildings_activity_local_collection;

		my $i = 0;
		my %used_buildings = ();
		for my $building_from_sinfo (@{$buildings_from_sinfo}) {
			my $building_sinfo_id = $self->_remap_sinfo_prop_to_art_dt('buildingSinfoId', $building_from_sinfo, $building_dt_map_vs_sinfo)||'';
			if(exists $buildings_local_buildingSinfoId->{$building_sinfo_id}) { # se esiste
				$used_buildings{$buildings_local_buildingSinfoId->{$building_sinfo_id}->id()} = 1;
				my $buildings_activity = $buildings_local_buildingSinfoId->{$building_sinfo_id}->get_activities_object(
					ACTIVITY_TYPE_NAME => ['BUILDING_LC'],
				);
				return undef
					unless defined $buildings_activity;
				$self->_art()->last_error(__x('Found {number} activities for buildingSinfoId {id_sinfo}, customerId {id_cliente}, contractId {id_contratto}, projectId {projectId}', number => scalar @{$buildings_activity}, id_sinfo => $building_sinfo_id, id_cliente => $params{customerId}, id_contratto => $params{contractId}, projectId => $project->property('projectId')))
					&& return undef
						unless scalar @{$buildings_activity} == 1;
				
				$self->_logger->trace(__x("Found a building with buildingSinfoId {idsinfo} already present ({n}/{m})", idsinfo => $building_sinfo_id, n => ++$i, m => $num_buildings));

				# verifico se devo aggiungere o modificare una property
				my $building_system_property = $buildings_local_buildingSinfoId->{$building_sinfo_id}->property();
				my $do_modify = 0;
				my %building_params = ();
				for my $dt (keys %{$building_dt_map_vs_sinfo}) {
					next if $dt eq 'buildingSinfoId';
					my $sinfo_prop = $self->_remap_sinfo_prop_to_art_dt($dt, $building_from_sinfo, $building_dt_map_vs_sinfo);
					undef $sinfo_prop
						if(defined $sinfo_prop && $sinfo_prop eq '');
#					if($dt =~ /^(XXXX)$/ && defined $sinfo_prop) { # lasciato per promemoria gestione booleani
#						# gestione booleani
#						$sinfo_prop = $sinfo_prop ? 1 : 0;
#					}
					my $art_dt = $building_system_property->{$dt};
					if(
						defined $sinfo_prop && !defined $art_dt
						||
						defined $sinfo_prop && $sinfo_prop ne $art_dt
					) {
						$self->_logger->trace(__x("Property {property} to add or modify", property => $dt));
						$do_modify = 1;
					}
					if(!defined $sinfo_prop && defined $art_dt) {
						$self->_logger->trace(__x("Property {property} to delete", property => $dt));
						$do_modify = 1;
					}
					$building_params{$dt} = $sinfo_prop;
				}
				
				my $func;
				if($do_modify) {
					if($buildings_activity->[0]->get_current_status_name() eq 'SOSPESA') {
						$self->_logger()->info(__x('Activity suspended for buildingSinfoId {idsinfo}, found differences: proceed to modify and unsuspend', idsinfo => $building_sinfo_id));
						$func = 'desospendi';
					} else {
						$self->_logger()->info(__x('Found differences for buildingSinfoId {idsinfo}: proceed to modify', idsinfo => $building_sinfo_id));
						$func = 'modifica';
					}
				} else {
					if($buildings_activity->[0]->get_current_status_name() eq 'SOSPESA') {
						$self->_logger()->info(__x('Activity suspended for buildingSinfoId {idsinfo}: proceed to unsuspend', idsinfo => $building_sinfo_id));
						$func = 'desospendi';
					} else {
						$self->_logger()->trace(__('Nothing to do'));
					}
				}
				if(defined $func) {
					return undef
						unless $buildings_activity->[0]->$func(%building_params);
				}
			} else {
				# creo il building presente su sinfo e non in locale
				$self->_logger->debug(__x("Found a new building with buildingSinfoId {idsinfo} ({n}/{m})", idsinfo => $building_sinfo_id, n => ++$i, m => $num_buildings));
				my %building_params = (
					 customerId		=> $params{customerId}
					,contractId	=> $params{contractId}
					,projectId	=> $project->property('projectId')
				);
				for my $dt (keys %{$building_dt_map_vs_sinfo}) {
					my $sinfo_prop = $self->_remap_sinfo_prop_to_art_dt($dt, $building_from_sinfo, $building_dt_map_vs_sinfo);
					undef $sinfo_prop
						if(defined $sinfo_prop && $sinfo_prop eq '');
#					if($dt =~ /^(XXXX)$/ && defined $sinfo_prop) { # lasciato per promemoria gestione booleani
#						# gestione booleani
#						$sinfo_prop = $sinfo_prop ? 1 : 0;
#					}
					$building_params{$dt} = $sinfo_prop if defined $sinfo_prop;
				}
				$self->_logger()->trace("Building creation params: ".Dumper(\%building_params));
				return undef
					unless defined $buildings_activity_local_collection->crea(%building_params);
				$self->_logger->info(__x("Building with buildingSinfoId {idsinfo} created", idsinfo => $building_sinfo_id));
			}
			
			if($params{COMMIT_AFTER_EVERY_BUILDING}) {
				$self->_logger->trace(__("Do commit"));
				$self->_art()->save();
			}
			
		}
		
		for my $building (@{$buildings_local}) {
			next if defined $used_buildings{$building->id()};
			my $buildings_activity = $building->get_activities_object(
				ACTIVITY_TYPE_NAME => ['BUILDING_LC'],
			);
			return undef
				unless defined $buildings_activity;
			$self->_logger->trace(__x("Found building with buildingSinfoId {idsinfo} in ART but not in SINFO", idsinfo => $buildings_activity->[0]->system()->property('buildingSinfoId')));
			if($buildings_activity->[0]->get_current_status_name() eq 'SOSPESA') {
				$self->_logger->trace(__x("Building is suspended for buildingSinfoId {idsinfo}, nothing to do", idsinfo => $buildings_activity->[0]->system()->property('buildingSinfoId')));
			} else {
				$self->_logger->info(__x("Suspending the building for buildingSinfoId {idsinfo}", idsinfo => $buildings_activity->[0]->system()->property('buildingSinfoId')));
				return undef
					unless $buildings_activity->[0]->sospendi();
			}
			
			if($params{COMMIT_AFTER_EVERY_BUILDING}) {
				$self->_logger->trace(__("Do commit"));
				$self->_art()->save();
			}
			
		}
	}
	
	return 1;
	
}

sub _remap_sinfo_prop_to_art_dt {
	my $self = shift;
	my $property = shift;
	my $sinfo_obj = shift;
	my $map = shift;
	
	return undef
		unless defined $map->{$property};
	
	my $p = $sinfo_obj;
	for my $chunk (@{$map->{$property}}) {
		return undef
			unless defined $p->{$chunk};
		$p = $p->{$chunk};
	}
	
	return $p;
	
};

if (__FILE__ eq $0) {
	use API::ART;
	use WPSOAP;
	
	my $log_level = 'INFO';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $wpsoap = WPSOAP->new(ART => $art);
	
	my $controller;
	eval {
		$controller = WPSOAP::Controller::BUILDING->new(
			WPSOAP => $wpsoap
		)
	};
	if ($@) {
		die "WPSOAP::Controller::BUILDING error: $@";
	}

	my $s = {
		"permitsAreaId" => 123,
		"address" => {
			"streetName" => "Roma"
		}
	};
	get_logger->info($controller->_remap_sinfo_prop_to_art_dt("permitsAreaId", $s, $building_dt_map_vs_sinfo)||"undefined");
	get_logger->info($controller->_remap_sinfo_prop_to_art_dt("streetName", $s, $building_dt_map_vs_sinfo)||"undefined");
	get_logger->info($controller->_remap_sinfo_prop_to_art_dt("placeName", $s, $building_dt_map_vs_sinfo)||"undefined");
	get_logger->info($controller->_remap_sinfo_prop_to_art_dt("REFERENTE_NOME", $s, $building_dt_map_vs_sinfo)||"undefined");
	get_logger->info($controller->_remap_sinfo_prop_to_art_dt("NON_CE", $s, $building_dt_map_vs_sinfo)||"undefined");

	if($controller->create_or_update_buildings_from_sinfo(
		customerId => 'ENEL'
		,contractId => 'FTTH'
	)) {
		get_logger->info("OK");
	} else {
		die "Unable to get buildings from sinfo: ".$wpsoap->art()->last_error();
	}
	
	$art->cancel();
}

1;
