package WPSOAP::Subcontracts;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	return $self->_cerca(%params);
}

sub get_email_from_username{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		&& return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					username => { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $sql = '
		select EMAIL "email"
		from ap.MV_ANAG_PERSONE_EXT
		where userid = ?
	';
	my @bind_params = (
		$params{username}
	);
	
	# creo nome dinamico per sfruttare la prepare
	my $prepare_name = __PACKAGE__."_cc_us";
	
	my $prepare = $self->_art()->_create_prepare($prepare_name, $sql);
	
	my $res = $prepare->fetchall_hashref(@bind_params);
	
	unless ($res) {
		$self->_art()->last_error($self->get_db()->get_errormessage());
		return undef;
	}
	
	return $res->[0]||'';
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					
				}
				,OPTIONAL	=> {
					companyCode	=> { isa =>'SCALAR' },
					q		=> { isa =>'SCALAR', pattern => qr/^.+$/m },
					limit	=> { isa =>'SCALAR', pattern => qr/^\d+$/ }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	
	$self->_art()->last_error(__x("One param between {param1} and {param2} must be defined", param1 => 'q', param2 => 'companyCode'))
		&& return undef
			if (!defined $params{q} && !defined $params{companyCode});
	
	$self->_art()->last_error(__x("Only one param between {param1} and {param2} must be defined", param1 => 'q', param2 => 'companyCode'))
		&& return undef
			if (defined $params{q} && defined $params{companyCode});
	
	my $where_condition = '';
	
	if (defined $params{q}) {
		my $value_search = '%'.uc($params{q}).'%';
	
		$params{limit} = 20 unless defined $params{limit};
		
		$where_condition.= ' and 
				(
					upper(partner) like '.$self->get_db()->quote($value_search).'
					or
					upper(RAGIONE_SOCIALE) like '.$self->get_db()->quote($value_search).'
					or
					upper(INDIRIZZO) like '.$self->get_db()->quote($value_search).'
					or
					upper(LOCALITA) like '.$self->get_db()->quote($value_search).'
					or
					upper(PV) like '.$self->get_db()->quote($value_search).'
					or
					upper(CAP) like '.$self->get_db()->quote($value_search).'
					or
					upper(PARTITA_IVA) like '.$self->get_db()->quote($value_search).'
					or
					upper(PARTITA_IVA_UE) like '.$self->get_db()->quote($value_search).'
					or
					upper(CODICE_FISCALE) like '.$self->get_db()->quote($value_search).'
				)
				and rownum <= '.$params{limit};
	} else {
		$where_condition.= 'and PARTNER = '.$self->get_db()->quote($params{companyCode});
	}
	
	my $sql = '
		select
			PARTNER "companyCode"
			,RAGIONE_SOCIALE "companyName"
			,INDIRIZZO "address"
			,LOCALITA "city"
			,PV "province"
			,CAP "zip"
			,PARTITA_IVA "vatNumber"
			,PARTITA_IVA_UE "UEvatNumber"
			,CODICE_FISCALE "fiscalCode"
			,nvl(ref_name, ref_surname) "refName"
			,ref_surname "refSurname"
			,ref_phone "refPhone"
			,ref_fax "refFax"
		from
			ap.mv_anagrafica_sub
		where 1=1
		'.$where_condition.'
		order by ragione_sociale
	';
	
	my $subcontracts = $self->get_db()->fetchall_hashref($sql);
	
	return $subcontracts;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $s = WPSOAP::Subcontracts->new(ART => $art);
	
	my $subcontracts = $s->get_email_from_username(
		#q => 'bus'
		#companyCode => '0000121589'
		username => 'XT05823'
	);
	
	if( defined $subcontracts) {
		get_logger()->info("OK cerca: ".Dumper($subcontracts));
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
	my $sub_from_user = $s->get_company_from_username(username => 'XT07202');
	
	if( defined $sub_from_user) {
		get_logger()->info("OK get_company_from_username: ".Dumper($sub_from_user));
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
}

1;
