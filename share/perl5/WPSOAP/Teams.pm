package WPSOAP::Teams;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;
use WPSOAP::Collection::System::CUSTOMER;
use WPSOAP::Collection::System::CONTRACT;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	my $self = bless {}, $class;
	
	$self->{ART} = $params->{ART};

	$self->{COLL_CUSTOMER} = WPSOAP::Collection::System::CUSTOMER->new(ART => $self->{ART});

	$self->{COLLS_CONTRACT} = {};

	$self->{CUSTOMERS} = {};

	$self->{CONTRACTS} = {};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOAP::LIB::' . __PACKAGE__ );
	
	return $self;
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _get_coll_customer {shift->{COLL_CUSTOMER}}

sub get_db{shift->_art()->_dbh()}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	return $self->_cerca(%params);
}

sub _cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
				}
				,OPTIONAL	=> {
					companyCard			=> { isa =>'SCALAR' },
					username			=> { isa =>'SCALAR' },
					q					=> { isa =>'SCALAR', pattern => qr/^.+$/m },
					limit				=> { isa =>'SCALAR', pattern => qr/^\d+$/ }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);

	$self->_art()->last_error(__x("One param between {param1}, {param2} or {param3} must be defined", param1 => 'q', param2 => 'companyCard', param3 => 'username'))
		&& return undef
			if (!defined $params{q} && !defined $params{companyCard} && !defined $params{username});
	
	$self->_art()->last_error(__x("Only one param between {param1}, {param2} and {param3} must be defined", param1 => 'q', param2 => 'companyCard', param3 => 'username'))
		&& return undef
			if (defined $params{q} && defined $params{companyCard} && defined $params{username});
	
	my $where_condition = '';
	
	if (defined $params{q}) {
		my $value_search = '%'.uc($params{q}).'%';
	
		$params{limit} = 20 unless defined $params{limit};
		
		$where_condition.= ' and 
				(
					upper(v.nome)||\' \'||upper(v.cognome) like '.$self->get_db()->quote($value_search).'
					or
					upper(v.cognome)||\' \'||upper(v.nome) like '.$self->get_db()->quote($value_search).'
				)
		and rownum <= '.$params{limit};
	} elsif (defined $params{companyCard}) {
		$where_condition.= 'where v.cidsap = '.$self->get_db()->quote($params{companyCard});
	} else {
		
		# effettuto cablatura per ROOT: solo per test/sviluppo
		return [{
			"username" => $params{username},
			"teamId" => '00000000',
			"teamName" => $params{username}.' '.$params{username},
			"name" => $params{username},
			"surname" => $params{username},
			"businessRole" => $params{username},
			"workingGroupCode" => '100000',
			"email" => '<EMAIL>'
		}]
			if ($params{username} eq 'ROOT');
		
		
		$where_condition.= 'where v.username = '.$self->get_db()->quote($params{username});
	}
	
	my $sql = '
		select
			v.username "username",
			v.cidsap "teamId",
			v.cognome||\' \'||v.nome "teamName",
			v.nome "name",
			v.cognome "surname",
			v.email "email",
			substr(v.descr_ruolo_prof, 0,  instr(v.descr_ruolo_prof, \' \', 1,1)-1) "businessRole",
			substr(ccosto, 1,2)||substr(ccosto, length(ccosto)-3) "workingGroupCode"
		from
			ap.mv_anag_persone v
		'.$where_condition.'
		order by v.cognome, v.nome
	';
	
	my $teams = $self->get_db()->fetchall_hashref($sql);
	
	return $teams;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			USER => 'root',
			PASSWORD => 'pippo123'
		);
	};
	if ($@) {
		die "Login error: $@";
	}
	
	my $s = WPSOAP::Teams->new(ART => $art);
	
	my $subcontracts = $s->cerca(
		#q => 'MARZIO'
		companyCard => '49380836',
		#username => 'LIVRAGH',
		#companyAbbreviation => 'IT50',
		#customerId => 'TIM',
		#contractId => 'FTTH',
	);
	
	if( defined $subcontracts) {
		get_logger()->info("OK: ".Dumper($subcontracts));
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
	
}

1;
