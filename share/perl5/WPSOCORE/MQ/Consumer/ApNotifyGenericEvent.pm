package WPSOCORE::MQ::Consumer::ApNotifyGenericEvent;

use strict;
use warnings;
use Carp;
use Data::Dumper;
use JSON;
use WPSOCORE::Collection::Activity::NETWORK;

use base 'SIRTI::Queue::EventConsumer';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_managed_event_types
{
	return [
		'TT001'
	];
}

sub get_managed_source_refs
{
	return [];
}

sub init
{
	my $self = shift;
	my $db		= $self->db();
	
	$self->{WPSOCORE} = WPSOCORE->new(ART => $self->art());
	
	$self->{CollNetworks} = WPSOCORE::Collection::Activity::NETWORK->new(ART => $self->art());
	
	return $self;
}

sub _wpsocore { shift->{WPSOCORE} }

sub _get_coll_networks { shift->{CollNetworks} }

#
# override
#
# params
# EVENT : oggetto di tipo SIRTI::Queue::Event
#
# return:
# oggetto di tipo SIRTI::Queue::Response o undef (e set last_error()) in caso di problemi fatali
#
sub consume_event()
{
	my $self	= shift;
	my %params	= @_;
	my $event	 = $params{EVENT};
	my $art	 = $self->art();
	my $errmsg;
	my $message;
	
	$self->logger()->info( __x("EVENT_NAME: {event_name}", event_name => $event->get_event_name() ) );
	$self->logger()->info( __x("SOURCE_REF: {source_ref}", source_ref => $event->get_source_ref() ) );
	
	my $objAct = from_json($event->get_data()->{ACTIVITY});
	my $final_status_type = $event->get_data()->{FINAL_STATUS_TYPE};
	#$self->logger()->info(Dumper $objAct);
	my $coll = $self->_get_coll_networks();
	# verifico se è già presente
	my $networks = $coll->cerca(
		customerId	=> $objAct->{properties}->{customerId},
		contractId	=> $objAct->{properties}->{contractId},
		networkId	=> $objAct->{properties}->{networkId}
	);
	unless (defined $networks){
		$message = $art->last_error();
		$self->logger()->error($message);
		return $self->skip( REASON => $message );
	}
	
	my $act;
	if (scalar @{$networks}){
		$act = $networks->[0];
		unless($act->system->set_property(
			PROPERTIES => {
				tt001Total		=> $event->get_data()->{STATS_TOTAL},
				tt001OnGoing	=> $event->get_data()->{STATS_ONGOING},
				tt001ClosedOK	=> $event->get_data()->{STATS_OK},
				tt001ClosedKO	=> $event->get_data()->{STATS_KO},
		}
		)){
			$message = $art->last_error();
			$self->logger()->error($message);
			return $self->skip( REASON => $message );
		};
		# se l'azione è presa in carico o apertura tt cliente o è in uno stato finale
		if(
			($event->get_data()->{ACTION}
			&& (
				$event->get_data()->{ACTION} eq 'PRESA_IN_CARICO_SIRTI' 
				||
				$event->get_data()->{ACTION} eq 'APERTURA_TT_CLIENTE'
			))
			||
				$objAct->{info}->{isClosed}
		){
			#cercare il dato tecnico sistema centralCabinetId,se esiste,per ognuno di questi id,istanzio attività
			my $cabinets = $act->system_property('centralCabinetId');	
			if(defined $cabinets){
				for my $cabinetId (@{$cabinets}){
					my $lc01 = API::ART::Activity::Factory->new(ART => $art, ID => $cabinetId);
					unless (defined $lc01){		
						$message = $art->last_error();
						$self->logger()->error($message);
						return $self->skip(REASON => $message);
					}
					my @targetAsset = defined $objAct->{properties}->{tt001TargetAsset} ? split('-', $objAct->{properties}->{tt001TargetAsset}) : ();
					my $actCentral = $targetAsset[0]||$lc01->system_property('centralId');
					my $actCabinet = $targetAsset[1]||$lc01->system_property('id');
					my $sysCentralId = $lc01->system_property('centralId');
					my $sysCabinetId = $lc01->system_property('id');
					# devo fare qualcosa solo se l'armadio che sto gestendo è esattamente quello previsto per il TT
					if ($sysCentralId eq $actCentral && $sysCabinetId eq $actCabinet) {
						my $refresh = $lc01->refresh_cabinet_by_tt(
							networkId 			=> 	$objAct->{properties}->{networkId},
							ACTION 				=> 	$objAct->{info}->{isClosed} ? 'RESTORE' :'SUSP',
							ttId				=> 	$objAct->{id},
							ttCustomerSystem	=>	$objAct->{properties}->{customerSystem}
						);
						# Controlla se la chiamata ha avuto successo
						unless ($refresh) {
							$message = $art->last_error();
							$self->logger()->error($message);
							return $self->skip(REASON => $message);
						}
					}			
				}
			}
		}
	} else { 
		$message = __x("Unable to find ongoing network {networkId}", networkId => $objAct->{properties}->{networkId});
		$self->logger()->error($message);
		return $self->consume( STATUS => 1, TARGET_REF => 'KO', REASON => $message );
	}
	# refresh per aggiornamento ELK
	unless($act->refresh()){
                $message = $art->last_error();
                $self->logger()->error($message);
                return $self->skip( REASON => $message );
        };

	# use Data::Dumper;
	# print STDERR Dumper $act->system_property();
	$message = __x("Network {networkId} (activity id {id}) successfully worked", networkId => $objAct->{properties}->{networkId}, id => $act->id());
	$self->logger()->info($message);
	return $self->consume( STATUS => 0, TARGET_REF => $act->id(), REASON => $message );
	
}

sub finish
{

}
1;

