package WPSOCORE::Collection::Activity::LC02;

use strict;
use warnings;

use Data::Dumper;
$Data::Dumper::Sortkeys=1;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;
use Excel::Writer::XLSX;
use Encode;

use API::ART::Collection::Activity;
use API::ART::Activity::Factory;

use WPSOCORE;
use WPSOCORE::Subcontracts;
use WPSOCORE::Cluster;
use WPSOCORE::WorkPerformance;
use WPSOCORE::Collection::Activity::LC01;
use WPSOCORE::Collection::System::EL01;
use WPSOCORE::Collection::System::EL02;
use WPSOCORE::Collection::Activity::ROE;
use WPSOCORE::Collection::System::CUSTOMER;
use WPSOCORE::Collection::System::CONTRACT;
use WPSOCORE::Collection::Activity::NETWORK;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
    my $this  = shift;
    my $class = ref($this) || $this;
    my $params = {@_};

    my $self = bless( {}, $class );

    # Controlli sui parametri
    die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
    die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';

    $self->{ART} = $params->{ART};

    $self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOCORE::LIB::' . __PACKAGE__ );

    $self->{WPSOCORE} = WPSOCORE->new(ART => $self->{ART});

    $self->{Subcontracts} = WPSOCORE::Subcontracts->new(ART => $self->{ART});

    $self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});

    $self->{CollAttLC01} = WPSOCORE::Collection::Activity::LC01->new(ART => $self->{ART});

    $self->{CollSysEL01} = WPSOCORE::Collection::System::EL01->new(ART => $self->{ART});

    $self->{CollAttROE} = WPSOCORE::Collection::Activity::ROE->new(ART => $self->{ART});

    $self->{CollNetwork} = WPSOCORE::Collection::Activity::NETWORK->new(ART => $self->{ART});

    return bless( $self, $class );
}

sub _art { shift->{ART} }

sub _logger { shift->{LOGGER} }

sub _get_db { shift->_art->_dbh }

sub _wpsocore { shift->{WPSOCORE} }

sub _subcontracts { shift->{Subcontracts} }

sub _get_coll_activity {shift->{CollActivity}}

sub _get_coll_activity_lc01 {shift->{CollAttLC01}}

sub _get_coll_system_el01 {shift->{CollSysEL01}}

sub _get_coll_activity_roe {shift->{CollAttROE}}

sub _get_coll_network {shift->{CollNetwork}}

sub _get_customer_collection {
	my $self = shift;

	return $self->{COLL_CUSTOMER} if defined $self->{COLL_CUSTOMER};

	$self->{COLL_CUSTOMER} = WPSOCORE::Collection::System::CUSTOMER->new(ART => $self->_art) unless exists $self->{COLL_CUSTOMER};
	return $self->{COLL_CUSTOMER};
}

sub crea_migFibercop {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return
            unless $self->_art->check_named_params(
                    ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    ID_ATTIVITA_ROE => { isa => 'SCALAR' },
                    customerId => { isa => 'SCALAR' },
                    contractId => { isa => 'SCALAR' },
                    cabinetWBELevel3 => { isa => 'SCALAR' },
                    cabinetODS => { isa => 'SCALAR' },
                    cluster => { isa => 'SCALAR' },
                    WBELevel3 => { isa => 'SCALAR' },
                    cabinet => { isa => 'SCALAR' },
                    ODS => { isa => 'SCALAR' },
                    elementType => { isa => 'SCALAR', list => ['ROE', 'PTE'] },
                    cabinetType => { isa => 'SCALAR', list => ['CNO', 'ARLO'] },
                    clusterJobReport => { isa => 'SCALAR' },
                    penetrationIndex => { isa => 'SCALAR' },
                }
                ,OPTIONAL   => {
                    # questo parametro viene usato solo se il ROE agganciato è già in CHIUSA
                     WORKS            => { isa => 'ARRAY' },
                    ,idCNO            => { isa => 'SCALAR' }
                    ,networkBasedUpon => { isa => 'SCALAR' }
                    ,teamAssistant    => { isa => 'SCALAR' }
                    ,CLLI             => { isa => 'SCALAR' }
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    # instanziare l'oggetto ROE per recuperare tutte le info da passare alla crea
    my $roes = $self->_get_coll_activity_roe()->cerca(
        customerId => $params{customerId},
        contractId => $params{contractId},
        ids => [$params{ID_ATTIVITA_ROE}],
        all => 1
    );

    return undef
        unless defined $roes;

    unless ( @{$roes} ) {
        $self->_art->last_error(__(q{No active ROEs found}));
        return;
    }

    my $roe = $roes->[0];

    # nella crea modificare logica in modo che non venga creata l'external_sync (sia per lc01 che lc02)
    my $roe_ap = $roe->activity_property();
    my $roe_sp = $roe->system_property();

    # provo a normalizzare l'armadio
    if (length($roe_ap->{CNO}) != 3){
        $roe_ap->{CNO} = sprintf("%03s", $roe_ap->{CNO});
    }

    # print STDERR Dumper $roe_ap;
    # print STDERR Dumper $roe_sp;

    if (defined $roe_ap->{ODS} && defined $params{ODS} && $params{ODS} ne ''){
    if ($roe_ap->{ODS} ne $params{ODS}) {
            $self->_art->last_error(sprintf("Inconsistenza sul valore dell'ODS (atteso \"%s\", trovato \"\%s\")",$roe_ap->{ODS}||'', $params{ODS}||''));
            return undef;
    }
    } else {
    $params{ODS} = $roe_ap->{ODS};  
    }
    if ($roe_ap->{CNO} ne $params{"cabinet"}){
        #$self->_art->last_error(sprintf("Inconsistenza sul valore del CNO (atteso \"%s\", trovato \"\%s\")",$roe_ap->{CNO}||'', $params{cabinet}||''));
        #return undef;
	$roe_ap->{CNO} = $params{"cabinet"};
    }

    my $map_pte_potential = {
        'ROE 16' => 'ROE 16',
        'ROE 16 (SOCRATE)' => 'ROE 16',
        'ROE 32' => 'ROE 32',
        'ROE 32 (SOCRATE)' => 'ROE 32',
        'ROE 48 MTCVO' => 'ROE 48',
        'ROE POZZETTO' => 'PTE 12',
        'PTE Small' => 'PTE Small (24)',
        'PTE Large' => 'PTE Large (48)',
    };

    my $map_pte_ubicazone = {
        '1' => 'MURO INTERNO',
        '01' => 'MURO INTERNO',
        '.01' => 'MURO INTERNO',
        'Interno' => 'INTERNO INGRESSO',
        'Colonnina' => 'COLONNINA O COLONNA',
        'Esterno' => 'MURO ESTERNO',
    };

    my $map_pte_tipo = {
        'Esterno' => 'Completo',
        'Interno' => 'Completo',
        'Pozzetto' => 'Da pozzetto',
        'Colonnina' => 'Optical Core',
        'ROE PTE Colonnina' => 'Optical Core',
        'ROE PTE Interno' => 'Completo',
        'ROE PTE Muro Esterno' => 'Completo',
        'ROE PTE Muro Interno' => 'Completo',
        'ROE PTE Sotterraneo' => 'Da pozzetto',
    };

    my $map_suspension_reason = {
        'Mancanza permesso privato' => 'KO - Permesso Privato',
        'Mancanza permesso Ente' => 'KO - Permesso Ente',
        'Problemi infrastruttura' => 'MigFibercop ROE - Problemi infrastruttura',
        'Altro' => 'MigFibercop ROE - Altro',
    };

    my %create_params = (
        migFibercop             => 1,
        elementType             => $params{elementType},
        id                      => $roe_ap->{ROE},
        cabinetType             => $params{cabinetType},
        cabinet                 => $roe_ap->{CNO},
        address                 => $roe_ap->{address},
        city                    => $roe_ap->{city},
        province                => $roe_ap->{province},
        copperDistributor       => $roe_ap->{copperDistributor},
        cluster                 => $params{cluster},
        RoId                    => $roe_ap->{RO},
        AOLId                   => $roe_ap->{AOL},
        centralId               => $roe_ap->{centralId},
        customerId              => $params{customerId},
        contractId              => $params{contractId},
        #WBELevel3               => $roe_ap->{WBELevel3}||$params{WBELevel3},
        WBELevel3               => $params{WBELevel3},
        cabinetWBELevel3        => $params{cabinetWBELevel3},
        PTEType                 => (defined $roe_ap->{ROEType} && $map_pte_tipo->{$roe_ap->{ROEType}}) ? $map_pte_tipo->{$roe_ap->{ROEType}} : $roe_ap->{ROEType},
        PTEPotential            => (defined $roe_ap->{potential} && $map_pte_potential->{$roe_ap->{potential}}) ? $map_pte_potential->{$roe_ap->{potential}} : $roe_ap->{potential},
        location                => (defined $roe_ap->{typology} && $map_pte_ubicazone->{$roe_ap->{typology}}) ? $map_pte_ubicazone->{$roe_ap->{typology}} : $roe_ap->{typology},
        PTEPosition             => $roe_ap->{ROEPosition},
        zipCode                 => $roe_ap->{zipCode},
        specialProject          => $roe_ap->{specialProject},
        CLLI                    => defined $roe_ap->{CLLI} ? $roe_ap->{CLLI} : $params{CLLI} || undef,
        UI                      => $roe_ap->{UI},
        seat                    => $roe_ap->{seat},
        customerAssistant       => $roe_ap->{customerAssistant},
        note                    => $roe_ap->{note},
        cabinetODS              => $params{cabinetODS},
        ring                    => $roe_ap->{ring},
        FORing                  => $roe_ap->{FORing},
        primarySplitter         => $roe_ap->{primarySplitter},
        secondarySplitterCount  => $roe_ap->{secondarySplitterCount},
        idCNO                   => $params{idCNO},
        networkBasedUpon        => $params{networkBasedUpon},
        ID_ATTIVITA_ROE         => $params{ID_ATTIVITA_ROE},
        teamAssistant           => $params{teamAssistant},
        clusterJobReport        => $params{clusterJobReport},
        penetrationIndex        => $params{penetrationIndex},
        fibercop                => 1,
    );

    my $lc02 = $self->crea( %create_params ) or return undef; # Quando il DTA migFibercop=1 LC02 non crea autonomamente un external sync

    my $update_roe_properties = {
        migFibercop    => 1,
        canOpenWorks   => 0,
        canOpenPermits => 0,
    };
    my $update_lc02_properties = {};
    my ( $id_intervento, $ultimo_progressivo );
    if ($params{ODS}){
        my $info = $self->_wpsocore->get_fc_info_by_ods( ODS => $params{ODS} ) or return;
        ( $id_intervento, $ultimo_progressivo ) = @{$info};
        unless ( defined $id_intervento ) {
            $self->_art->last_error(__x(q{Unable to recover id_intervento for Cabinet OdS: '{ODS}'}, ODS => $params{ODS}));
            return;
        }
        $update_lc02_properties->{externalId} = $id_intervento;
        $update_lc02_properties->{externalSequence} = $ultimo_progressivo;
        $update_roe_properties->{externalId} = undef;
    }

    my $external_sync;
    # Si sgancia l'external sync dal ROE e si aggancia a LC02 cambiando anche il sistema e gestendo externalSync
    # oppure lo si crea quando serve
    if ($roe->has_external_sync()){
        $external_sync = $roe->get_last_external_sync() or return;
        if ($roe->is_active() && !$external_sync->is_active()){
            #lo devo creare ne creerò uno nuovo
            undef $external_sync;
        }
    }
    if ( $external_sync ) {
        $roe->disinherit_children( CHILDREN => [$external_sync] ) or return;
        $lc02->adopt_children( CHILDREN => [$external_sync] ) or return;
        my $external_id = $external_sync->system_property('externalId');
        if ( defined $id_intervento && $id_intervento ne $external_id ) {
            $self->_art->last_error(__x(q{externalId found ({found}) inconsistent with expected ({expected})}, found => $id_intervento, expected => $external_id));
            return;
        }
        my $q = q{ update attivita set id_sistema = ? where id_attivita = ? };
        my $p = $self->_art->_create_prepare(__PACKAGE__ . 'UPDATE_ES', $q);
        my $r = $p->do($lc02->system->id,$external_sync->id);
        unless ( $r ) {
            $self->_art->last_error(__(q{Unable to update EXTERNAL_SYNC system}));
            return;
        }
        $update_lc02_properties->{externalId} = $external_id;
        
    } else {
        $external_sync = $lc02->add_external_sync(
            externalId => $update_lc02_properties->{externalId}
        ) or return;
#        # se il ROE era in aperta è anche necessario inviare la notice
#        if ($roe->get_current_status_name eq 'APERTA'){
            my $history = $lc02->history;
            return unless defined $history;
                
            my $last = $history->last;
            return unless defined $last;
            
            my $data = {
                EVENTO => $last->action_name
                ,ACTIVITY => to_json($lc02->dump())
            };
                
            return unless $lc02->sender_send_external_sync_notice(
                SOURCE_REF => $lc02->id,
                DATA => $data
            );
#        }
    }

    $roe->system->set_property( PROPERTIES => $update_roe_properties ) or return;

    my $roe_sospeso = $roe->get_current_status_name() eq 'SOSPESA' ? 1 : 0;
    # questo controllo viene fatt due volte per fare in modo che il ROE venga ristemato prima del LC02
    if ($roe->get_current_status_name() eq 'CHIUSA'){
        my $map = $lc02->remap_works_system_property();
        for my $w (@{$params{WORKS}}){
            $update_lc02_properties->{$map->{$w->{type}}.'OK'} = 1; #per costruzione in fase di migrazione avrò solo un lavoro per tipo
            $update_lc02_properties->{$map->{$w->{type}}.'LAS'} = 'ESPLETATO';
            $update_lc02_properties->{$map->{$w->{type}}.'Status'} = 'OK';
            
            $update_lc02_properties->{$map->{$w->{type}}.'LAMaker'} = $w->{maker};
            if ($w->{maker} eq 'Team'){
                $update_lc02_properties->{$map->{$w->{type}}.'LATeamId'} = $w->{workerId};
                $update_lc02_properties->{$map->{$w->{type}}.'LATeamName'} = $w->{workerName};
            } else {
                $update_lc02_properties->{$map->{$w->{type}}.'LASubContractCode'} = $w->{workerId};
                $update_lc02_properties->{$map->{$w->{type}}.'LASubContractName'} = $w->{workerName};
            }
        }
    } else {
        #$roe->park( DESCRIPTION => 'Parcheggiato a seguito migrazione Fibercop' ) or return;
    }

    if (keys %$update_lc02_properties){
        $lc02->system->set_property( PROPERTIES => $update_lc02_properties ) or return;
    }

    # gestisco gli stati che non possono essere gestiti in automatico con i lavori
    if ($roe_sospeso){
        $lc02->step(
            ACTION          => 'SOSPENSIONE',
            DESCRIPTION     => 'Portata in sospensione a seguito migrazione Fibercop',
            PROPERTIES => {
                suspendReason => $map_suspension_reason->{$roe_ap->{suspensionReasonROE}}
            }
        ) or return;
    }

    return $lc02;

}

sub crea {
    my $self = shift;
    my %params = @_;


    my $errmsg;
    $self->_art->last_error($errmsg)
        and return
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    elementType             => { isa => 'SCALAR' }
                    ,id                     => { isa => 'SCALAR' }
                    ,cabinetType            => { isa => 'SCALAR' }
                    ,cabinet                => { isa => 'SCALAR' }
                    ,address                => { isa => 'SCALAR' }
                    ,city                   => { isa => 'SCALAR' }
                    ,copperDistributor      => { isa => 'SCALAR' }
                    ,RoId                   => { isa => 'SCALAR' }
                    ,AOLId                  => { isa => 'SCALAR' }
                    ,centralId              => { isa => 'SCALAR' }
                    ,customerId             => { isa => 'SCALAR' }
                    ,contractId             => { isa => 'SCALAR' }
                    ,WBELevel3              => { isa => 'SCALAR' }
                    ,workingGroupCode       => { isa => 'SCALAR' }
                    ,PTEScope               => { isa => 'SCALAR', list => ['FTTH', 'Creation'] }
                }
                ,OPTIONAL   => {
                     province               => { isa => 'SCALAR' }
                    ,zipCode                => { isa => 'SCALAR' }
                    ,PTEType                => { isa => 'SCALAR', list => ['Optical Core','Completo','Da pozzetto'] }
                    ,PTEPosition            => { isa => 'SCALAR', list => ['Terminale', 'Passante'] }
                    ,location               => { isa => 'SCALAR', list => ['INTERNO INGRESSO','INTERNO CANTINE','INTERNO GARAGE','INTERNO CORTILE','MURO ESTERNO','MURO INTERNO','ANDRONE','SOTTOSCALA','CENTRALINO','PALO','SEMINTERRATO','MARCIAPIEDE','COLONNINA O COLONNA','POZZETTO'] }
                    ,specialProject         => { isa => 'SCALAR' }
                    ,CLLI                   => { isa => 'SCALAR' }
                    ,cluster                => { isa => 'SCALAR' }
                    ,UI                     => { isa => 'SCALAR' }
                    ,seat                   => { isa => 'SCALAR' }
                    ,customerAssistant      => { isa => 'SCALAR' }
                    ,PTEPotential           => { isa => 'SCALAR', list => ['PTE Large (48)','PTE Small (24)','PTE 12','ROE 16','ROE 32','ROE 48']}
                    ,note                   => { isa => 'SCALAR' }
                    ,ring                   => { isa => 'SCALAR' }
                    ,FORing                 => { isa => 'SCALAR' }
                    ,primarySplitter        => { isa => 'SCALAR' }
                    ,secondarySplitterCount => { isa => 'SCALAR' }
                    ,requestId              => { isa => 'SCALAR' }
                    ,requestorName          => { isa => 'SCALAR' }
                    ,migFibercop            => { isa => 'SCALAR' }
                    ,cabinetODS             => { isa => 'SCALAR' }
                    ,idCNO                  => { isa => 'SCALAR' }
                    ,networkBasedUpon       => { isa => 'SCALAR' }
                    ,ID_ATTIVITA_ROE        => { isa => 'SCALAR' }
                    ,teamAssistant          => { isa => 'SCALAR' }
                    ,planningFirm           => { isa => 'SCALAR' }
                    ,paymentNetwork         => { isa => 'SCALAR' }
                    ,clusterJobReport       => { isa => 'SCALAR' }
                    ,penetrationIndex       => { isa => 'SCALAR' }
                    ,workPerformance        => { isa => 'SCALAR' }
                    ,copperCabinet          => { isa => 'SCALAR' }
                    ,workOrderId              => { isa => 'SCALAR' }
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $cluster = WPSOCORE::Cluster->new(
        ART => $self->{ART},
        customerId => $params{customerId},
        contractId => $params{contractId},
    );

    my $workPerformance = WPSOCORE::WorkPerformance->new(
        ART => $self->{ART},
        customerId => $params{customerId},
        contractId => $params{contractId},
    );

    if ( defined $params{ID_ATTIVITA_ROE} ) {
        $params{ROEActivityId} = $params{ID_ATTIVITA_ROE};
        delete $params{ID_ATTIVITA_ROE};
    }

    $params{cabinet} = uc($params{cabinet});
    if ($params{cabinet} !~ /^[A-Z0-9]{3}$/) {
        $self->_art->last_error(__('Cabinet id must be a string of 3 alphanumeric characters'));
        $self->_logger->error( $self->_art->last_error );
        return;
    }
    if ( $params{elementType} eq 'ROE' ) {
        # verifica la presenza dei parametri obbligatori per i soli ROE
        for my $key ( qw(ring FORing primarySplitter secondarySplitterCount idCNO networkBasedUpon) ) {
            unless ( defined $params{$key} ) {
                $self->_art->last_error(__x(q{Missing mandatory parameter '{key}' for elementType '{elementType}'}, key => $key, elementType => $params{elementType}));
                return;
            }
        }
        # verifico che il parametro cabinetType sia consistente con elementType
        if ( $params{cabinetType} ne 'CNO' ) {
            $self->_art->last_error(__x(q{cabinetType must be 'CNO', not '{cabinetType}', for elementType '{elementType}'},
                cabinetType => $params{cabinetType},
                elementType => $params{elementType},
            ));
            return;
        }
        # verifico che il formato di CNO sia corretto
        $params{idCNO} = uc($params{idCNO});
        if ($params{idCNO} !~/^[A-Z0-9]{3}$/){
            $self->_art->last_error(__("idCNO must be a string of 3 alphanumeric characters"));
            $self->_logger->error( $self->_art->last_error );
            return;
        }
        # verifico che il valore di networkBasedUpon sia acccettabile
        unless ( grep { $params{networkBasedUpon} eq $_ } qw (ARMADIO CNO) ) {
            $self->_art->last_error(__x("Parameter networkBasedUpon must be either 'ARMADIO' or 'CNO', not '{value}'", value => $params{networkBasedUpon} ));
            $self->_logger->error( $self->_art->last_error );
            return;
        }
        # modifico l'ID del cabinet come da regole se networkBasedUpon = CNO
        if ( $params{networkBasedUpon} eq 'CNO' ) {
            $params{cabinet} = sprintf '%s/%s', $params{cabinet}, $params{idCNO};
        }
    } elsif ( $params{elementType}eq 'PTE' ) {
        # verifico che il parametro cabinetType sia consistente con elementType
        if ( $params{cabinetType} ne 'ARLO' ) {
            $self->_art->last_error(__x(q{cabinetType must be 'ARLO', not '{cabinetType}', for elementType '{elementType}'},
                cabinetType => $params{cabinetType},
                elementType => $params{elementType},
            ));
            return;
        }
        # verifico che il parametro idCNO non sia presente 
        if ( defined $params{idCNO} and $params{idCNO} ne '' ) {
            $self->_art->last_error(__x(q{Parameter 'idCNO' is not compatible with cabinetType '{cabinetType}'},
                cabinetType => $params{cabinetType},
            ));
            return;
        }
        # verifico che il parametro networkBasedUpon non sia presente 
        if ( defined $params{networkBasedUpon} and $params{networkBasedUpon} ne ''  ) {
            $self->_art->last_error(__x(q{Parameter 'networkBasedUpon' is not compatible with cabinetType '{cabinetType}'},
                cabinetType => $params{cabinetType},
            ));
            return;
        }
    }

    if ( defined $params{paymentNetwork} ) {
        $params{paymentNetwork}= sprintf '%012d', $params{paymentNetwork};
    }

    if ( defined $params{planningFirm} ) {
        my @citta = qw(roma vicenza);
        #Se planningFirm è definita dobbiamo essere a Roma o Vicenza (cluster C1)
        unless ( grep { lc($params{city}) eq $_ } @citta ) {
            $self->_art->last_error(__x(q{Parameter planningFirm is only allowed for the following cities: {city}},
                city => join(', ',map { ucfirst $_ } @citta),
            ));
            return;
        }
        #Se planningFirm è definita dev'essere presente nell'anagrafica fornitori
        $params{planningFirm} = sprintf q{%010d}, $params{planningFirm};
        my $subContract = $self->_subcontracts->cerca( companyCode => $params{planningFirm} );
        if ( @{$subContract} ) {
            $params{planningFirmDesc} = $subContract->[0]->{companyName};
        } else {
            $self->_art->last_error(__x(q{Planning Firm {planningFirm} was not found},
                planningFirm => $params{planningFirm},
            ));
            return;
        }
    }

    if ( defined $params{workPerformance} ){
        unless ( $workPerformance->check( workPerformance => $params{workPerformance} ) ) {
            $self->_art->last_error(__x(q{Invalid workPerformance {workPerformance}, valid values are {allowedWorkPerformances} },
                workPerformance => $params{workPerformance},
                allowedWorkPerformances => join(', ',@{$workPerformance->workPerformances}),
            ));
            return;
        }
    }

    my $savepoint = 'WPSOCORE_Cll_Act_LC02_crea';
    $self->_get_db->do( sprintf 'savepoint %s', $savepoint );

    # 1 - recupero dei dati tecnici calcolabili

    # aggiungo le coordinate se presenti i dati dell'indirizzo
    if (defined $params{city} && defined $params{address}){
        my $geo = $self->_wpsocore->get_geo_object;
        return unless $geo;

        # costruisco l'indirizzo in funzione dei parametri che ho in input
        my $address = $params{address};
        $address .= ' '.$params{zipCode} if defined $params{zipCode};
        $address .= ' '.$params{city} if defined $params{city};
        $address .= ' ('.$params{province}.')' if defined $params{province};

        my $location = $geo->query_by_address($address);

        my %map = (
            country                     => 'country',
            administrative_area_level_1 => 'region',
            # administrative_area_level_2 => 'province',
            # administrative_area_level_3 => 'city',
            # route                       => 'address',
            # street_number               => 'streetNumber',
            # postal_code                 => 'zipCode',
        );

        if (defined $location){
            $self->_logger->trace( Dumper({
                LOCATION => $location,
            }));
            if ($location->{partial_match}){
                $params{geoCoordinatesWarning} = 'Partial';
            }
            for my $comp ( @{$location->{address_components}} ) {
                for my $k ( keys %map ) {
                    if ( grep { $k eq $_ } @{$comp->{types}} ) {
                        $params{$map{$k}} = $comp->{short_name};
                    }
                }
            }
            # CS01 non prevede un campo numero civico separato dall'indirizzo
            # if ( defined $params{streetNumber} ) {
            #     $params{address} .= ' ' . $params{streetNumber};
            # }
            # delete $params{streetNumber};
            my $geo_location = $geo->get_geo_location(LOCATION => $location, FORMAT => 'STRING');
            return unless defined $geo_location;
            $params{geoCoordinates} = $geo_location;

        } else {
            $params{geoCoordinatesWarning} = $geo->last_error;
        }
    } else {
        $params{geoCoordinatesWarning} = 'No params to calculate coordinates';
    }

    my $companyAbbreviation;

    # aggiungo i dati desunti dalla WBE di terzo livello
    my $customers = $self->_get_customer_collection()->cerca(
        customerId => $params{customerId}
    );

    return undef unless (defined $customers);

    my $customer;
    if (scalar @{$customers} > 1){
        $self->_art()->last_error(__x("Find more than one customer for {customerId}", customerId => $params{customerId}));
        return undef;
    } elsif (scalar @{$customers} == 0){
        $self->_art()->last_error(__x("Unable to find customer for {customerId}", customerId => $params{customerId}));
        return undef;
    } else {
        $customer = $customers->[0];
    }

    my $collContract = eval {WPSOCORE::Collection::System::CONTRACT->new(ART => $self->_art, CUSTOMER => $customer)};
    if ($@){
        $self->_art->last_error($@);
        return;
    }

    my $contracts = $collContract->cerca(
        contractId => $params{contractId}
    );

    unless (defined $contracts){
        $self->_logger->error( $self->_art->last_error );
        return undef;
    }

    my $contract;
    if (scalar @{$contracts} > 1){
        $self->_art()->last_error(__x("Find more than one contract for {contractId}", contractId => $params{contractId}));
        return undef;
    } elsif (scalar @{$contracts} == 0){
        $self->_art()->last_error(__x("Unable to find contract for {contractId}", contractId => $params{contractId}));
        return undef;
    } else {
        $contract = $contracts->[0];
    }

    $companyAbbreviation = $contract->property('companyAbbreviation');   

    # recupero le info dalla Wbe
    my $wbes_info = $self->_wpsocore->get_fc_info_wbe_for_roe(
        customerId          => $params{customerId},
        WBELevel3           => $params{WBELevel3},
        workingGroupCode    => $params{workingGroupCode},
        companyAbbreviation => $companyAbbreviation,
        PTEScope            => $params{PTEScope},
    );
    unless (defined $wbes_info){
        $self->_logger->error( $self->_art->last_error );
        return;
    }
    if (scalar @{$wbes_info} == 0){
        $self->_art->last_error(__x("No wbe info found for wbe {wbe} and working group code {wgc}", wbe => $params{WBELevel3}, wgc => $params{workingGroupCode}));
        return;
    } elsif (scalar @{$wbes_info} > 1){
        # nel caso FTTH è bloccante,
        # nel caso CREATION non lo è perchè verrà eventualmente dedotto dopo in fase di aggancio
        # con la relativa network creation
        if ($params{PTEScope} eq 'FTTH'){
            $self->_art->last_error(__x("Found more than one wbe info for wbe {wbe} and working group code {wgc}", wbe => $params{WBELevel3}, wgc => $params{workingGroupCode}));
            return;
        } else { # se è un Cretion di default externalWorkTypeId a YS perchè tutti devono essere così
            $params{externalWorkTypeId} = 'YS';
        }
    } elsif (scalar @{$wbes_info} == 1){
        for my $k ('externalWorkTypeId','externalMacroActivityId','externalActivityId','externalAccountingType'){
            if (!defined $wbes_info->[0]->{$k}){
                $self->_art->last_error(__x("{param} not found for wbe: {wbe}", param => $k, wbe => $params{WBELevel3}));
                return;
            } else {
                $params{$k} = $wbes_info->[0]->{$k};
            }
        }

        for my $k ('externalMacroActivity','externalActivity'){
            $params{$k} = $wbes_info->[0]->{$k} if defined $wbes_info->[0]->{$k};
        }
        if ($params{externalMacroActivityId} eq 'FCOP2024'){
            $params{subcontractItem} = $self->_wpsocore()->get_subcontract_item_name(
                customerId  => $params{customerId},
                contractId  => $params{contractId},
                name        => $params{externalMacroActivityId},
                DETAILS     => \%params
            ); 
            return undef unless defined $params{subcontractItem};
        }
    }

    if (defined $params{workOrderId}){
        my $check_oda_wbe = $self->_wpsocore->check_oda_wbe_data(
            workOrderId         => $params{workOrderId},
            WBELevel3           => $params{WBELevel3},
            companyAbbreviation => $companyAbbreviation,
            externalWorkTypeId  => $params{externalWorkTypeId},
        );
        return undef
            unless defined $check_oda_wbe;

        unless ($check_oda_wbe){
            $self->_art->last_error(__x("Unable to find data for work order id {workOrderId} and wbe {wbe}", workOrderId => $params{workOrderId}, wbe => $params{WBELevel3}));
            return undef;
        }
    }
    
    # Faccio i vari check in funzione del PTEScope
    if ( $params{PTEScope} eq 'FTTH' ) {
        if ($params{externalAccountingType} =~/^(A CORPO|A CORPO 24)$/){
            for ('clusterJobReport', 'penetrationIndex'){
                unless ( defined $params{$_} ) {
                    $self->_art->last_error(__x(q{Parameter {param} must be present for a FTTH PTE with accounting {accounting}}, param => $_, accounting => $params{externalAccountingType}));
                    return;
                }
            }
            unless ( $cluster->check( clusterJobReport => $params{clusterJobReport} ) ) {
                $self->_art->last_error(__x(q{Invalid cluster {cluster}, valid values are {allowedClusters} },
                    cluster => $params{clusterJobReport},
                    allowedClusters => join(', ',@{$cluster->clusters}),
                ));
                return;
            }

            unless ( $cluster->checkPenetrationIndex( clusterJobReport => $params{clusterJobReport}, penetrationIndex => $params{penetrationIndex} )  ) {
                $self->_art->last_error(__x(q{Invalid penetrationIndex {penetrationIndex} for cluster {cluster}, valid values are {allowedPIs} },
                    penetrationIndex => $params{penetrationIndex},
                    cluster => $params{clusterJobReport},
                    allowedPIs => join(', ',@{$cluster->penetrationIndexes($params{clusterJobReport})}),
                ));
                return;
            }
            if ($params{clusterJobReport} eq 'FCop24' && $params{externalMacroActivityId} ne 'FCOP2024'){
                $self->_art->last_error(__x(q{Invalid externalMacroActivityId {externalMacroActivityId} for clusterJobReport {clusterJobReport}: valid value is {expected} },
                    clusterJobReport => $params{clusterJobReport},
                    externalMacroActivityId => $params{externalMacroActivityId},
                    expected => 'FCOP2024',
                ));
                return undef;
            }
            if ($params{clusterJobReport} ne 'FCop24' && $params{externalMacroActivityId} eq 'FCOP2024'){
                $self->_art->last_error(__x(q{Invalid clusterJobReport {clusterJobReport} for externalMacroActivityId {externalMacroActivityId}: valid value is {expected} },
                    clusterJobReport => $params{clusterJobReport},
                    externalMacroActivityId => $params{externalMacroActivityId},
                    expected => 'FCop24',
                ));
                return undef;
            }
        } else {
            for ('clusterJobReport', 'penetrationIndex'){
                if ( defined $params{$_} ) {
                    $self->_art->last_error(__x(q{Parameter {param} must not be present for a FTTH PTE with accounting {accounting}}, param => $_, accounting => $params{externalAccountingType}));
                    return;
                }
            }
        }
    } else {
        if ( defined $params{clusterJobReport} ) {
            $self->_art->last_error(__(q{Parameter clusteJobReport must not be present for a Creation PTE}));
            return;
        }
    }

    # aggiungo i dati desunti dalla centrale
    my $wgc_params = {
        customerId          => $params{customerId},
        externalWorkTypeId  => $params{externalWorkTypeId},
        centralId           => $params{centralId},
        companyAbbreviation => $companyAbbreviation,
    };

    $wgc_params->{externalMacroActivityId} = $params{externalMacroActivityId} if defined $params{externalMacroActivityId};

    my $workingGroupCodeList = $self->_wpsocore->get_fc_workingGroupCode_by_centralId(%{$wgc_params}) or return;

    $self->_art->last_error(__x("{param} not found for {param1} {value1}", param => 'workingGroupCode', param1 => 'centralId', value1 => $params{centralId}))
        && return
            unless @{$workingGroupCodeList};

    if ( @{$workingGroupCodeList} > 0 ) {
        if (!grep {$_ eq $params{workingGroupCode}} @{$workingGroupCodeList}) {
            $self->_art->last_error(__x("Working group code {value} not available for central with id {centralId}", value => $params{workingGroupCode}, centralId => $params{centralId}));
            return;
        }
    } else {
        $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
        $self->_art->last_error(__('no workingGroupCode found for central'));
        return;
    }

    # 2 - recupera l'attività LC01 corrispondente - se non esiste la crea (e a sua volta crea il sistema EL01, se non esiste)
    my %search_lc01_params = (
        id         => $params{cabinet},
        centralId  => $params{centralId},
        customerId => $params{customerId},
        contractId => $params{contractId},
    );
    $self->_logger->trace( Dumper( { LC01_SEARCH => \%search_lc01_params }));
    my $lc01;
    my $lc01s = $self->_get_coll_activity_lc01->cerca(%search_lc01_params) or return;
    if ( @{$lc01s} ) {
        $lc01 = $lc01s->[0];
        # l'armadio deve avere i centri di lavoro di tutti i pte (NB: se dovesse già averlo non è un problema)
        unless ($lc01->system->set_groups('CL_'.$params{workingGroupCode})){
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_logger->error( $self->_art->last_error );
            return;
        }
        # serve per alimentare ELK
        unless ($lc01->refresh()){
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_logger->error( $self->_art->last_error );
            return;
        }
    } else {
        my %act_lc01_params = (
            id                 => $params{cabinet},
            cabinetType        => $params{cabinetType},
            centralId          => $params{centralId},
            customerId         => $params{customerId},
            contractId         => $params{contractId},
            externalWorkTypeId => $params{externalWorkTypeId},
            workingGroupCode    => $params{workingGroupCode},
        );
        for my $key ( qw(networkBasedUpon) ) {
            $act_lc01_params{$key} = $params{$key} if defined $params{$key};
        }
        $self->_logger->trace( Dumper( { LC01_INPUT => \%act_lc01_params }));
        $lc01 = $self->_get_coll_activity_lc01->crea(%act_lc01_params);
        unless ( defined $lc01 ) {
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_logger->error( $self->_art->last_error );
            return;
        }
    }

    my $el01_property = $lc01->system_property;
    $self->_logger->trace( Dumper({
        EL01_PROPERTIES => $el01_property,
        LC01_PROPERTIES => $lc01->property,
    }));

    # 3 - crea il sistema EL02 se non esiste
    $params{id} =~ s{^\s+|\s+$}{}g;
    my $coll_system_el02 = WPSOCORE::Collection::System::EL02->new(ART => $self->{ART}, PARENT => $lc01->system);
    my %search_el02_params = (
        id         => $params{id},
        centralId  => $params{centralId},
        customerId => $params{customerId},
        contractId => $params{contractId},
    );
    # Se questo LC02 è un ROE il sistema dev'essere cercato anche per idCNO, che se non presente va defaultato a N/A
    if ( $params{elementType}eq 'ROE' and defined $params{idCNO} ) {
        $search_el02_params{idCNO} = $params{idCNO}
    }
    $self->_logger->trace( Dumper({
        EL02_SEARCH => \%search_el02_params,
    }));
    my $el02;
    my $el02s = $coll_system_el02->cerca(%search_el02_params) or return;
    if ( @{$el02s} ) {
        # Adesso il sistema EL02 è persistente
        #$self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
        #$self->_art->last_error(__x('EL02 system {id} already exists', id => $el02s->[0]->id));
        #return;
        $el02 = $el02s->[0];
        if ( $el02->property('wTestAppStatus') eq 'OK' and $el02->property('wTestOTDRStatus') eq 'OK' ) {
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_art->last_error(__x('EL02 system {id} already exists and it is tested', id => $el02->id));
            return;
        }
        # sbianco tutti i dati tecnici di lavori e permesi
        my $new_system_property = {};
        for my $key ( qw(
            wTestApp
            wTestOTDR
            wRestoration
            wPlanning
            wSurvey
            wInfrastructure
            wLaying
            wJunction
            wUpdateDatabaseF1
            wUpdateDatabaseF2
            privatePermits
        ) ) {
            $new_system_property->{$key.'Status'} = 'TODO';
            for ('LAMaker','LASubContractCode','LASubContractName','LATeamId','LATeamName','Total','Booking','BookingStatus','BookingWarning'){
                $new_system_property->{$key.$_} = undef;
            }
            if ($key eq 'privatePermits'){
                for ('ClosedKO','ClosedOK','OnGoing'){
                    $new_system_property->{$key.$_} = undef;
                }
            } else {
                for ('KO','LAS','OK','OnFieldStatus','SuspReason'){
                    $new_system_property->{$key.$_} = undef;
                }
            }
        }

        # sovrascrivo alcune chiavi "strutturali"
        for (
            'workOrderId',
            'copperCabinet',
            'externalMacroActivityId',
            'externalMacroActivity',
            'externalActivityId',
            'externalActivity',
            'externalActivity',
            'externalAccountingType',
            'WBELevel3',
            'paymentNetwork',
            'subcontractItem'
        ){
            $new_system_property->{$_} = $params{$_} if defined $params{$_};
        }

        unless ( $el02->set_property(PROPERTIES => $new_system_property)) {
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            return;
        }
    } else {
        my %sys_el02_params = (
            central => $el01_property->{central},
        );
        my $allowed_keys = $self->_art->enum_system_property(SYSTEM_TYPE_NAME => 'EL02');
        for my $key ( 'cabinetId', grep { $_ ne 'cabinet' } keys %params ) {
            next unless grep { $key eq $_ } keys %{$allowed_keys};
            if ( defined $params{$key} ) {
                $sys_el02_params{$key} = $params{$key};
            } elsif ( defined $el01_property->{$key} ) {
                $sys_el02_params{$key} = $el01_property->{$key};
            }
        }
        $self->_logger->trace( Dumper({
            EL02_INPUT => \%sys_el02_params,
        }));
        $el02 = $coll_system_el02->crea(%sys_el02_params);
        unless ( defined $el02 ) {
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_logger->error( $self->_art->last_error );
            return;
        }
    }
    $self->_logger->trace( Dumper({
        EL02_PROPERTIES => $el02->property,
    }));

    my $id_cno = defined $params{idCNO} && $params{idCNO} ne '' ? $params{idCNO} : 'N/A';
    my $description = sprintf '%s-%s-%s-%s',
        $params{centralId},
        $params{cabinet},
        $id_cno,
        $params{id},
    ;

    # 4 - crea l'attività LC02
    my %searchParam = (
        ACTIVE => 1,
        ACTIVITY_TYPE_NAME_EQUAL => 'LC02',
        SYSTEM_PROPERTIES_EQUAL => \%search_el02_params,
	DESCRIPTION_EQUAL => $description
    );
    $searchParam{SYSTEM_PROPERTIES_EQUAL}->{cabinet} = $el01_property->{id};

    #cerco le attività
    my $activity;
    my $activities = $self->_get_coll_activity->find_object(%searchParam) or return;
    if (@{$activities}) {
        $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
        $self->_art->last_error(__x('PTE with id {pte} already exists (activity {id})', pte => $search_el02_params{id}, id => $activities->[0]->id));
        return;
    } else {
        my $flag_skip_at = ( ( $lc01->get_current_status_name eq 'IN_CORSO' ) and ( !defined $lc01->activity_property('teamAssistant') ) );
        my $properties = {
            id                      => $params{id},
            elementType             => $params{elementType},
            cabinetId               => $el01_property->{cabinetId},
            address                 => $params{address},
            city                    => $params{city},
            province                => $params{province},
            zipCode                 => $params{zipCode},
            region                  => $params{region},
            country                 => $params{country},
            copperDistributor       => $params{copperDistributor},
            clusterJobReport        => $params{clusterJobReport},
            penetrationIndex        => $params{penetrationIndex},
            RoId                    => $params{RoId},
            AOLId                   => $params{AOLId},
            WBELevel3               => $params{WBELevel3},
            PTEType                 => $params{PTEType},
            PTEPosition             => $params{PTEPosition},
            location                => $params{location},
            specialProject          => $params{specialProject},
            CLLI                    => $params{CLLI},
            UI                      => $params{UI},
            seat                    => $params{seat},
            customerAssistant       => $params{customerAssistant},
            PTEPotential            => $params{PTEPotential},
            note                    => $params{note},
            ring                    => $params{ring},
            FORing                  => $params{FORing},
            primarySplitter         => $params{primarySplitter},
            secondarySplitterCount  => $params{secondarySplitterCount},
            requestId               => $params{requestId},
            requestorName           => $params{requestorName},
            workingGroupCode        => $params{workingGroupCode},
            migFibercop             => $params{migFibercop},
            planningFirm            => $params{planningFirm},
            planningFirmDesc        => $params{planningFirmDesc},
            PTEScope                => $params{PTEScope},
            flagSkipAT              => $flag_skip_at,
            externalAccountingType  => $params{externalAccountingType},
        };
        for (
            'cluster',
            'ROEActivityId',
            'workPerformance',
            'workOrderId',
            'copperCabinet',
            'teamAssistant',
            'externalMacroActivityId',
            'externalMacroActivity',
            'externalActivityId',
            'externalActivity',
            'subcontractItem',
        ){
            $properties->{$_} = $params{$_} if defined $params{$_};
        }

        my %create_params = (
            ACTIVITY_TYPE_NAME  => 'LC02'
            , ID_CUSTOM         => $self->_art->get_activity_next_id
            , DESCRIPTION       => $description
            , SYSTEM_ID         => $el02->id
            , PROPERTIES        => $properties
        );
        $activity = $self->_get_coll_activity->create (%create_params);
        unless (defined $activity){
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_logger->error( $self->_art->last_error );
            return;
        }
        $lc01->adopt_children(CHILDREN => [ $activity ]);
    }

    if (defined $params{paymentNetwork}){
        unless ($activity->step(
            ACTION => 'AGGIORNAMENTO_NETWORK',
            PROPERTIES => {
                paymentNetwork => $params{paymentNetwork}
            },
            DESCRIPTION => 'Aggancio PTE con network'
        )){
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            $self->_logger->error( $self->_art->last_error );
            return;
        }
    }

    return $activity;
}

sub cerca{
    my $self = shift;
    my %params = @_;
    my $errmsg;
    $self->_art->last_error($errmsg)
        and return
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    customerId  => { isa => 'SCALAR' }
                }
                ,OPTIONAL   => {
                    contractId => { isa => 'SCALAR' }
                    ,id              => { isa => 'SCALAR' }
                    ,ids            => { isa => 'ARRAY' }
                    ,cabinet        => { isa => 'SCALAR' }
                    ,cabinetId      => { isa => 'SCALAR' }
                    ,idCNO          => { isa => 'SCALAR' }
                    ,centralId      => { isa => 'SCALAR' }
                    ,paymentNetwork => { isa => 'SCALAR' }
                    ,onlyCount      => { isa => 'SCALAR', list => [0,1] }
                    ,ROEActivityId  => { isa => 'SCALAR' }
                    ,SORT           => { isa => 'ARRAY' }
                    ,LIMIT          => { isa => 'SCALAR' }
                    ,__FIND_ALL__   => { isa => 'SCALAR', list => [1] }
                    ,SHOW_NO_SUDO   => { isa => 'SCALAR' }
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $search_system_properties = {};
    my $search_activity_properties = {};
    for my $key ( qw( customerId contractId cabinet idCNO centralId ) ) {
        $search_system_properties->{$key} = $params{$key} if defined $params{$key};
    }

    for my $key ( qw( ROEActivityId id cabinetId paymentNetwork ) ) {
        $search_activity_properties->{$key} = $params{$key} if defined $params{$key};
    }

    my $searchParam = {
        ACTIVITY_TYPE_NAME_EQUAL => 'LC02',
        SYSTEM_PROPERTIES_EQUAL => $search_system_properties,
    };

    $searchParam->{ACTIVE} = 1 unless $params{__FIND_ALL__};

    $searchParam->{ID_IN} = $params{ids} if defined $params{ids};

    for ('SORT', 'LIMIT'){
        $searchParam->{$_} = $params{$_} if defined $params{$_};
    }

    if (keys %$search_activity_properties){
        $searchParam->{ACTIVITY_PROPERTIES_EQUAL} = $search_activity_properties
    }
    if ($params{SHOW_NO_SUDO}){
	$searchParam->{SHOW_NO_SUDO} = $params{SHOW_NO_SUDO};
    } else {
	$searchParam->{SHOW_ONLY_WITH_VISIBILITY} = 1;
    }

    #cerco le attività
    if ($params{onlyCount}){
        my $lc02 = $self->_get_coll_activity->find_id(%{$searchParam});

        unless (defined $lc02){
            $self->_logger->error( $self->_art->last_error );
            return;
        }

        return scalar @{$lc02};
    } else {
        my $lc02 = $self->_get_coll_activity->find_object(%{$searchParam});

        unless (defined $lc02){
            $self->_logger->error( $self->_art->last_error );
            return;
        }

        return $lc02;
    }
}

sub export_PteReport {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    regionId   => { isa => 'SCALAR' },
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $q_date = qq{
        select to_char(last_refresh_Date,'YYYYMMDDHH24MISS')
        from all_mview_analysis
        where owner = 'CORE_RPT' 
        and mview_name = 'MV_TIM_FIBERCOP_PTE'
    };
    my $p_date= $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_D', $q_date) or return;
    my $date = $p_date->fetch_minimalized();
    my $gruppi_utente = $self->_art->user->groups;
    my $q_types = "
	select column_name, data_type, column_id-1 COLUMN_ID
	from all_tab_cols
	where table_name = 'MV_TIM_FIBERCOP_PTE'
	and owner = 'CORE_RPT'
	and column_name not in ('regionId','ID_SISTEMA')
	order by column_id
	";
    my $p_types = $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_TYPES', $q_types) or return;
    my $results_types = $p_types->fetchall_hashref();

    my $types;
    for (@{$results_types}){
	$types->{$_->{COLUMN_NAME}} = {
		DATA_TYPE => $_->{DATA_TYPE},
		COLUMN_ID => $_->{COLUMN_ID}
	};
    }

    my @keys =  keys %{$types};

    my $q = '
        select distinct '.join (',', map {'"'.$_.'"'} @keys).'
        from    core_rpt.MV_TIM_FIBERCOP_PTE v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where v."regionId" = ?
            and ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $orig_nls_date_format = $self->_art->_dbh()->get_session_parameters('NLS_DATE_FORMAT');

    $self->_art->_dbh()->set_session_parameters(NLS_DATE_FORMAT => 'yyyy-mm-dd"T"hh24:mi:ss');
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_hashref($params{regionId},@$gruppi_utente);
    # ripristino
    $self->_art->_dbh()->set_session_parameters(NLS_DATE_FORMAT => $orig_nls_date_format->{NLS_DATE_FORMAT});

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;

    # scrivo header
    for (@keys){
	$wsh->write_string( 0, $types->{$_}->{COLUMN_ID}, $_ );
    }
    my $r = 1;
    
    
    my $date_format = $wbk->add_format( num_format => 'dd/mm/yyyy' );
    for my $line ( @{$results} ) {
	for my $cell (keys %$line){
		if (defined $line->{$cell}){
			if ($types->{$cell}->{DATA_TYPE} eq 'NUMBER'){
				$wsh->write_number($r, $types->{$cell}->{COLUMN_ID}, $line->{$cell});
			} elsif ($types->{$cell}->{DATA_TYPE} eq 'DATE'){
				#$wsh->write_string($r, $types->{$cell}->{COLUMN_ID}, $line->{$cell});
				$wsh->write_date_time($r, $types->{$cell}->{COLUMN_ID}, $line->{$cell}, $date_format);
			} else {
				$wsh->write_string($r, $types->{$cell}->{COLUMN_ID}, $line->{$cell});
			}
		} else {
			$wsh->write_blank($r, $types->{$cell}->{COLUMN_ID});
		}
	}
	$r++;
    }
    
    $wbk->close;
    
    return $date;
}

sub export_PteReport_Fascia_Rendicontazione {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    regionId   => { isa => 'SCALAR' },
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $q_date = qq{
        select to_char(last_refresh_Date,'YYYYMMDDHH24MISS')
        from all_mview_analysis
        where owner = 'CORE_RPT' 
        and mview_name = 'MV_PTE_FASCIA_RENDICONTAZIONE'
    };
    my $p_date= $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_D', $q_date) or return;
    my $date = $p_date->fetch_minimalized();
    my $gruppi_utente = $self->_art->user->groups;
    my $q = '
        select distinct "CLLI","Cluster","Fascia Rendicontazione","CENTRALE","ID ARMADIO","CITTA","LOTTO","ID WPSO", "CDL"
        from    core_rpt.MV_PTE_FASCIA_RENDICONTAZIONE v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where   v."regionId" = ? 
            and ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_arrayref($params{regionId},@$gruppi_utente);

    my $header = $p->{STH}->{NAME};

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;
    

    my $r;

    $wsh->write_row( $r++, 0, $header );
    

    for my $line ( @{$results} ) {
        $line = [map {decode_utf8($_)} @{$line}];
    
        $wsh->write_row( $r++, 0, $line );
    }
    
    $wbk->close;
    
    return $date;
}

sub export_PteReport_UpdateDB {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    regionId   => { isa => 'SCALAR' },
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $q_date = qq{
        select to_char(last_refresh_Date,'YYYYMMDDHH24MISS')
        from all_mview_analysis
        where owner = 'CORE_RPT' 
        and mview_name = 'MV_PTE_AGG_BD'
    };
    my $p_date= $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_D', $q_date) or return;
    my $date = $p_date->fetch_minimalized();
    my $gruppi_utente = $self->_art->user->groups;
    my $q = '
        select distinct "Citta","Centrale","ID Armadio","Distributore Rame","PTE","CLLI","ID_ODS","WBE","Indirizzo","STATO PTE","APPALTO GEN. CLLI","STATO GEN. CLLI","CAUSALE SOSP. GEN. CLLI","MOTIVO SOSPENSIONE GEN. CLLI","DATA ULTIMA VAR. GEN. CLLI","APPALTO AGG. CART.","STATO AGG. CART.","CAUSALE SOSP. AGG. CART.","MOTIVO SOSPENSIONE AGG. CART.","DATA ULTIMA VAR. AGG. CART.", "CDL"
        from core_rpt.MV_PTE_AGG_BD v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where   v."regionId" = ? 
            and ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'PTE_REPORT_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_arrayref($params{regionId},@$gruppi_utente);

    my $header = $p->{STH}->{NAME};

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;
    

    my $r;

    $wsh->write_row( $r++, 0, $header );
    

    for my $line ( @{$results} ) {
	for my $l (@{$line}){
		eval {decode_utf8($l)};
	}
    
        $wsh->write_row( $r++, 0, $line );
    }
    
    $wbk->close;
    
    return $date;
}

sub export_NetworkNoPTE {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $gruppi_utente = $self->_art->user->groups;
    my $q = '
        select distinct "CLLI", "networkId", "OA"
        from core_rpt.v_loader_test_data_ntw_no_pte v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'NTW_NO_PTE_REPORT_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_arrayref(@$gruppi_utente);

    my $header = $p->{STH}->{NAME};

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;
    
    my $r;

    $wsh->write_row( $r++, 0, $header );

    for my $line ( @{$results} ) {
	for my $l (@{$line}){
		eval {decode_utf8($l)};
	}
    
        $wsh->write_row( $r++, 0, $line );
    }
    
    $wbk->close;
    
    return 1;
}

sub export_testedNoData {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $gruppi_utente = $self->_art->user->groups;
    my $q = '
        select distinct ID_WPSO, "centralId", "cabinet", "id", "CLLI", "networkId"
        from core_rpt.v_loader_test_data_coll_no_data v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'TESTED_NO_DATA_REPORT_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_arrayref(@$gruppi_utente);

    my $header = $p->{STH}->{NAME};

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;
    
    my $r;

    $wsh->write_row( $r++, 0, $header );

    for my $line ( @{$results} ) {
	for my $l (@{$line}){
		eval {decode_utf8($l)};
	}
    
        $wsh->write_row( $r++, 0, $line );
    }
    
    $wbk->close;
    
    return 1;
}

sub export_notWorkableTested {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $gruppi_utente = $self->_art->user->groups;
    my $q = '
        select distinct ID_WPSO, "centralId", "cabinet", "id", "CLLI", "networkId"
        from core_rpt.V_LOADER_TEST_DATA_NOT_WORK_DATA v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'WORK_DATA_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_arrayref(@$gruppi_utente);

    my $header = $p->{STH}->{NAME};

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;
    
    my $r;

    $wsh->write_row( $r++, 0, $header );

    for my $line ( @{$results} ) {
	for my $l (@{$line}){
		eval {decode_utf8($l)};
	}
    
        $wsh->write_row( $r++, 0, $line );
    }
    
    $wbk->close;
    
    return 1;
}

sub export_noTestOldData {
    my $self = shift;
    my %params = @_;

    my $errmsg;
    $self->_art->last_error($errmsg)
        and return undef
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    FILE_TMP   => { isa => undef, inherits => [ 'GLOB' ] },
                    EXPORT_TYPE => { isa => 'SCALAR', list => [ 'zip'] },
                }
                ,OPTIONAL   => {
                    
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    my $gruppi_utente = $self->_art->user->groups;
    my $q = '
        select distinct ID_WPSO, "centralId", "cabinet", "id", "CLLI", "networkId"
        from core_rpt.V_LOADER_NO_TEST_OLD_DATA v
            join permission_sistemi ps on ps.id_Sistema=v.id_Sistema
        where ps.id_gruppo_abilitato in ('.(join (',',map{'?'} @$gruppi_utente)).')
    ';
    my $p = $self->_art->_create_prepare(__PACKAGE__ . 'NO_TEST_OLD_DATA_'.(scalar @$gruppi_utente), $q) or return;
    my $results = $p->fetchall_arrayref(@$gruppi_utente);

    my $header = $p->{STH}->{NAME};

    my $file_tmp = $params{FILE_TMP};

    my ( $csv, $wbk, $wsh );

    $wbk = Excel::Writer::XLSX->new($file_tmp);
    $wsh = $wbk->add_worksheet;
    $wsh->keep_leading_zeros;
    
    my $r;

    $wsh->write_row( $r++, 0, $header );

    for my $line ( @{$results} ) {
	for my $l (@{$line}){
		eval {decode_utf8($l)};
	}
    
        $wsh->write_row( $r++, 0, $line );
    }
    
    $wbk->close;
    
    return 1;
}

sub manage_network_association{
    my $self = shift;
    my %params = @_;
    my $errmsg;
    $self->_art->last_error($errmsg)
        and return
            unless $self->_art->check_named_params(
                 ERRMSG     => \$errmsg
                ,PARAMS     => \%params
                ,MANDATORY  => {
                    TYPE    => { isa => 'SCALAR', list => ['ASSOCIATE', 'DEASSOCIATE', 'CHANGE'] },
                    LC02    => { isa => 'REF', isa => 'API::ART::APP::Activity::LC02' }
                }
                ,OPTIONAL    => {
                    NETWORK             => { isa => 'REF', isa => 'API::ART::APP::Activity::NETWORK' },
                    paymentNetwork      => { isa => 'SCALAR' },
                    OLD_NETWORK         => { isa => 'REF', isa => 'API::ART::APP::Activity::NETWORK' },
                    oldPaymentNetwork   => { isa => 'SCALAR' },
                    workOrderId         => { isa => 'SCALAR' },
                    WBELevel3           => { isa => 'SCALAR' },
                    workingGroupCode           => { isa => 'SCALAR' },
                }
                ,IGNORE_EXTRA_PARAMS => 0
    );

    if ($params{TYPE} eq 'ASSOCIATE'){
        if (!defined $params{NETWORK} && !defined $params{paymentNetwork}){
            $self->_art()->last_error(__x("At least one param between {param} and {param1} must be defined", param => 'NETWORK', param1 => 'paymentNetwork'));
            return undef;
        }

        if (defined $params{NETWORK} && defined $params{paymentNetwork}){
            $self->_art()->last_error(__x("Onlyone param between {param} and {param1} must be defined", param => 'NETWORK', param1 => 'paymentNetwork'));
            return undef;
        }
    } elsif ($params{TYPE} eq 'DEASSOCIATE'){
        if (!defined $params{OLD_NETWORK} && !defined $params{oldPaymentNetwork}){
            $self->_art()->last_error(__x("At least one param between {param} and {param1} must be defined", param => 'OLD_NETWORK', param1 => 'oldPaymentNetwork'));
            return undef;
        }

        if (defined $params{OLD_NETWORK} && defined $params{oldPaymentNetwork}){
            $self->_art()->last_error(__x("Onlyone param between {param} and {param1} must be defined", param => 'OLD_NETWORK', param1 => 'oldPaymentNetwork'));
            return undef;
        }
    } elsif ($params{TYPE} eq 'CHANGE'){
        if (!defined $params{OLD_NETWORK} && !defined $params{oldPaymentNetwork}){
            $self->_art()->last_error(__x("At least one param between {param} and {param1} must be defined", param => 'OLD_NETWORK', param1 => 'oldPaymentNetwork'));
            return undef;
        }

        if (defined $params{OLD_NETWORK} && defined $params{oldPaymentNetwork}){
            $self->_art()->last_error(__x("Onlyone param between {param} and {param1} must be defined", param => 'OLD_NETWORK', param1 => 'oldPaymentNetwork'));
            return undef;
        }
    }

    my $result = {
        TYPE => $params{TYPE},
        LC02 => $params{LC02},
    };

    my $lc02 = $params{LC02};
    my $lc02Scope = $lc02->activity_property('PTEScope');
    my $networkToLink;

    # ci sono due gestioni leggermente diverse se è un PTE FTTH o un PTE Creation
    my $purpose = 'Gestione armadio';
    if (defined $params{NETWORK}){
        $networkToLink = $params{NETWORK};
    } elsif (defined $params{paymentNetwork}){
        # cerco se esiste la network
        my $cerca_params;
        $cerca_params = {
            customerId      => $lc02->system_property('customerId'),
            networkId       => $params{paymentNetwork}
        };
        my $networksToLink = $self->_get_coll_network->cerca(%$cerca_params);
        return undef unless defined $networksToLink;
        if (@{$networksToLink} > 1){
            $self->_art->last_error(__x('Too many active networks found with id {paymentNetwork}',
                paymentNetwork => $params{paymentNetwork},
            ));
            return undef;
        } elsif (@{$networksToLink} == 1){
            my $tmpNetworkToLink = $networksToLink->[0];
            my $networkCentralCabinet = $tmpNetworkToLink->system_property('centralCabinet');

            my $networkWBE = $tmpNetworkToLink->activity_property('WBELevel3');
            my $networkWGC = $tmpNetworkToLink->activity_property('workingGroupCode');
            # eseguo i vari controlli:
            # 1) la network non deve essere nello stato ANNULLATA
            if ($tmpNetworkToLink->get_current_status_name() eq 'ANNULLATA'){
                $self->_art->last_error(__x('Network {paymentNetwork} in status ({status}): unable to associate',
                    paymentNetwork   => $tmpNetworkToLink->activity_property('networkId'),
                    status      => $tmpNetworkToLink->get_current_status_name()
                ));
                return undef;
            }
            # 2) coerenza centrale
	        if (
                (
		    (defined $tmpNetworkToLink->activity_property('networkPurpose') && $tmpNetworkToLink->activity_property('networkPurpose') ne 'Gestione armadio')
                    ||
                    ($tmpNetworkToLink->activity_property('multiCabinet')||'NO') eq 'NO' # implicito $tmpNetworkToLink->activity_property('networkPurpose') eq 'Gestione armadio' ma non lo testo per efficienza
                )
                &&
                $tmpNetworkToLink->system_property('centralId') ne $lc02->parent()->system_property('centralId')
            ){
                $self->_art->last_error(__x('Network {paymentNetwork} not associated to the central: expected {expected}, found {found}',
                    paymentNetwork  =>  $tmpNetworkToLink->activity_property('networkId'),
                    expected         => $lc02->parent()->system_property('centralId'),
		            found => $tmpNetworkToLink->system_property('centralId')
                ));
                return undef;
		    }
            # 3) verifico per YS che la network sia di tipo Gestione armadio
            if ($lc02Scope ne 'Creation'){
                if (!defined $tmpNetworkToLink->activity_property('networkPurpose') || $tmpNetworkToLink->activity_property('networkPurpose') ne $purpose){
                    $self->_art->last_error(__x('Network {paymentNetwork} with purpose not equal to {purpose}',
                        paymentNetwork  =>  $tmpNetworkToLink->activity_property('networkId'),
                        purpose         =>  $purpose
                    ));
                    return undef;
                }
                if ($tmpNetworkToLink->system_property('contractId') ne 'FTTH'){
                    $self->_art->last_error(__x('Network {paymentNetwork} of wrong type: expected {expected}, found {found}',
                        paymentNetwork  =>  $tmpNetworkToLink->activity_property('networkId'),
                        expected         => 'FTTH',
                        found => $tmpNetworkToLink->system_property('contractId')
                    ));
                    return undef;
                }
            } else {
                if ($tmpNetworkToLink->system_property('contractId') ne 'CREATION'){
                    $self->_art->last_error(__x('Network {paymentNetwork} of wrong type: expected {expected}, found {found}',
                        paymentNetwork  =>  $tmpNetworkToLink->activity_property('networkId'),
                        expected         => 'CREATION',
                        found => $tmpNetworkToLink->system_property('contractId')
                    ));
                    return undef;
                }
            }
            # 4) verifico che il contractId sia quello atteso
            if ($tmpNetworkToLink->system_property('contractId') ne ($lc02Scope eq 'Creation' ? 'CREATION' : $lc02->system_property('contractId'))){
                $self->_art->last_error(__x('Network {paymentNetwork} with unexpected contract: found {found}, expected {expected}',
                    paymentNetwork  =>  $tmpNetworkToLink->activity_property('networkId'),
                    found           =>  $tmpNetworkToLink->activity_property('contractId'),
                    expected        =>  ($lc02Scope eq 'Creation' ? 'CREATION' : $lc02->system_property('contractId')),
                ));
                return undef;
            }

            # 5) deve avere la stessa WBE
            if ($networkWBE ne ($params{WBELevel3}||$lc02->activity_property('WBELevel3'))){
                $self->_art->last_error(__x('Network {paymentNetwork} with different WBE ({networkWBE}) than PTE ({PTEWBE})',
                    paymentNetwork   => $tmpNetworkToLink->activity_property('networkId'),
                    networkWBE  => $networkWBE,
                    PTEWBE      => $params{WBELevel3}||$lc02->activity_property('WBELevel3'),
                ));
                return undef;
            }
            # 6) devono essere sullo stesso centro di lavoro
            if ($networkWGC ne ($params{workingGroupCode}||$lc02->activity_property('workingGroupCode'))){
                $self->_art->last_error(__x('Network {paymentNetwork} with different working group code ({networkWGC}) than PTE ({PTEWGC})',
                    paymentNetwork   => $tmpNetworkToLink->activity_property('networkId'),
                    networkWGC  => $networkWGC,
                    PTEWGC      => ($params{workingGroupCode}||$lc02->activity_property('workingGroupCode')),
                ));
                return undef;
            }
            # 7) verifico se l'oda è compatibile
            if (defined $params{workOrderId} && $params{workOrderId} ne $tmpNetworkToLink->activity_property('workOrderId')){
                $self->_art->last_error(__x('Work order {workOrderId} not coherent with network ({paymentNetwork}) work order {networkWorkOrderId}',
                    workOrderId         => $params{workOrderId},
                    paymentNetwork      => $tmpNetworkToLink->activity_property('networkId'),
                    networkWorkOrderId  => $tmpNetworkToLink->activity_property('workOrderId'),
                ));
                return undef;
            }
            # 8) controllo che sulla network sia possibile effettuare l'azione AGGIORNAMENTO_ARMADIO
            if (!$tmpNetworkToLink->is_closed()){
                $tmpNetworkToLink->{__CALLER__} = __PACKAGE__;
                unless ($tmpNetworkToLink->can_do_action(NAME => 'AGGIORNAMENTO_ARMADIO')){
                    $self->_art->last_error(__x('Network {paymentNetwork} in status {status}: {error}',
                        paymentNetwork  => $tmpNetworkToLink->activity_property('networkId'),
                        status          => $tmpNetworkToLink->get_current_status_name(),
                        error           => $self->_art()->last_error() ne '' ? $self->_art()->last_error() : __("unable to update")
                    ));
                    return undef;
                }
            }
            # 9) non deve essere agganciato già ad un armadio diverso
            if (!defined $tmpNetworkToLink->activity_property('networkPurpose') || $tmpNetworkToLink->activity_property('networkPurpose') ne 'Gestione armadio'){
                if (scalar @{$networkCentralCabinet} && $networkCentralCabinet->[0] ne $lc02->parent()->system_property('centralId').'-'.$lc02->parent()->system_property('id')){
                    $self->_art->last_error(__x('Network {paymentNetwork} already associated to cabinet {cabinet}',
                        paymentNetwork   => $tmpNetworkToLink->activity_property('networkId'),
                        cabinet     => $networkCentralCabinet->[0],
                    ));
                    return undef;
                } elsif (scalar @{$networkCentralCabinet} && $networkCentralCabinet->[0] eq $lc02->parent()->system_property('centralId').'-'.$lc02->parent()->system_property('id') && $params{TYPE} =~/^(ASSOCIATE|CHANGE)$/){ #se � gi� agganciato all'armadio non devo fare nulla
                    if ($params{TYPE} =~/^(ASSOCIATE)$/){
                        $result->{NETWORK} = $tmpNetworkToLink;
                        return $result;
                    } else {
                        $networkToLink = $tmpNetworkToLink;
                    }
                } elsif(!scalar @{$networkCentralCabinet}){
                    $networkToLink = $tmpNetworkToLink;
                }
            } else {
                # se è multiCabinet deve essere univoco l'armadio
                if (
                    ($tmpNetworkToLink->activity_property('multiCabinet')||'NO') eq 'NO'
                ){
                    my @other_cab = grep { ($lc02->parent()->system_property('centralId').'-'.$lc02->parent()->system_property('id')) ne $_} @$networkCentralCabinet;
                    if (scalar @other_cab){
                        $self->_art->last_error(__x('Network {paymentNetwork} already associated to cabinet {cabinet}',
                            paymentNetwork  => $tmpNetworkToLink->activity_property('networkId'),
                            cabinet         => $other_cab[0],
                        ));
                        return undef;
                    }
                }
                # se la network è già associata all'armadio del PTE non deve essere fatto nulla sulla network
                if ($params{TYPE} =~/^(ASSOCIATE)$/ && defined $networkCentralCabinet && grep { $lc02->parent()->system_property('centralId').'-'.$lc02->parent()->system_property('id') eq $_} @{$networkCentralCabinet}){
                    $result->{NETWORK} = $tmpNetworkToLink;
                    return $result;
                } else {
                    $networkToLink = $tmpNetworkToLink;
                }
            }

            
            
        } else {
            $self->_art->last_error(__x('Network {paymentNetwork} not found',
                paymentNetwork   => $params{paymentNetwork}
            ));
            return undef;
        }
    }
    my $networkToDelink;
    if (defined $params{OLD_NETWORK}){
        $networkToDelink = $params{OLD_NETWORK};
    } elsif (defined $params{oldPaymentNetwork}){
        # cerco se esiste la network
        my $cerca_params = {
            customerId      => $lc02->system_property('customerId'),
            contractId      => $lc02Scope ne 'Creation' ? $lc02->system_property('contractId') : 'CREATION',
            networkId       => $params{oldPaymentNetwork}
        };
        $cerca_params->{networkPurpose} = $purpose if $lc02Scope eq 'FTTH';
        my $networksToDeLink = $self->_get_coll_network->cerca(%$cerca_params);
        return undef unless defined $networksToDeLink;
        if (@{$networksToDeLink} > 1){
            $self->_art->last_error(__x('Too many active networks found with id {paymentNetwork}',
                paymentNetwork => $params{oldPaymentNetwork},
            ));
            return undef;
        } elsif (@{$networksToDeLink} == 1){
            $networkToDelink = $networksToDeLink->[0];
        } else {
            $self->_art->last_error(__x('Network {paymentNetwork} not found',
                paymentNetwork   => $params{oldPaymentNetwork}
            ));
            return undef;
        }
    }

    my %associate_network = (
        ACTION => 'AGGIORNAMENTO_ARMADIO',
    );

    # se TYPE = ASSOCIATE è valido solo se la network non è associata o se è già associata allo stesso armadio
    if ($params{TYPE} ne 'CHANGE'){
        # verifico se la network passata è gestibile
        my $centralCabinetId = defined $networkToLink ? $networkToLink->system_property('centralCabinetId') : $networkToDelink->system_property('centralCabinetId');
        my $centralCabinet = defined $networkToLink ? $networkToLink->system_property('centralCabinet') : $networkToDelink->system_property('centralCabinet');
        if ($params{TYPE} eq 'ASSOCIATE'){
            if ( defined $networkToLink->activity_property('networkPurpose') && $networkToLink->activity_property('networkPurpose') ne 'Gestione armadio' && scalar @{$centralCabinetId} && $centralCabinetId->[0] ne $lc02->parent()->id()){
                $self->_art()->last_error(__x("Network {paymentNetwork} already associated to cabinet {cabinet}", paymentNetwork => $networkToLink->activity_property('networkId'), cabinet => $centralCabinet->[0]));
                return undef;
            } elsif (defined $networkToLink->activity_property('networkPurpose') && $networkToLink->activity_property('networkPurpose') eq 'Gestione armadio' && scalar @{$centralCabinetId} && grep {$_ eq $lc02->parent()->id()} @{$centralCabinetId}){
                $self->_art()->last_error(__x("Network {paymentNetwork} already associated to cabinet {cabinet}", paymentNetwork => $networkToLink->activity_property('networkId'), cabinet => $lc02->parent()->system_property('centralId').'-'.$lc02->parent()->system_property('id')));
                return undef;
            } else {
                if (defined $networkToLink->activity_property('networkPurpose') && $networkToLink->activity_property('networkPurpose') eq 'Gestione armadio'){
                    $associate_network{PROPERTIES} = {
                        cabinet => $lc02->parent()->system_property('centralId').'-'.$lc02->system_property('cabinet'),
                        cabinetId => $lc02->parent()->id()
                    };
                } else {
                    $associate_network{PROPERTIES} = {
                        cabinet => $lc02->parent()->system_property('centralId').'-'.$lc02->system_property('cabinet'),
                        cabinetId => $lc02->parent()->id()
                    };
                }
	    }
        } elsif ($params{TYPE} eq 'DEASSOCIATE'){
            # cerco se ci sono altri PTE ancora agganciati alla network
            my $lc02s = $self->cerca(
                customerId      => $networkToDelink->system_property('customerId'),
                contractId      => $lc02->system_property('contractId'),
                centralId       => $lc02->parent()->system_property('centralId'),
                cabinet         => $lc02->parent()->system_property('id'),
                paymentNetwork  => $networkToDelink->activity_property('networkId'),
                __FIND_ALL__ => 1,
            );

            return undef unless $lc02s;

            $result->{OLD_NETWORK} = $networkToDelink;
            # se c'è più di un PTE associato non devo fare nulla sulla network
            if (scalar @{$lc02s} > 1){
                return $result;
            } elsif (scalar @{$lc02s} == 1){
                # verifico che sia effettivamente lo stesso PTE altrimenti è un'anomalia
                if ($lc02s->[0]->id() != $lc02->id()){
                    $self->_art()->last_error(__x("Anomaly => incoherent PTE id: expected {exp}, found {found}", exp => $lc02->id(), found => $lc02s->[0]->id()));
                    return undef;
                }
            } else { # scalar @{$lc02s} == 0
                # non dovrebbe mai succedere ma nell'eventualità lo considero gestibile
                return $result;
            }

            $associate_network{PROPERTIES} = {
                cabinet     => $lc02->parent()->system_property('centralId').'-'.$lc02->system_property('cabinet'),
                cabinetId   => $lc02->parent()->id(),
                __DEASSOCIATE__    => $JSON::true
            };

        }
    } elsif ($params{TYPE} eq 'CHANGE'){
        # se cambia l'ora e il PTE ha già l'assistente tecnico allora non si può fare
        # ma sarà aperto un ServiceMe
        if (defined $lc02->activity_property('teamAssistant') && $networkToDelink->system_property('workOrderId') ne $networkToDelink->system_property('workOrderId')){
            $self->_art->last_error(__('Unable to change network on pte already take in charge when old work order id is different than new work order id'));
            return undef;
        }
        return undef
            unless defined $self->manage_network_association(
                TYPE => 'DEASSOCIATE',
                OLD_NETWORK => $networkToDelink,
                LC02 => $lc02,
            );
        $result->{OLD_NETWORK} = $networkToDelink;
        return undef
            unless defined $self->manage_network_association(
                TYPE => 'ASSOCIATE',
                #NETWORK => $networkToLink,
                paymentNetwork => $networkToLink->activity_property('networkId'),
                LC02 => $lc02,
                workOrderId                     => $params{workOrderId}||$lc02->activity_property('workOrderId'),
                WBELevel3                       => $params{WBELevel3}||$lc02->activity_property('WBELevel3'),
                workingGroupCode        => $params{workingGroupCode}||$lc02->activity_property('workingGroupCode'),
            );
        $result->{NETWORK} = $networkToLink;
    }

    if ($params{TYPE} eq 'ASSOCIATE'){
        # se è chiuso diventa una virtual action sullo stato finale
        if ($networkToLink->is_closed()){
            $associate_network{VIRTUAL} = 1;
            $associate_network{DEST_STATUS} = $networkToLink->get_current_status_name();
            $associate_network{IGNORE_FINAL_STATUS} = 1;
        }
        $networkToLink->{__CALLER__} = __PACKAGE__;
        my $savepoint = 'NETWORK_ASSOCIATE_'.$networkToLink->id();
        $self->_get_db->do( sprintf 'savepoint %s', $savepoint );
        unless ($networkToLink->step(%associate_network)){
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            return undef;
        }
        $result->{NETWORK} = $networkToLink;
    } elsif ($params{TYPE} eq 'DEASSOCIATE'){
        # se è chiuso diventa una virtual action sullo stato finale
        if ($networkToDelink->is_closed()){
            $associate_network{VIRTUAL} = 1;
            $associate_network{DEST_STATUS} = $networkToDelink->get_current_status_name();
            $associate_network{IGNORE_FINAL_STATUS} = 1;
        }
        $networkToDelink->{__CALLER__} = __PACKAGE__;
        my $savepoint = 'NETWORK_DEASSOCIATE_'.$networkToDelink->id();
        $self->_get_db->do( sprintf 'savepoint %s', $savepoint );
        unless ($networkToDelink->step(%associate_network)){
            $self->_get_db->do( sprintf 'rollback to savepoint %s', $savepoint );
            return undef;
        }
        $result->{OLD_NETWORK} = $networkToDelink;
    }

    return $result;
} 

if (__FILE__ eq $0) {

    use API::ART;

    my $log_level = 'DEBUG';

    Log::Log4perl::init(\"
        log4perl.rootLogger = $log_level, Screen
        log4perl.appender.Screen = Log::Dispatch::Screen
        log4perl.appender.Screen.stderr = 0
        log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
        log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
    ");

    my $art;
    eval {
        $art = API::ART->new(
            ARTID => $ENV{ARTID},
            PASSWORD => 'pippo123',
            USER => 'root',
            DEBUG => 0
        );
    };
    if ($@) {
        die "Login error: $@";
    }

    my $coll = WPSOCORE::Collection::Activity::LC02->new(ART => $art);
    $coll->_logger->level($TRACE);

=pod
    my $aps = $coll->crea(
        id                      => '4',
        elementType             => 'ROE',
        cabinet                 => '010',
        cabinetType             => 'CNO',
        idCNO                   => '007',
        networkBasedUpon        => 'CNO',
        cluster                 => 'LOTTO_01',
        contractId              => 'FTTH',
        customerId              => 'TIM',
        centralId               => '74401F',
        WBELevel3               => 'WWTI060IT0013',
        cabinetWBELevel3        => 'WWTI066IT0199',
        address                 => 'via roma 31',
        city                    => 'Paladina',
        province                => 'BG',
        zipCode                 => '24030',
        copperDistributor       => '05102D_802/1',
        RoId                    => 'S2',
        AOLId                   => 'SIC-W',
        ring                    => '74',
        FORing                  => '136\'',
        primarySplitter         => 'S004(8) dd 136',
        secondarySplitterCount  => '1(8) S004 u1',
        cabinetODS              => '001000078286',
        ID_ATTIVITA_ROE         => 54586,
        migFibercop             => 1,
    );
=cut

    my $aps = $coll->crea(
        contractId              => 'FTTH',
        customerId              => 'TIM',
        id                      => '02',
        elementType             => 'PTE',
        cabinet                 => '089',
        cabinetType             => 'ARLO',
        address                 => 'VIA AMEDEO OBICI 2',
        city                    => 'ODERZO',
        province                => 'TV',
        zipCode                 => '31046',
        copperDistributor       => '42204G_15/5',
        cluster                 => 'FVG',
        RoId                    => 'NE',
        AOLId                   => 'FVG',
        centralId               => '01101E',
        PTEType                 => 'Completo',
        PTEPosition             => 'Terminale',
        location                => 'MURO ESTERNO',
        WBELevel3               => 'TETI066IT0007',
        workingGroupCode        => '496657',
        penetrationIndex        => 50,
        clusterJobReport        => 'FCop24',
        PTEScope                => 'FTTH',
        #paymentNetwork          => '0005005629592'
        #paymentNetwork               => '0005005629591'
        #paymentNetwork          => '000500951363',
        #primarySplitter         => 'S004(8) dd 136',
        #secondarySplitterCount  => '1(8) S004 u1',
        #cabinetODS              => '001000078286',
    );

=pod
    my $aps = $coll->crea_migFibercop(
        elementType             => 'ROE',
        cabinet                 => '001',
        cabinetType             => 'CNO',
        idCNO                   => '007',
        networkBasedUpon        => 'ARMADIO',
        cluster                 => 'LOTTO_01',
        contractId              => 'FTTH',
        customerId              => 'TIM',
        WBELevel3               => 'WWTI060IT0013',
        cabinetWBELevel3        => 'WWTI066IT0199',
        cabinetODS              => '001000078286',
        ODS                     => '001000078286',
        ID_ATTIVITA_ROE         => 54586,
    );
=cut

    if( defined $aps) {
        # warn Dumper( {
        #     SYSTEM_PROPERTIES => $aps->system_property,
        #     ACTIVITY_PROPERTIES => $aps->activity_property,
        #     STATO_CORRENTE => $aps->get_current_status_name,
        # } );
        get_logger->info("OK: Creata attivita ".ref($aps)." ".$aps->id);
        get_logger->info("OK: Armadio - " . $aps->parent->id);
        get_logger->info("OK: PTE activity_property- " . Dumper ($aps->activity_property()));
    } else {
        get_logger->error("Error: " . $art->last_error);
	exit;
    }

    # if ($aps->step(ACTION => 'AGGIORNAMENTO_DATI',PROPERTIES => {
    #     id => $aps->activity_property('id'),
    #     penetrationIndex => $aps->activity_property('penetrationIndex'),
    #     WBELevel3 => $aps->activity_property('WBELevel3'),
    #     workingGroupCodeChoice => $aps->activity_property('workingGroupCode'),
    #     workOrderId => '4520808366',
    # })){
    #     get_logger->info("OK: Azione AGGIORNAMENTO_DATI ".$aps->get_current_status_name);
    # } else {
    #     get_logger->error("Error: " . $art->last_error);
    # }

    # if ($aps->step(ACTION => 'PRESA_IN_CARICO')){
    #     get_logger->info("OK: Azione PRESA_IN_CARICO ".$aps->get_current_status_name);
    # } else {
    #     get_logger->error("Error: " . $art->last_error);
    # }

    if ($ARGV[0]){
        $art->save;
    } else {
        $art->cancel;
    }
}

1;
