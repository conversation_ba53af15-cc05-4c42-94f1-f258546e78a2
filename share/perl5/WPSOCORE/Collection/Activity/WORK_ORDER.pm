package WPSOCORE::Collection::Activity::WORK_ORDER;

use strict;
use warnings;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use JSON;
use Email::Valid;

use API::ART::Collection::Activity;

use WPSOCORE;
use WPSOCORE::Collection::System::WORK_ORDER;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub new {
	my $this  = shift;
	my $class = ref($this) || $this;
	my $params = {@_};
	
	my $self = bless( {}, $class );
		
	# Controlli sui parametri
	die(__x('Missing mandatory param {paramname}', paramname => 'ART')) unless defined $params->{ART};
	die(__x('Param {paramname} must be of type {type}', paramname => 'ART', type => 'API::ART')) if ref($params->{ART}) ne 'API::ART';
	
	$self->{ART} = $params->{ART};
	
	$self->{LOGGER} = Log::Log4perl->get_logger( 'WPSOCORE::LIB::' . __PACKAGE__ );
	
	$self->{WPSOCORE} = WPSOCORE->new(ART => $self->{ART});
	
	$self->{CollActivity} = API::ART::Collection::Activity->new(ART => $self->{ART});

	$self->{CollWorkOrder} = WPSOCORE::Collection::System::WORK_ORDER->new(ART =>  $self->{ART});
	
	return bless( $self, $class );
}

sub _art{shift->{ART}}

sub _logger { shift->{LOGGER} }

sub _get_db{shift->_art()->_dbh()}

sub _wpsocore { shift->{WPSOCORE} }

sub _get_coll_activity{shift->{CollActivity}}

sub _get_coll_work_order{shift->{CollWorkOrder}}

sub crea{
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					customerId			=> { isa => 'SCALAR' },
					workOrderId			=> { isa => 'SCALAR' },
					WBELevel3			=> { isa => 'SCALAR' },
				}
				,OPTIONAL	=> {
					contractId				=> { isa => 'SCALAR' },
					externalActivity		=> { isa => 'SCALAR' },
					externalActivityId		=> { isa => 'SCALAR' },
					externalMacroActivity	=> { isa => 'SCALAR' },
					externalMacroActivityId	=> { isa => 'SCALAR' },
					externalWorkTypeId		=> { isa => 'SCALAR' },
					waitingNetworks			=> { isa => 'SCALAR' },	
					workOrder				=> { isa => 'SCALAR' },
					workOrderReady			=> { isa => 'SCALAR' },
					externalAccountingType	=> { isa => 'SCALAR' },
					companyAbbreviation	=> { isa => 'SCALAR' },
				}
				,IGNORE_EXTRA_PARAMS => 1
	);

	# verifico se esiste già il sistema workOrder
	my $work_orders = $self->_get_coll_work_order->cerca(
		customerId			=> $params{customerId}, 
		workOrderId			=> $params{workOrderId},
		WBELevel3			=> $params{WBELevel3},
	);

	return undef unless defined $work_orders;

	# default
	$params{workOrderReady} = 'N';

	my $savepoint = 'WPSOCORE_Cll_Act_WO_crea';
	my $workOrder_system;
	$self->_get_db()->do( "savepoint $savepoint" );

	if (scalar @{$work_orders}>1){
		$self->_art()->last_error(__x("Found more than one system from customerId {customerId}, workOrderId {workOrderId} and WBELevel3 {WBELevel3}",
			customerId => $params{customerId}, 
			workOrderId			=> $params{workOrderId},
			WBELevel3			=> $params{WBELevel3},
		));
		return undef;
	} elsif (scalar @{$work_orders} == 1){
		$workOrder_system = $work_orders->[0];
	} else {
		$workOrder_system = $self->_get_coll_work_order->crea(
			customerId				=> $params{customerId},
			workOrderId				=> $params{workOrderId},
			waitingNetworks			=> $params{waitingNetworks},
			companyAbbreviation		=> $params{companyAbbreviation},
			contractId				=> $params{contractId},
			workOrder				=> $params{workOrder},
			WBELevel3				=> $params{WBELevel3},
			externalWorkTypeId		=> $params{externalWorkTypeId},
			externalMacroActivityId	=> $params{externalMacroActivityId},
			externalMacroActivity	=> $params{externalMacroActivity},
			externalAccountingType	=> $params{externalAccountingType},
			workOrderReady			=> $params{workOrderReady},
		);
		unless (defined $workOrder_system){
			$self->_get_db()->do( "rollback to savepoint $savepoint" );
			return undef;
		}
	}

	my $workOrder = $self->_get_coll_activity->create(
		DESCRIPTION			=> $params{workOrderId},
		SYSTEM_ID			=> $workOrder_system->id(),
		ACTIVITY_TYPE_NAME	=> 'WORK_ORDER',
		PROPERTIES			=> {
			customerId				=> $params{customerId},
			workOrderId				=> $params{workOrderId},
			companyAbbreviation		=> $params{companyAbbreviation},
			contractId				=> $params{contractId},
			workOrder				=> $params{workOrder},
			WBELevel3				=> $params{WBELevel3},
			externalWorkTypeId		=> $params{externalWorkTypeId},
			externalMacroActivityId	=> $params{externalMacroActivityId},
			externalMacroActivity	=> $params{externalMacroActivity},
			externalActivityId		=> $params{externalActivityId},
			externalActivity		=> $params{externalActivity},
			externalAccountingType	=> $params{externalAccountingType},
			workOrderReady			=> $params{workOrderReady},
		}
	);
	unless (defined $workOrder){
		$self->_get_db()->do( "rollback to savepoint $savepoint" );
		return undef;
	}

	return $workOrder;
}

sub cerca{
	my $self = shift;
	my %params = @_;
	
	my $errmsg;
	$self->_art()->last_error($errmsg)
		and return undef
			unless $self->_art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
					"customerId"	=> { isa => 'SCALAR' },
					"workOrderId"	=> { isa => 'SCALAR' }
				}
				,OPTIONAL	=> {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	
	my $searchParam = {
		ACTIVITY_TYPE_NAME_EQUAL => 'WORK_ORDER'
		,ACTIVITY_PROPERTIES_EQUAL => {
			customerId	=> $params{customerId},
			#workOrderId	=> $params{workOrderId},
		}
		,LIMIT => 1
		,DESCRIPTION_EQUAL => $params{workOrderId}
		,SORT => [{ACTIVITY_ID => -1}]
	};
	
	#cerco le attività
	my $workOrders = $self->_get_coll_activity()->find_object(%{$searchParam});
	
	unless (defined $workOrders){
		$self->_logger()->error( $self->_art()->last_error() );
		return undef;	
	}
	
	return $workOrders;
}

if (__FILE__ eq $0) {

	use API::ART;
	
	my $log_level = 'DEBUG';

	Log::Log4perl::init(\"
		log4perl.rootLogger = $log_level, Screen
		log4perl.appender.Screen = Log::Dispatch::Screen
		log4perl.appender.Screen.stderr = 0
		log4perl.appender.Screen.layout = Log::Log4perl::Layout::PatternLayout
		log4perl.appender.Screen.layout.ConversionPattern = \%p> \%m\%n
	");

	my $art;
	eval {
		$art = API::ART->new(
			ARTID => $ENV{ARTID},
			PASSWORD => 'pippo123',
			USER => 'root',
			DEBUG => 0
		);
	};
	if ($@) {
		die "Login error: $@";
	}

	my $coll = WPSOCORE::Collection::Activity::MANUTENZIONE_CORRETTIVA->new(ART => $art);
	
	# creazione interna SIRTI
	# my $aps = $coll->crea(
	# 	"contractId" 		=> 'RTN_MNT'
	# 	,"customerId" 		=> 'TIM'
	# 	,"description"		=> 'Descrizione'
	# 	,"workingGroupCode"	=> '106457'
	# 	,"directiveId"		=> 51568
	# 	,"sectionId"		=> 51581
	# 	,"cableIds"			=> [51589,51588]
	# 	,"reason"			=> 'Roditori'
	# 	,"mntActivityType"	=> 'Infrastruttura'
	# 	,"reporter"			=> 'Telecom'
	# 	,"defectDate"		=> '2019-01-01'
	# 	#,"defectStreet"		=> 'Via Piemonte'
	# 	,"defectLocation"	=> 'Mantova'
	# );

	# creazione diretta da utente TIM
	my $aps = $coll->crea(
		"contractId" 		=> 'RTN_MNT'
		,"customerId" 		=> 'TIM'
		,"directiveId"		=> 51568
		,"reporter"			=> 'TIM'
		, oa => 'Centro'
		, exRO => 'C'
		, mntRegion => 'Lazio'
		, customerCentralA => 'ARCEITAG - COLLE NOCE'
		, customerCentralZ => 'ARCEITAH - ARCE'
		, mntPlant => 'FROSITBC/PIMGITAG/I-FOS/00001'
		, mntPlantCode => '0000 1057'
        , workingGroupCode => '106457'
		, failedCablesNumber => '1'
		, customerContact => 'Fra Riz'
		, customerEmail => '<EMAIL>'
		, customerRTNEmail => '<EMAIL>'
        , ATTACHMENTS      => []
	);
	
	if( defined $aps) {
		get_logger()->info("OK: Creata attivita ".ref($aps)." ".$aps->id());
		print STDERR Dumper $aps->system_property();
		print STDERR Dumper $aps->property();
		#print STDERR "\n children_count = ".$aps->children_count()."\n";
		#print STDERR Dumper $aps->attachment_list();
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
=pod
	### vale solo per creazione diretta da utente TIM
	if ($aps->step(
		'ACTION' => 'INIZIO_LAVORAZIONE',
		'PROPERTIES' => {
			## obbligatori
			reason				=> 'Doloso'
			,defectDate			=> '2019-10-10'
			,sectionId			=> '51581'
			,cableIds			=> ['51592']
			## opzionali
			,defectLocation		=> 'Milano'
			,defectStreet		=> 'Via Stamira d\'Ancona'
			,mntOperationType	=> 'Definitivo'
		}
	)){
		get_logger()->info("OK: Step attivita ".$aps->id()." OK (".$aps->get_current_status_name().")");
		print STDERR Dumper $aps->system_property();
		print STDERR Dumper $aps->property();
		#print STDERR "\n children_count = ".$aps->children_count()."\n";
		#print STDERR Dumper $aps->attachment_list();
	} else {
		get_logger()->error("Error: " . $art->last_error());
		die;
	}
=cut
	
	if ($ARGV[0]){
		$art->save();
	} else {
		$art->cancel();
	}
	
}

1;
