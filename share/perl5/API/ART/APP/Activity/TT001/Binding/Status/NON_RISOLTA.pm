package API::ART::APP::Activity::TT001::Binding::Status::NON_RISOLTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use Template;
use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
    
    $activity->system()->end();
	return 1;
};

1;
