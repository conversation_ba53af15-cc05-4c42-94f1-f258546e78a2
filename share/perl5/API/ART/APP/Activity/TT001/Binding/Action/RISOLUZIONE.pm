package API::ART::APP::Activity::TT001::Binding::Action::RISOLUZIONE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use File::Temp qw(tempdir);
use Template;
use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub post {

	my ( $self, $activity, $params ) = @_;
		
	my $art = $self->{ART};

	my $user = $art->user()->name();
	# serve per evitare l'invio mail in fase di caricamento iniziale
	return 1 if $user eq uc $ENV{WPSOAP_SCRIPT_USER};

	my $actProps = $art->enum_activity_property(
			ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
			EXTENDED_OUTPUT => 1
		);
		return undef unless defined $actProps;
	my $errmsg;

	# Gestione email
	my $username = $activity->info('CREATION_USER_NAME');
	my @cc = $ENV{TT001_RECIPIENT_MAIL};
	my @email_to;

	if ($username ne uc $ENV{WPSOAP_SCRIPT_USER}){
		# recupero email to (utente che ha aperto il ticket WPSO)
		my $to = $activity->get_teams()->cerca(
			username	=> $username,
		);
		return undef
			unless defined $to;
		# Se non lo trovo,cerco negli EXT
		if (scalar @{$to}){
			push @email_to, $to->[0]->{"email"};
		} else {
			$to = $activity->get_subContracts()->get_email_from_username(
				username	=> $username,
			);
			return undef
				unless defined $to;
			push @email_to, $to->{"email"} if $to ne '';
		}
		$art->last_error(__x("Unable to find email for Username {username} ", username => $username))
			&& return undef
				unless scalar @email_to;
	}

	# recupero email AT
	my $atEmail = $activity->get_teams()->cerca(
		companyCard	=> $activity->activity_property('technicalAssistantId'),
	);
	if (defined $atEmail->[0]->{email} && $atEmail->[0]->{email} ne '') {
		push @cc, $atEmail->[0]->{email};
	}

	# recupero email ClusterManager
	if (defined $activity->activity_property('clusterManagerId')){
		my $clusterManagerEmail = $activity->get_teams()->cerca(
			companyCard	=> $activity->activity_property('clusterManagerId'),
		);
		return undef
			unless defined $clusterManagerEmail;
		$art->last_error(__x("Unable to find email for {label}:{clusterManagerId}",label => $actProps->{clusterManagerId}->{LABEL}, clusterManagerId => $activity->activity_property('clusterManagerId')))
			&& return undef
				unless scalar @{$clusterManagerEmail};
		push @cc, $clusterManagerEmail->[0]->{email} if $clusterManagerEmail->[0]->{email};
	}

	### Creazione struttura email ###
	my $template = {};
	# Definizione del percorso del template
	my $templatePath = $ENV{ETC}.'/templates/TT001/email_annullata.tt2';

	# Subject
	my $subject = sprintf(
	"WPSO - TT001 - %s - %s",
	$activity->id(),
	$activity->system()->info('SYSTEM_NAME'),
	);

	$template->{SUBJECT} = $subject;

	# Dati per il template
	my $actVal = $activity->activity_property();
	my $orig_default_date_format = $art->get_default_date_format;
	$art->set_default_date_format('dd/mm/yyyy hh24:mi:ss');

	my %data = (
		user              	=> $activity->info('OWNER_USER_NAME'),
		activity_type     	=> $activity->info('ACTIVITY_TYPE_NAME'),
		activity_id      	=> $activity->id(),
		creation_date     	=> $activity->info('CREATION_DATE'),
		creation_user     	=> $activity->info('CREATION_USER_NAME'),
		status           	=> $activity->info('STATUS_NAME'),
		last_variation_date => $activity->info('LAST_VAR_DATE'),
		actVal          	=> $actVal,
		actProps     		=> $actProps,
		linkActivity    	=> $ENV{WPSOUI_HOMEPAGE}.'-v2/CUSTOMER000/TT/'.$activity->info('ACTIVITY_TYPE_NAME').'/'.$activity->id().'/details',
		last_action			=> 'RISOLUZIONE',
	);
	$art->set_default_date_format($orig_default_date_format);
	my $templateHtml = Template->new({ABSOLUTE => 1});
	my $output = '';

	$templateHtml->process($templatePath, \%data, \$output)or do {
		my $msgerror = $templateHtml->error();
		$art->last_error($msgerror);
		return undef;
	};
	$template->{BODY_HTML} = $output;

	### Fine Creazione struttura email ###
	$art->send_email(
		SUBJECT => $template->{SUBJECT},
		TO => \@email_to,
		CC => \@cc,
		BODY_HTML => $template->{BODY_HTML},
		REF => $activity->id.'-action-RISOLUZIONE',
	) || return undef;

	return 1;

}

1;
