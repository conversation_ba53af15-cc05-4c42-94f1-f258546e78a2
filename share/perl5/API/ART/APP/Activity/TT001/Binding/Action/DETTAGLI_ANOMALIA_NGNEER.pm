package API::ART::APP::Activity::TT001::Binding::Action::DETTAGLI_ANOMALIA_NGNEER;

use strict;
use warnings;
use Carp qw(verbose croak);
use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	if ( $activity->activity_property('customerSystem') eq 'NGNEER' ) {
		return 1;
    }else{
		$activity->art()->last_error(__x("action not available for activity with customersystem {customerSystem}",customerSystem=>$activity->activity_property('customerSystem')));
        return 0;
    }

}

sub pre {
	my ( $self, $activity, $params ) = @_;
	
    my $art = $self->{ART};	
    my $errmsg;

    my $actProps = $art->enum_activity_property(
            ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
            EXTENDED_OUTPUT => 1
        );
        return undef unless defined $actProps;

    # Assegno valore predefiniti ai dt a seconda del valore del dt errorSTD passato
    if(defined $params->{PROPERTIES}->{errorSTD}){

        if($params->{PROPERTIES}->{errorSTD} eq 'Errore reconcile'){
            $params->{PROPERTIES}->{stepOperationPerformed} = 'Apertura WO';
            $params->{PROPERTIES}->{problemDescription} = "In fase di apertura del work order appare il messaggio: 'ATTENZIONE ! si è verificato un errore in fase di Reconcile; Chiudere il WO senza salvare i dati e contattare l'amministratore di sistema'"; 
        }
        elsif($params->{PROPERTIES}->{errorSTD} eq 'Errore Sistemistico'){
            $params->{PROPERTIES}->{stepOperationPerformed} = 'Apertura/richiesta Reconcile WO';
            $params->{PROPERTIES}->{problemDescription} ="Sia durante l'apertura che eseguendo il reconcile dall'interno del wo il programma non riesce ad eseguire l'attività impedendo quindi la transizione dello stesso e restituisce il seguente errore: 'Si è verificato un problema sistemistico dopo l'apertura del work order. Si prega di contattare il supporto tecnico'";

        }
        elsif($params->{PROPERTIES}->{errorSTD} eq 'Network drive X'){
            $params->{PROPERTIES}->{stepOperationPerformed} = 'Apertura del Work Order';
            $params->{PROPERTIES}->{problemDescription} = 'Modulo di controllo lock.dat:impossibile configurare il Network drive x'
        }
        elsif($params->{PROPERTIES}->{errorSTD} eq 'Errore Disco Samba'){
            $params->{PROPERTIES}->{stepOperationPerformed} = 'Apertura del Work Order';
            $params->{PROPERTIES}->{problemDescription} = 'Impossibile Aprire il Work Order : Errore nella copia del disco samba'
        }
        elsif($params->{PROPERTIES}->{errorSTD} eq 'Altro'){
            unless (defined $params->{PROPERTIES}->{stepOperationPerformed} && $params->{PROPERTIES}->{stepOperationPerformed} ne '') {
                $art->last_error(__("If Errore STD is set to Other, the Step of Operation Performed field must be entered"));
                return undef;
            }
            unless (defined $params->{PROPERTIES}->{problemDescription} && $params->{PROPERTIES}->{problemDescription} ne '') {
                $art->last_error(__("If Errore STD is set to Other, the Problem description field must be entered"));
                return undef;
            }
        }
    }

    #Controllo dt reportingUser
    $params->{PROPERTIES}->{reportingUser} = uc($params->{PROPERTIES}->{reportingUser});
    unless ($params->{PROPERTIES}->{reportingUser} =~ /^[A-Z0-9]{1,8}$/) {
        $art->last_error(__x("Invalid {label} : {reportingUser} value, must be alphanumeric and no more than 8 characters",label => $actProps->{reportingUser}->{LABEL}, reportingUser => $params->{PROPERTIES}->{reportingUser}));
        return undef;
    }
    my $user = $art->user()->name();

    unless ( $user eq uc $ENV{WPSOAP_SCRIPT_USER} ) {
        #Gestione allegati
        my $mt = MIME::Types->new;
        if(!defined $params->{ATTACHMENTS}){
            $art->last_error(__("Missing mandatory param ATTACHMENTS"));
            return undef;
        }

        my $error_count = 0;
        my $log_count = 0;

        for my $att (@{$params->{ATTACHMENTS}} ) {
            
            my $mimetype = $mt->mimeTypeOf($att->{FILENAME});
            my $format = $mimetype->type;
            if ( $att->{DOC_TYPE} eq 'ERROR_1' || $att->{DOC_TYPE} eq 'ERROR_2'  ) {
                # Controllo per formato .docx o .doc
                if ( $att->{FILENAME} !~ m/\.(doc|docx)$/i ) {
                    $art->last_error(__("Only .doc and .docx attachment type are allowed"));
                    return undef;
                }
                unless ($format =~ m/application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/i || $format =~ m/application\/msword/i) {
                    $art->last_error(__x("Incorrect attachment {format}, Only .doc and .docx attachment type are allowed", format => $format));
                    return undef;
                } 
                $error_count++;
                if ( $error_count > 2 ) {
                    $art->last_error(__("Found more than two ERROR attachments, maximum two allowed"));
                    return undef;
                }
            } elsif ( $att->{DOC_TYPE} eq 'LOG' ) {
                if ( $att->{FILENAME} !~ m/\.(log)$/i ) {
                    $art->last_error(__("Only .log attachment type are allowed"));
                    return undef; 
                }
                unless ($format =~ m/x-log/i) {
                    $art->last_error(__("Incorrect attachment {format}, Only .log attachment type are allowed"));
                    return undef;
                } 
                $log_count++;
                if ( $log_count > 1 ) {
                    $art->last_error(__("Found more than one LOG attachment, maximum one allowed"));
                    return undef;
                }
            }
            # controllo dimensione allegato
            my $size = -s $att->{FILENAME};
            
            if ( $size && $size > 1572864 ) {
                $art->last_error(__("Attachment size exceeds the 1.5 MB limit"));
                return undef;
            }
        }

        if ( $error_count < 1 ) {
            $art->last_error(__("Missing mandatory attachments of type ERROR_1 or ERROR_2"));
            return undef;
        }
    }
    my $username = $activity->info('CREATION_USER_NAME');
    # controllo che sia popolato nel caso la NW selezionata sia di tipo YS (FTTH)
    if ( $activity->activity_property('contractId') eq 'FTTH' ) {
        unless (defined $params->{PROPERTIES}->{qtyPTELocked}){
            $art->last_error(__x("Missing mandatory param '{key}'", key => $actProps->{qtyPTELocked}->{LABEL}));
            return undef;
        }
    }elsif($activity->activity_property('contractId') eq 'CREATION'){
        if (defined $params->{PROPERTIES}->{qtyPTELocked}){
            if ($username eq uc $ENV{WPSOAP_SCRIPT_USER}){
                delete $params->{PROPERTIES}->{qtyPTELocked}
            } else {
                $art->last_error(__x("Field '{key}' to be populated only if Network of type FTTH", key => $actProps->{qtyPTELocked}->{LABEL}));
                return undef;
            }
        }
    }
    #controllo sulla la quantità dei qtyPTELocked
    if (defined $params->{PROPERTIES}->{qtyPTELocked} && $params->{PROPERTIES}->{qtyPTELocked} !~ /^(?:0|[1-9]\d{0,2})$/) {
        $art->last_error(__x("param '{key}' can be worth a maximum of 999", key => $actProps->{qtyPTELocked}->{LABEL}));
        return undef;
    }

    return 1;
}

sub post {
	my ( $self, $activity, $params ) = @_;
	
    my $art = $self->{ART};	
    my $errmsg;

    my $user = $art->user()->name();

    # serve per evitare l'invio mail in fase di caricamento iniziale
    return 1 if $user eq uc $ENV{WPSOAP_SCRIPT_USER};

    my $actProps = $art->enum_activity_property(
        ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
        EXTENDED_OUTPUT => 1
    );
    return undef unless defined $actProps;

    my $username = $activity->info('CREATION_USER_NAME');

    # Gestione email
    my @cc = $ENV{TT001_RECIPIENT_MAIL};
    my @email_to;

    if ($username ne uc $ENV{WPSOAP_SCRIPT_USER}){
        # recupero email to (utente che ha aperto il ticket WPSO)
        my $to = $activity->get_teams()->cerca(
            username	=> $username,
        );
        return undef
            unless defined $to;
        # Se non lo trovo,cerco negli EXT
        if (scalar @{$to}){
            push @email_to, $to->[0]->{"email"};
        } else {
            $to = $activity->get_subContracts()->get_email_from_username(
                username	=> $username,
            );
            return undef
                unless defined $to;
            push @email_to, $to->{"email"} if $to ne '';
        }
        $art->last_error(__x("Unable to find email for Username {username} ", username => $username))
            && return undef
                unless scalar @email_to;
    }

    # recupero email AT
    my $atEmail = $activity->get_teams()->cerca(
        companyCard	=> $activity->activity_property('technicalAssistantId'),
    );
    if (defined $atEmail->[0]->{email} && $atEmail->[0]->{email} ne '') {
        push @cc, $atEmail->[0]->{email};
    }

    # recupero email ClusterManager
    if (defined $activity->activity_property('clusterManagerId')){
        my $clusterManagerEmail = $activity->get_teams()->cerca(
            companyCard	=> $activity->activity_property('clusterManagerId'),
        );
        return undef
            unless defined $clusterManagerEmail;
        $art->last_error(__x("Unable to find email for {label}:{clusterManagerId}",label => $actProps->{clusterManagerId}->{LABEL}, clusterManagerId => $activity->activity_property('clusterManagerId')))
            && return undef
                unless scalar @{$clusterManagerEmail};
        push @cc, $clusterManagerEmail->[0]->{email} if $clusterManagerEmail->[0]->{email};
    }

    ### Creazione struttura email ###
    my $template = {};
    # Definizione del percorso del template
    my $templatePath = $ENV{ETC}.'/templates/TT001/email_aperta.tt2';

    # Subject
    my $subject_format = "Acquisita la richiesta per apertura ticket: NGNeer %s -RO:%s AOR:%s/Settore:%s/centrale:%s- WO:%s NTW:%s-Tipo:%s";
    my @subject_values = (
        $activity->activity_property('scope'),
        $activity->activity_property('RO'),
        $activity->activity_property('AOR'),
        $activity->activity_property('sector'),
        $activity->activity_property('central'),
        $activity->activity_property('nameWO'),
        $activity->activity_property('networkId'),
        $activity->activity_property('contractId'),
    );

    if (defined $activity->activity_property('qtyPTELocked') && $activity->activity_property('qtyPTELocked') ne '') {
        $subject_format .= "-Q.ta PTE:%s";
        push @subject_values, $activity->activity_property('qtyPTELocked');
    }

    my $subject = sprintf($subject_format, @subject_values);
    $template->{SUBJECT} = $subject;
    # Dati da sostituire nel template
    my %data = (
        id_wpso         => $activity->id,
        linkActivity    => $ENV{WPSOUI_HOMEPAGE}.'-v2/CUSTOMER000/TT/'.$activity->info('ACTIVITY_TYPE_NAME').'/'.$activity->id().'/details',
    );
    my $templateHtml = Template->new({ABSOLUTE => 1});
    my $output = '';

    $templateHtml->process($templatePath, \%data, \$output)or do {
        my $msgerror = $templateHtml->error();
        $art->last_error($msgerror);
        return undef;
    };
    $template->{BODY_HTML} = $output;

    ### Fine Creazione struttura email ###
    $art->send_email(
        SUBJECT => $template->{SUBJECT},
        TO => \@email_to,
        CC => \@cc,
        BODY_HTML => $template->{BODY_HTML},
        REF => $activity->id.'-action-DETTAGLI_ANOMALIA_NGNEER',
    ) || return undef;

    return 1;
}



1;
