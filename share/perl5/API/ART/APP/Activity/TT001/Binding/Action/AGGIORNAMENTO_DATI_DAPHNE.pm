package API::ART::APP::Activity::TT001::Binding::Action::AGGIORNAMENTO_DATI_DAPHNE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use File::Temp qw(tempdir);

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );


sub pre {

	my ( $self, $activity, $params ) = @_;
		
	my $art = $self->{ART};	
	my $errmsg;

	#Gestione allegati

	my $mt = MIME::Types->new;
	my $error_count = 0;
	
	if (scalar @{$params->{ATTACHMENTS}} > 3) {
		$art->last_error(__("Exceeded the maximum limit of 3 attachments"));
		return undef;
	}

	for my $att (@{$params->{ATTACHMENTS}} ) {
		
		my $mimetype = $mt->mimeTypeOf($att->{FILENAME});
		my $format = $mimetype->type;
		if ( $att->{DOC_TYPE} eq 'ERROR_1' || $att->{DOC_TYPE} eq 'ERROR_2'  ) {
			# Controllo per formato .docx o .doc
			if ( $att->{FILENAME} !~ m/\.(doc|docx)$/i ) {
				$art->last_error(__("Only .doc and .docx attachment type are allowed"));
				return undef;
			}
			unless ($format =~ m/application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/i || $format =~ m/application\/msword/i) {
				$art->last_error(__x("Incorrect attachment {format}, Only .doc and .docx attachment type are allowed", format => $format));
				return undef;
			} 
			$error_count++;
			if ( $error_count > 2 ) {
				$art->last_error(__("Found more than two ERROR attachments, maximum two allowed"));
				return undef;
			}
		} elsif ( $att->{DOC_TYPE} eq 'LOG' ) {
			if ( $att->{FILENAME} !~ m/\.(log)$/i ) {
				$art->last_error(__("Only .log attachment type are allowed"));
				return undef; 
			}
			unless ($format =~ m/x-log/i) {
				$art->last_error(__("Incorrect attachment {format}, Only .log attachment type are allowed"));
				return undef;
			} 
		}
		# controllo dimensione allegato
		my $size = -s $att->{FILENAME};
		
		if ( $size && $size > 1572864 ) {
			$art->last_error(__("Attachment size exceeds the 1.5 MB limit"));
			return undef;
		}
	}

	return 1;

}
sub can_do {
	my ( $self, $activity, $params ) = @_;
		
	my $art = $self->{ART};	

	if ( $activity->get_current_status_name() eq 'APERTA' ) {
		$activity->art->last_error(__('action not available for current status APERTA'));
		return 0;
    }
	my $profilo = $activity->wpsoap()->profilo();

	if(!$profilo->is_gruppo_banca_dati() && ($profilo->is_service() || $profilo->is_assistente_tecnico())){
		if ($activity->get_current_status_name() ne 'ANALISI'){
			$activity->art->last_error(__('action available only for status ANALISI '));
			return 0;
		}
	}
	if ( $activity->activity_property('customerSystem') eq 'DAPHNE' ) {
		return 1;
	}else{
		$activity->art()->last_error(__x("action not available for activity with customersystem {customerSystem}",customerSystem=>$activity->activity_property('customerSystem')));
		return 0;
	}
	return 1;
}

1;
