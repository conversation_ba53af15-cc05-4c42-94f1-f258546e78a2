package API::ART::APP::Activity::TT001::Binding::Action::ANNULLAMENTO_TT;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use File::Temp qw(tempdir);
use Template;
use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
        my ( $self, $activity, $params ) = @_;

        my $art = $self->{ART};
        my $errmsg;
        my $userName = $art->user()->name();
        if($art->user()->is_admin()){ 
                return 1;
        }
        my $creatorUser = $activity->info('CREATION_USER_NAME');
        if ($creatorUser ne uc $ENV{WPSOAP_SCRIPT_USER}){
                unless($userName eq $creatorUser ||  grep {$art->get_group_name($_) eq 'GBD'} @{$art->user->groups()}){
                        $activity->art()->last_error(__("This action is only possible for the ticket creator or GBD user"));
                        return 0;
                }
        }
}

1;
