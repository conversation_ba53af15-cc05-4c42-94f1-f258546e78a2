package API::ART::APP::Activity::TT001::Binding::Action::APERTURA_TT_CLIENTE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use File::Temp qw(tempdir);
use Template;
use base qw(API::ART::Activity::Binding::Action::Base);
use File::Temp qw/ tempdir /;
use File::Basename;
use File::Copy "copy";

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params ) = @_;
		
	my $art = $self->{ART};	
	my $errmsg;

	#Gestione allegati
    my $mt = MIME::Types->new;
	my $error_count = 0;
	my $log_count = 0;

	if ($activity->activity_property('customerSystem') eq 'NGNEER'){

		for my $att (@{$params->{ATTACHMENTS}}) {
			
			my $mimetype = $mt->mimeTypeOf($att->{FILENAME});
			my $format = $mimetype->type;
			if ( $att->{DOC_TYPE} eq 'ERROR_1' || $att->{DOC_TYPE} eq 'ERROR_2'  ) {
				# Controllo per formato .docx o .doc
				if ( $att->{FILENAME} !~ m/\.(doc|docx)$/i ) {
					$art->last_error(__("Only .doc and .docx attachment type are allowed"));
					return undef;
				}
				unless ($format =~ m/application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/i || $format =~ m/application\/msword/i) {
					$art->last_error(__x("Incorrect attachment {format}, Only .doc and .docx attachment type are allowed", format => $format));
					return undef;
				} 
				$error_count++;
				if ( $error_count > 2 ) {
					$art->last_error(__("Found more than two ERROR attachments, maximum two allowed"));
					return undef;
				}
			} elsif ( $att->{DOC_TYPE} eq 'LOG' ) {
				if ( $att->{FILENAME} !~ m/\.(log)$/i ) {
					$art->last_error(__("Only .log attachment type are allowed"));
					return undef; 
				}
				unless ($format =~ m/x-log/i) {
					$art->last_error(__("Incorrect attachment {format}, Only .log attachment type are allowed"));
					return undef;
				} 
				$log_count++;
				if ( $log_count > 1 ) {
					$art->last_error(__("Found more than one LOG attachment, maximum one allowed"));
					return undef;
				}
			}
			# controllo dimensione allegato
			my $size = -s $att->{FILENAME};
			
			if ( $size && $size > 1572864 ) {
				$art->last_error(__("Attachment size exceeds the 1.5 MB limit"));
				return undef;
			}
		}
	}elsif ($activity->activity_property('customerSystem') eq 'DAPHNE'){

		if (scalar @{$params->{ATTACHMENTS}} > 3) {
			$art->last_error(__("Exceeded the maximum limit of 3 attachments"));
			return undef;
		}
		for my $att (@{$params->{ATTACHMENTS}} ) {
			# controllo dimensione allegato
			my $size = -s $att->{FILENAME};
			if ( $size && $size > 1572864 ) {
				$art->last_error(__("Attachment size exceeds the 1.5 MB limit"));
				return undef;
			}
		}
	}else{
		if (scalar @{$params->{ATTACHMENTS}} > 3) {
			$art->last_error(__("Exceeded the maximum limit of 3 attachments"));
			return undef;
		}
		for my $att (@{$params->{ATTACHMENTS}} ) {
        
			my $mimetype = $mt->mimeTypeOf($att->{FILENAME});
			my $format = $mimetype->type;
			if ( $att->{DOC_TYPE} eq 'ERROR_1' || $att->{DOC_TYPE} eq 'ERROR_2'  ) {
				# Controllo per formato .docx o .doc
				if ( $att->{FILENAME} !~ m/\.(doc|docx)$/i ) {
					$art->last_error(__("Only .doc and .docx attachment type are allowed"));
					return undef;
				}
				unless ($format =~ m/application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/i || $format =~ m/application\/msword/i) {
					$art->last_error(__x("Incorrect attachment {format}, Only .doc and .docx attachment type are allowed", format => $format));
					return undef;
				} 
				$error_count++;
				if ( $error_count > 2 ) {
					$art->last_error(__("Found more than two ERROR attachments, maximum two allowed"));
					return undef;
				}
			} elsif ( $att->{DOC_TYPE} eq 'LOG' ) {
				if ( $att->{FILENAME} !~ m/\.(log)$/i ) {
					$art->last_error(__("Only .log attachment type are allowed"));
					return undef; 
				}
				unless ($format =~ m/x-log/i) {
					$art->last_error(__("Incorrect attachment {format}, Only .log attachment type are allowed"));
					return undef;
				}
			}
			# controllo dimensione allegato
			my $size = -s $att->{FILENAME};
			
			if ( $size && $size > 1572864 ) {
				$art->last_error(__("Attachment size exceeds the 1.5 MB limit"));
				return undef;
			}
		}
	}
	#se non mi viene passato,valorizzo con la data di step
	unless ($params->{PROPERTIES}->{dateRequestTT}) {
		$params->{PROPERTIES}->{dateRequestTT} = $art->_dbh()->get_sysdate('yyyy-mm-dd');
	}
	return 1;
}
sub post {

	my ( $self, $activity, $params ) = @_;
		
	my $art = $self->{ART};
	my $actProps = $art->enum_activity_property(
			ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
			EXTENDED_OUTPUT => 1
		);
		return undef unless defined $actProps;
	my $errmsg;
	
	### Creazione struttura email ###
	my ($to,$cc,$subject,$template,$templatePath,$templateHtml,%data,$output);
	my $user = $art->user();
	# recupero nome e cognome (utente che sta facendo l'azione)
	my $user_template = $user->first_name;
	my $surname_template = $user->last_name;
	my $telephoneContact = $user->mobile_phone;
	my @valid_to;
	my @valid_cc;	
	my $log_flag = 0;
	# recupero email AT
	my $atEmail = $activity->get_teams()->cerca(
		companyCard	=> $activity->activity_property('technicalAssistantId'),
	);
	
	if (defined $atEmail->[0]->{email} && $atEmail->[0]->{email} ne '') {
        push @valid_cc, $atEmail->[0]->{email};
    }

	my $attachments_directory = tempdir( DIR => $ENV{ART_REPOSITORY_TMP_TMPFILES}, CLEANUP => 0 );
	my $attachment_list = $activity->attachment_list();
	my %latest_attachments;
	my %filename_count;
	my $error_flag = 0;

	$to = $activity->activity_property('customerContactsTO');
	$cc = $activity->activity_property('customerContactsCC');

	#Controllo email customerContactsTO e customerContactsCC
	my @emails = split ',', $to;
	foreach my $single_email (@emails) {
		unless ($single_email =~ /^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i) {
			$art->last_error(__x("Invalid email {email}", email => $single_email));
			return undef;
		}
		push @valid_to, $single_email;
	}
	if ($activity->activity_property('customerContactsCC')){
		@emails = split ',', $cc;
		foreach my $single_email (@emails) {
			unless ($single_email =~ /^[a-z0-9!#$%&'*+\/=?^_`{|}~.-]+@[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$/i) {
				$art->last_error(__x("Invalid email {email}", email => $single_email));
				return undef;
			}
			push @valid_cc, $single_email;
		}
	}
	
	if($activity->activity_property('customerSystem') eq 'NGNEER') {

		# Seleziona l'ultimo attachment per ogni DOC_TYPE e conta i filename
		foreach my $att (@$attachment_list) {
			my $doc_type = $att->{DOC_TYPE};
			my $transition_date = $att->{TRANSITION_DATE};
			my $filename = $att->{FILENAME};

			if ($doc_type =~ /^(ERROR_1|ERROR_2|LOG|ALTRO)$/) {
				if (!exists $latest_attachments{$doc_type} || $transition_date gt $latest_attachments{$doc_type}->{TRANSITION_DATE}) {
					$latest_attachments{$doc_type} = $att;
					$filename_count{$filename}++;
				}
			}
		}
		# Scrittura degli attachment
		foreach my $key (keys %latest_attachments) {
			my $attachment = $latest_attachments{$key};
			my $doc_type = $attachment->{DOC_TYPE};
			my $filename = $attachment->{FILENAME};
			my $filepath = "$attachments_directory/$filename";

			if ($doc_type eq "ERROR_1" || $doc_type eq "ERROR_2") {
				$error_flag = 1;
			}
			if ($doc_type eq "LOG") {
				$log_flag = 1;
			};

			# Gestione filename se risulta duplicato
			if ($filename_count{$filename} > 1) {
				my $transition_date = $attachment->{TRANSITION_DATE};
				$transition_date =~ s/[^0-9A-Za-z_-]/_/g;

				my ($name, $dir, $ext) = fileparse($filename, qr/\.[^.]*/);
				$filepath = "$attachments_directory/${name}_${transition_date}${ext}";
			}

			my $content = $activity->get_attachment(
				TRANSITION_ID => $attachment->{TRANSITION_ID},
				SEQUENCE => $attachment->{SEQUENCE}
			);
			copy($content, $filepath) or do {
				$art->last_error(__x("Copy failed: {error}", error => $!));
				return undef;
			};
			close($content);
		}

		# esco con errore se non è presente allegato di tipo ERROR
		unless ($error_flag) {
			$art->last_error(__x("No ERROR_1 or ERROR_2 mandatory attachment found"));
			return undef;
		}

		$template = {};

		# Definizione del percorso del template
		$templatePath = $ENV{ETC}.'/templates/TT001/email_apertura_tt_cliente_NGNEER.tt2';

		# Subject
		my $subject_format = "%s Accesso RO:%s AOR:%s Settore:%s Centrale:%s WO:%s NTW:%s Tipo:%s";
		my @subject_values = (
			$activity->activity_property('customerSystem'),
			$activity->activity_property('RO'),
			$activity->activity_property('AOR'),
			$activity->activity_property('sector'),
			$activity->activity_property('central'),
			$activity->activity_property('nameWO'),
			$activity->activity_property('networkId'),
			$activity->activity_property('contractId')
		);

		if (defined $activity->activity_property('qtyPTELocked') && $activity->activity_property('qtyPTELocked') ne '') {
			$subject_format .= " Q.ta PTE:%s";
			push @subject_values, $activity->activity_property('qtyPTELocked');
		}

		$subject = sprintf($subject_format, @subject_values);

		$template->{SUBJECT} = $subject;

		# Dati da sostituire nel template
		%data = (
			scope					=> $activity->activity_property('scope'),
			scopeWo          		=> $activity->activity_property('scopeWO'),
			specialisationWO    	=> $activity->activity_property('specialisationWO'),
			AOR            			=> $activity->activity_property('AOR'),
			sector        			=> $activity->activity_property('sector'),
			central        			=> $activity->activity_property('central'),
			nameWO         			=> $activity->activity_property('nameWO'),
			networkId      			=> $activity->activity_property('networkId'),
			reportingUser   		=> $activity->activity_property('reportingUser'),
			stepOperationPerformed  => $activity->activity_property('stepOperationPerformed'),
			problemDescription   	=> $activity->activity_property('problemDescription'),
			linkActivity    		=> $ENV{WPSOUI_HOMEPAGE}.'-v2/CUSTOMER000/TT/'.$activity->info('ACTIVITY_TYPE_NAME').'/'.$activity->id().'/details',
		);
		# Se presenti,aggiungo dt opzionali
		for(
			"qtyPTELocked",
			"FictitiousOL"
		){
			if (defined $activity->activity_property($_)){
				$data{$_} = $activity->activity_property($_)
			}
		}
		# Se presenti,aggiungo nome e cognome di che esegue azione
		if (defined $user_template && defined $surname_template){
			$data{userName} = $user_template;
			$data{userSurname} = $surname_template;
		}
		#Aggiungo il recapito telefonico solo se valorizzato
		if (defined $telephoneContact && $telephoneContact ne '') {
			$data{telephoneContact} = $telephoneContact;
		}
	}
	elsif($activity->activity_property('customerSystem') eq 'DAPHNE') {

		my $flagAttachments;
		if (scalar @$attachment_list > 0) {
			$flagAttachments = 1;
		}

		# ordina gli attachment per TRANSITION_DATE decrescente
		my @sorted = sort { $b->{TRANSITION_DATE} cmp $a->{TRANSITION_DATE} } @$attachment_list;
		my @latest_three = @sorted[0..($#sorted < 2 ? $#sorted : 2)];

		# Conta i filename per gestire duplicati
		foreach my $att (@latest_three) {
			$filename_count{$att->{FILENAME}}++;
		}

		# Scrittura DAPHNE
		foreach my $attachment (@latest_three) {
			my $filename = $attachment->{FILENAME};
			my $filepath = "$attachments_directory/$filename";

			if ($filename_count{$filename} > 1) {
				my $transition_date = $attachment->{TRANSITION_DATE};
				$transition_date =~ s/[^0-9A-Za-z_-]/_/g;

				my ($name, $dir, $ext) = fileparse($filename, qr/\.[^.]*/);
				$filepath = "$attachments_directory/${name}_${transition_date}${ext}";
			}

			my $content = $activity->get_attachment(
				TRANSITION_ID => $attachment->{TRANSITION_ID},
				SEQUENCE => $attachment->{SEQUENCE}
			);
			copy($content, $filepath) or do {
				$art->last_error(__x("Copy failed: {error}", error => $!));
				return undef;
			};
			close($content);
		}

		$template = {};

		# Definizione del percorso del template
		$templatePath = $ENV{ETC}.'/templates/TT001/email_apertura_tt_cliente_DAPHNE.tt2';

		# Subject
		my $subject_format = "%s Accesso RO:%s AOR:%s Settore:%s Centrale:%s NTW:%s Tipo:%s";
		my @subject_values = (
			$activity->activity_property('customerSystem'),
			$activity->activity_property('RO'),
			$activity->activity_property('AOR'),
			$activity->activity_property('sector'),
			$activity->activity_property('central'),
			$activity->activity_property('networkId'),
			$activity->activity_property('contractId'),
		);

		if (defined $activity->activity_property('qtyPTELocked') && $activity->activity_property('qtyPTELocked') ne '') {
			$subject_format .= " Q.ta PTE:%s";
			push @subject_values, $activity->activity_property('qtyPTELocked');
		}

		$subject = sprintf($subject_format, @subject_values);

		$template->{SUBJECT} = $subject;

		# Dati da sostituire nel template
		%data = (
			typeJOB					=> $activity->activity_property('typeJOB'),
			specialisationJOB    	=> $activity->activity_property('specialisationJOB'),
			nameJOB       			=> $activity->activity_property('nameJOB'),
			networkId      			=> $activity->activity_property('networkId'),
			reportingUser   		=> $activity->activity_property('reportingUser'),
			stepOperationPerformed  => $activity->activity_property('stepOperationPerformed'),
			problemDescription   	=> $activity->activity_property('problemDescription'),
			ro   					=> $activity->activity_property('RO'),
			central   				=> $activity->activity_property('central'),
			centralId   			=> $activity->activity_property('centralId'),
			flagAttachments  		=> $flagAttachments,
			linkActivity    		=> $ENV{WPSOUI_HOMEPAGE}.'-v2/CUSTOMER000/TT/'.$activity->info('ACTIVITY_TYPE_NAME').'/'.$activity->id().'/details',
		);
		# Se presenti,aggiungo dt opzionale qtyPTELocked
		if (defined $activity->activity_property('qtyPTELocked')){
				$data{qtyPTELocked} = $activity->activity_property('qtyPTELocked')
			}
		# Se presenti,aggiungo nome e cognome di che esegue azione
		if (defined $user_template && defined $surname_template){
			$data{userName} = $user_template;
			$data{userSurname} = $surname_template;
		}
		#Aggiungo il recapito telefonico solo se valorizzato
		if (defined $telephoneContact && $telephoneContact ne '') {
			$data{telephoneContact} = $telephoneContact;
		}
		my $centrale = $activity->wpsoap()->get_aor_ro_by_central(centralId => $activity->activity_property('centralId'));
		return undef unless defined $centrale;

		if (scalar @{$centrale}){
			$data{city} = $centrale->[0]->{CITTA};
			$data{clli} = $centrale->[0]->{CLLI};
		}		
	}else{
		# ordina gli attachment per TRANSITION_DATE decrescente
		my @sorted = sort { $b->{TRANSITION_DATE} cmp $a->{TRANSITION_DATE} } @$attachment_list;
		my @latest_three = @sorted[0..($#sorted < 2 ? $#sorted : 2)];

		# Conta i filename per gestire duplicati
		foreach my $att (@latest_three) {
			$filename_count{$att->{FILENAME}}++;
		}

		# Scrittura UNICARA
		foreach my $attachment (@latest_three) {
			my $filename = $attachment->{FILENAME};
			my $filepath = "$attachments_directory/$filename";

			if ($filename_count{$filename} > 1) {
				my $transition_date = $attachment->{TRANSITION_DATE};
				$transition_date =~ s/[^0-9A-Za-z_-]/_/g;

				my ($name, $dir, $ext) = fileparse($filename, qr/\.[^.]*/);
				$filepath = "$attachments_directory/${name}_${transition_date}${ext}";
			}

			my $content = $activity->get_attachment(
				TRANSITION_ID => $attachment->{TRANSITION_ID},
				SEQUENCE => $attachment->{SEQUENCE}
			);
			copy($content, $filepath) or do {
				$art->last_error(__x("Copy failed: {error}", error => $!));
				return undef;
			};
			close($content);
		}

		$template = {};

		# Definizione del percorso del template
		$templatePath = $ENV{ETC}.'/templates/TT001/email_apertura_tt_cliente_UNICARA.tt2';

		# Subject
		$subject = sprintf(
		"Richiesta Apertura TT- UNICARA - Anomalia:%s",
		$activity->activity_property('anomalyFound'),
		);

		$template->{SUBJECT} = $subject;

		# Dati da sostituire nel template
		%data = (
			central					=> $activity->activity_property('central'),
			centralId				=> $activity->activity_property('centralId'),
			nameOL					=> $activity->activity_property('nameOL'),
			reportingUser   		=> $activity->activity_property('reportingUser'),
			stepOperationPerformed  => $activity->activity_property('stepOperationPerformed'),
			problemDescription   	=> $activity->activity_property('problemDescription'),
			linkActivity    		=> $ENV{WPSOUI_HOMEPAGE}.'-v2/CUSTOMER000/TT/'.$activity->info('ACTIVITY_TYPE_NAME').'/'.$activity->id().'/details',
		);
	}

	$templateHtml = Template->new({ABSOLUTE => 1});
	$output = '';

	$templateHtml->process($templatePath, \%data, \$output) or do {
		my $msgerror = $templateHtml->error();
		$art->last_error($msgerror);
		return undef;
	};

	$template->{BODY_HTML} = $output;

	### Fine Creazione struttura email ###
	push @valid_cc, $ENV{TT001_RECIPIENT_MAIL};
    unless ($user->name() eq uc $ENV{WPSOAP_SCRIPT_USER}){
		$art->send_email(
			SUBJECT => $template->{SUBJECT},
			TO => \@valid_to,
			CC => \@valid_cc,
			BODY_HTML => $template->{BODY_HTML},
			REF => $activity->id.'-action-APERTURA_TT_CLIENTE-'.$activity->activity_property('customerSystem'),
			SENDER => $ENV{TT001_RECIPIENT_MAIL},
			ATTACHMENTS_DIRECTORY => $attachments_directory,
		)||return undef;
	}
	return 1;
}

1;
