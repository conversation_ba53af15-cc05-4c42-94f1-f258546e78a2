package API::ART::APP::Activity::TT001::Binding::Action::DETTAGLI_ANOMALIA_UNICARA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use File::Temp qw(tempdir);

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	if ( $activity->activity_property('customerSystem') eq 'UNICARA' ) {
		return 1;
    }else{
		$activity->art()->last_error(__x("action not available for activity with customersystem {customerSystem}",customerSystem=>$activity->activity_property('customerSystem')));
        return 0;
    }

}

sub pre {
    my ( $self, $activity, $params ) = @_;

    my $art = $self->{ART};	
    my $errmsg;

    #Controllo dt reportingUser
    $params->{PROPERTIES}->{reportingUser} = uc($params->{PROPERTIES}->{reportingUser});
    unless ($params->{PROPERTIES}->{reportingUser} =~ /^[A-Z0-9]{1,8}$/) {
        $art->last_error(__x("Invalid reportingUser {reportingUser} value: must be alphanumeric and no more than 8 characters", reportingUser => $params->{PROPERTIES}->{reportingUser}));
        return undef;
    }

    my $user = $art->user()->name();
    unless ( $user eq uc $ENV{WPSOAP_SCRIPT_USER} ) {
        #Gestione allegati
        my $mt = MIME::Types->new;
        if(!defined $params->{ATTACHMENTS}){
            $art->last_error(__("Missing mandatory param ATTACHMENTS"));
            return undef;
        }

        my $error_count = 0;
        if (scalar @{$params->{ATTACHMENTS}} > 3) {
            $art->last_error(__("Exceeded the maximum limit of 3 attachments"));
            return undef;
        }
        for my $att (@{$params->{ATTACHMENTS}} ) {
            
            my $mimetype = $mt->mimeTypeOf($att->{FILENAME});
            my $format = $mimetype->type;
            if ( $att->{DOC_TYPE} eq 'ERROR_1' || $att->{DOC_TYPE} eq 'ERROR_2'  ) {
                # Controllo per formato .docx o .doc
                if ( $att->{FILENAME} !~ m/\.(doc|docx)$/i ) {
                    $art->last_error(__("Only .doc and .docx attachment type are allowed"));
                    return undef;
                }
                unless ($format =~ m/application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document/i || $format =~ m/application\/msword/i) {
                    $art->last_error(__x("Incorrect attachment {format}, Only .doc and .docx attachment type are allowed", format => $format));
                    return undef;
                } 
                $error_count++;
                if ( $error_count > 2 ) {
                    $art->last_error(__("Found more than two ERROR attachments, maximum two allowed"));
                    return undef;
                }
            } elsif ( $att->{DOC_TYPE} eq 'LOG' ) {
                if ( $att->{FILENAME} !~ m/\.(log)$/i ) {
                    $art->last_error(__("Only .log attachment type are allowed"));
                    return undef; 
                }
                unless ($format =~ m/x-log/i) {
                    $art->last_error(__("Incorrect attachment {format}, Only .log attachment type are allowed"));
                    return undef;
                }
            }
            # controllo dimensione allegato
            my $size = -s $att->{FILENAME};
            
            if ( $size && $size > 1572864 ) {
                $art->last_error(__("Attachment size exceeds the 1.5 MB limit"));
                return undef;
            }
        }

        if ( $error_count < 1 ) {
            $art->last_error(__("Missing mandatory attachments of type ERROR_1 or ERROR_2"));
            return undef;
        }
    }

    return 1;
  
}

sub post {
    my ( $self, $activity, $params ) = @_;

    my $art = $self->{ART};	
    my $errmsg;

    my $user = $art->user()->name();

    # serve per evitare l'invio mail in fase di caricamento iniziale
    return 1 if $user eq uc $ENV{WPSOAP_SCRIPT_USER};

    my $actProps = $art->enum_activity_property(
        ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
        EXTENDED_OUTPUT => 1
    );
    return undef unless defined $actProps;

    # Gestione email
    my $username = $activity->info('CREATION_USER_NAME');
    my @cc = $ENV{TT001_RECIPIENT_MAIL};
    my @email_to;

    if ($username ne uc $ENV{WPSOAP_SCRIPT_USER}){
        # recupero email to (utente che ha aperto il ticket WPSO)
        my $to = $activity->get_teams()->cerca(
            username	=> $username,
        );
        return undef
            unless defined $to;
        # Se non lo trovo,cerco negli EXT
        if (scalar @{$to}){
            push @email_to, $to->[0]->{"email"};
        } else {
            $to = $activity->get_subContracts()->get_email_from_username(
                username	=> $username,
            );
            return undef
                unless defined $to;
            push @email_to, $to->{"email"} if $to ne '';
        }
        $art->last_error(__x("Unable to find email for Username {username} ", username => $username))
            && return undef
                unless scalar @email_to;
    }

    # recupero email AT
    my $atEmail = $activity->get_teams()->cerca(
        companyCard	=> $activity->activity_property('technicalAssistantId'),
    );
    if (defined $atEmail->[0]->{email} && $atEmail->[0]->{email} ne '') {
        push @cc, $atEmail->[0]->{email};
    }

    # recupero email ClusterManager
    if (defined $activity->activity_property('clusterManagerId')){
        my $clusterManagerEmail = $activity->get_teams()->cerca(
            companyCard	=> $activity->activity_property('clusterManagerId'),
        );
        return undef
            unless defined $clusterManagerEmail;
        $art->last_error(__x("Unable to find email for {label}:{clusterManagerId}",label => $actProps->{clusterManagerId}->{LABEL}, clusterManagerId => $activity->activity_property('clusterManagerId')))
            && return undef
                unless scalar @{$clusterManagerEmail};
        push @cc, $clusterManagerEmail->[0]->{email} if $clusterManagerEmail->[0]->{email};
    }

    ### Creazione struttura email ###
    my $template = {};
    # Definizione del percorso del template
    my $templatePath = $ENV{ETC}.'/templates/TT001/email_aperta.tt2';

    # Subject
    my $subject_format = "Acquisita la richiesta per apertura ticket: UNICA RA %s -RO:%s AOR:%s/Settore:%s/centrale:%s-NTW:%s-Tipo:%s";
    my @subject_values = (
        $activity->activity_property('RO'),
        $activity->activity_property('AOR'),
        $activity->activity_property('sector'),
        $activity->activity_property('central'),
        $activity->activity_property('networkId'),
        $activity->activity_property('contractId'),
    );

    my $subject = sprintf($subject_format, @subject_values);
    $template->{SUBJECT} = $subject;
    # Dati da sostituire nel template
    my %data = (
        id_wpso         => $activity->id,
        linkActivity    => $ENV{WPSOUI_HOMEPAGE}.'-v2/CUSTOMER000/TT/'.$activity->info('ACTIVITY_TYPE_NAME').'/'.$activity->id().'/details',
    );
    my $templateHtml = Template->new({ABSOLUTE => 1});
    my $output = '';

    $templateHtml->process($templatePath, \%data, \$output)or do {
        my $msgerror = $templateHtml->error();
        $art->last_error($msgerror);
        return undef;
    };
    $template->{BODY_HTML} = $output;

    ### Fine Creazione struttura email ###
    $art->send_email(
        SUBJECT => $template->{SUBJECT},
        TO => \@email_to,
        CC => \@cc,
        BODY_HTML => $template->{BODY_HTML},
        REF => $activity->id.'-action-DETTAGLI_ANOMALIA_UNICARA',
    ) || return undef;

    return 1;
}

1;
