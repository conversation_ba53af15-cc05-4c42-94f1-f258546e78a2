package API::ART::APP::Activity::TT001::Binding::Action::PRESA_IN_CARICO_SIRTI;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;
use File::Temp qw(tempdir);
use Template;
use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
        my ( $self, $activity, $params) = @_;
	my $art = $self->{ART};
	my $login = $params->{PROPERTIES}->{username} ? $params->{PROPERTIES}->{username} : $art->user()->name();

	# aggiungo il campo technicalAssistantId
	my $ats = $activity->get_teams()->cerca(
                username	        => $login,
                companyAbbreviation     => $activity->get_contract->property('companyAbbreviation'),
	);
	return undef unless defined $ats;
	
	$art->last_error(__x("Unable to find info for user with login {login}", login => $login))
		&& return undef
			unless scalar @{$ats};
	
	$params->{PROPERTIES}->{operatorId} 	= $ats->[0]->{teamId};
	$params->{PROPERTIES}->{operatorName} 	= $ats->[0]->{surname}.' '.$ats->[0]->{name};

	delete $params->{PROPERTIES}->{username};

	$params->{DEST_USER} = $login;

	return 1;
}

1;
