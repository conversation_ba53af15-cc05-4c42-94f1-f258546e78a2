package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::RIPRESA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;

	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
	
		## capisco da dove sono partito prima di andare in SOSPESA per sapere dove tornare
		my $history = $activity->history();
		return undef unless $history;
		
		# imposto il filtro
		return undef unless defined $history->set_filter(
			ACTION => [
				'SOSPENSIONE'
			]
		);
		
		# recupero l'ultimo step che ha effettuato questa azione
		my $transition = $history->last();
		return undef unless (defined $transition);
		
		my $initial_status = $transition->from_status_name();
		
		# a seconda dello stato iniziale definisco quale azione effettuare
		my $actions = {
			NUOVA => 'RITORNA_IN_NUOVA',
			INOLTRATA => 'RITORNA_IN_INOLTRATA'
		};
		
		return $activity->step(
			ACTION => $actions->{$initial_status}
		);
	}

	return 1;
};

1;
