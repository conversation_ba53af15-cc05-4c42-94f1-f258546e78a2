package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Action::_CHECK_OC;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity, $action) = @_;
	
	my $enableGroups = ['ROOT', 'ADMIN'];
	
	if ($activity->is_of()){ # gestico OF
		
		push @{$enableGroups}, @{[
			'REFERENTE_PERMESSI_BUILDING',
		]};
		
		if ($action =~ /^(PERMESSO_OTTENUTO|PERMESSO_RIFIUTATO|PERMESSO_NON_NECESSARIO)$/){
			$self->art()->last_error(__x("Can't do this action: available only for fiber construction"));
			return undef;
		}
	} else { #gestisco FC
		
		push @{$enableGroups}, @{[
			'AT',
			'SERVICE',
		]};
		
		if ($action =~ /^(CONFERMA_POSITIVA|RIFIUTO)$/){
			$self->art()->last_error(__x("Action not available for fiber construction"));
			return undef;
		}
		
		if ($action eq 'PERMESSO_NON_NECESSARIO'){
			my $targetAsset = $activity->activity_property('targetAsset');
			if ($targetAsset ne 'PTE'){
				$self->art()->last_error(__x("Action not available for targetAsset {targetAsset}", targetAsset => $targetAsset));
				return undef;
			}
		}
		
	} 
	
	my $userGroups = $self->art()->user()->groups;
	
	# verifico che abbia almeno un gruppo associato ad FC: è indispensabile per le azioni
	# che sono utilizzate in entrambe le gestione
	my $found = 0;
	for my $enGroup (@{$enableGroups}){
		if (grep {$enGroup eq $self->art()->get_group_name($_)} @{$userGroups}){
			$found = 1; 
			last;
		}
	}
	
	unless ($found){
		$self->art()->last_error(__x("Can't do this action: user not enabled"));
		return undef;
	}
	
	return 1;
}

1;
