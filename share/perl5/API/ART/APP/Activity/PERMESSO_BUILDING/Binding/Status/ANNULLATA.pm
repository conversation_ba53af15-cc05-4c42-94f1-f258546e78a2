package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::ANNULLATA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::_FINAL_STATUS);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;
	
	return 1 unless $activity->is_of;
	
	return $activity->parent()->update_permits_count();
};

1;
