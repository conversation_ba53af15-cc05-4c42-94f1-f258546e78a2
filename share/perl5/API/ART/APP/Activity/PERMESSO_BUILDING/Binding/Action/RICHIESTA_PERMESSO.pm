package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Action::RICHIESTA_PERMESSO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub can_do {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::can_do($activity, 'RICHIESTA_PERMESSO');
	return undef unless defined $s;

	return 1;
}

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	my $activity_property = $activity->activity_property;

	# # infarcisco, se possibile, eventuali valori non presenti perchè potrebbero essere già stati passati in apertura
	# for ('expectedAuthorizationDate','expectedEndAuthorizationDate', 'requestDate'){
	# 	next if defined $params->{PROPERTIES}->{$_};
	# 	$params->{PROPERTIES}->{$_} = $activity_property->{$_};
	# }

	if (! defined $params->{PROPERTIES}->{requestDate}){
		$params->{PROPERTIES}->{requestDate} = $art->get_iso_date_from_date($art->_dbh()->get_sysdate());
	}

	return 1;
}

1;
