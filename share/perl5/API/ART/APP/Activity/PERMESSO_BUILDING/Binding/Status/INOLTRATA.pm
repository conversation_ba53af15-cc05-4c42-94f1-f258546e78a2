package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::INOLTRATA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;

	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
	
		## il binding è da eseguire solo se non è stata fatta l'azione di AGGIUNGI_INFO
		return 1 if $activity->is_aggiungi_info();

		my $history = $activity->history();
		return undef unless defined $history;

		my $last = $history->last();
		return undef unless defined $last;

		# per ora si applica solo al caso di ripresa da RIPRESA dopo SOSPENSIONE
		return 1 if $last->action_name() ne 'RITORNA_IN_INOLTRATA';

		my $sender = $activity->get_sender();
		return undef unless defined $sender;
		
		my $data = $activity->get_data_for_notify_permit();
		
		$self->art()->last_error()
			&& return undef
				unless defined $sender->notify_permit_update(%{$data});
	}
	
	return 1;
};

1;
