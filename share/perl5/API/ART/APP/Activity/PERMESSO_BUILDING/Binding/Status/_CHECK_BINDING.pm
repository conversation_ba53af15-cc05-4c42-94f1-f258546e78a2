package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::_CHECK_BINDING;

use strict;
use warnings;
use Carp qw(verbose croak);

use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $art = $self->art();
	
	# se l'ultima azione eseguita è uno dell'elenco sotto non devo eseguire il binding
	my $history = $activity->history();
	return undef unless defined $history;
	
	my $transition = $history->last();
	return undef unless defined $transition;
	
	my $any_status_action = [
		'SOCIETARIZZAZIONE_CLIENTE'
	];
	
	if (grep {$transition->action_name() eq $_} @{$any_status_action}){
		return 0;
	} else {
		return 1;
	}
};

1;
