package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Action::SOCIETARIZZAZIONE_CLIENTE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	return undef unless $activity->system->set_description($params->{PROPERTIES}->{'projectId'});

	my @parts = split ('-', $activity->info('DESCRIPTION'));

	$parts[2] = $params->{PROPERTIES}->{'projectId'};

	my $description = join('-',@parts);

	return undef unless $activity->set_description($description, FORCE => 1);

	if (defined $params->{PROPERTIES}->{'projectId'}){
		$params->{PROPERTIES}->{'projectIdOld'} = $activity->activity_property('projectId');
	}

	return 1;
}

1;
