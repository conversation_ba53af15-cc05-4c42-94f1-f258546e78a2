package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::APERTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;

	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
	
		unless ($activity->is_of()){

			my $property = $activity->activity_property();

			if ($property->{'targetAsset'} eq 'PTE' && $property->{'invalidatePreviousReferences'}){
				# cerco tutti i lavori espletati sullo stesso riferimento
				my $params_for_cerca = {
					"customerId"	=> $activity->system_property('customerId')
					,"contractId"	=> $activity->system_property('contractId')
					,"targetAsset"	=> [$activity->activity_property('targetAsset')]
					,"ref00"		=> $property->{ref00}
					,"ref01"		=> $property->{ref01}
					,"assetId"		=> $activity->system_property('assetId')->[0]
					,"STATUS_IN"	=> ['CHIUSA']
				};
				my $activities_to_invalidate = $activity->get_coll_permesso()->cerca(%$params_for_cerca);
				return undef
					unless defined $activities_to_invalidate;

				for my $activity_to_invalidate (@{$activities_to_invalidate}){
					return undef
						unless defined $activity_to_invalidate->invalidate(
							DESCRIPTION => 'Invalidata a seguito apertura ticket '.$activity->id(),
							PROPERTIES => {
								invalidateActivityId => $activity->id()
							}
						);
				}
			}

			my $sender = $activity->get_sender();
			return undef unless defined $sender;
			
			my $data = $activity->get_data_for_notify_permit();
			
			$self->art()->last_error()
				&& return undef
					unless defined $sender->notify_permit_open(%{$data});
		}
		
		return $activity->step(ACTION => 'NUOVO_PERMESSO');
	}

	return 1;
};

1;
