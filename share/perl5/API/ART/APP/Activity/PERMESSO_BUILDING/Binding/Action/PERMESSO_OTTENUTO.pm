package API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Action::PERMESSO_OTTENUTO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_BUILDING::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub can_do {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::can_do($activity, 'PERMESSO_OTTENUTO');
	return undef unless defined $s;

	return 1;
}

1;
