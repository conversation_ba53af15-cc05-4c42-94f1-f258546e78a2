package API::ART::APP::Activity::PERMESSO_LAVORI;

use strict;
use warnings;
use JSON;
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOAP::MQ::Sender::PERMESSO_LAVORI;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art()->_dbh() }

sub is_aggiungi_info {
	my $self = shift;
	
	## il binding è da eseguire solo se non è stata fatta l'azione di AGGIUNGI_INFO
	my $history = $self->history();
	return undef unless $history;
	
	my $transition = $history->last();
	unless (defined $transition){
		$self->art()->last_error(__x("Anomaly: unable to find last transition"));
		return undef;
	}
	
	return ($transition->action_name() eq 'AGGIUNGI_INFO') ? 1 : 0;
};

sub get_sender {
	my $self = shift;
	$self->{SENDER} = WPSOAP::MQ::Sender::PERMESSO_LAVORI->instance(DB => $self->get_db());
	return $self->{SENDER};
}

sub update_stato_area_permessi {
	#aggiorno SiNFO relativamente ai permessi
	
	my $self = shift;
	
	return $self->get_sender()->update_stato_area_permessi(
		SOURCE_REF => $self->id(),
		DATA => {
			permitsAreaId => $self->activity_property('permitsAreaId')
		}
	);
}

sub is_of {
	my $self = shift;
	return $self->info('SYSTEM_TYPE_NAME') eq 'AP' ? 1 : 0;
}

sub get_data_for_notify_permit{
	my $self = shift;
	
	my $property = $self->activity_property();
		
	my $system_property = $self->system_property();
	
	my $data = {
		SOURCE_REF => $self->id()
		, TYPE => uc($property->{targetAsset})
		, DATA => {
			ACTIVITY_ID => $self->id(),
			ACTIVITY	=> to_json($self->dump()),
			STATUS		=> $self->get_current_status_name(),
			customerId => $system_property->{customerId},
			contractId => $system_property->{contractId},
		}
	};
	
	$data->{DATA}->{projectId} = $property->{projectId} if defined $property->{projectId};
	$data->{DATA}->{assetId} = $system_property->{assetId} if defined $system_property->{assetId};
	
	$data->{DATA}->{SUBCONTRACT_CODE} = $property->{subContractCode} if defined $property->{subContractCode};

	return $data;
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub step(){
	my $self = shift;
	my %params = @_;
	
	# per ora i meta-allegati li mettiamo solo per gestione fc
	if (exists $params{ATTACHMENTS} && !$self->is_of()){
		my $projectId = $self->activity_property('projectId');
		my $assets = $self->system_property('assetId');
		my $meta = {
			activityId => $self->id(),
			activityType => $self->info('ACTIVITY_TYPE_NAME'),
		};
		$meta->{networkId} = $projectId if defined $projectId;
		for my $asset (@{$assets}){
			$meta->{"assetId-".$asset} = 1;
		}
		my $targetAsset = $self->activity_property('targetAsset');
		if (defined $targetAsset && $targetAsset =~ /^(NetworkFibercop)$/){
			$meta->{cabinetId} = $self->activity_property('ref00');
		}
		$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
			&& return undef 
				if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
		for my $att (@{$params{ATTACHMENTS}}){
			if (!ref ($att)){
				# lo trasformo in una struttura per gestire i meta
				$att = {
					FILENAME => $att,
					META => $meta
				};
			} elsif (ref($att) eq 'HASH'){
				# aggiungo la chiave META se non esiste
				if (!exists $att->{META}){
					$att->{META} = $meta
				} else {
					# aggiungo le chiavi sovrascrivendole se già presenti
					for my $k (keys %{$meta}){
						$att->{META}->{$k} = $meta->{$k};
					}
				}
				
			} else {
				$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
				return undef; 
			}
		}
	}
	
	return $self->SUPER::step(%params);
}
1;
