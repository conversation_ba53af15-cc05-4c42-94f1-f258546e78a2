package API::ART::APP::Activity::LAVORO::Binding::Status::IN_LAVORAZIONE_SUBAPPALTO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LAVORO::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
	
		my $art = $self->{ART};
		
		my $last_action = $activity->get_last_action();
		return undef unless defined $last_action;

		my $activity_property = $activity->activity_property();
		
		# a seconda dell'ultima azione ho un comportamento diverso
		if ($last_action eq 'MODIFICA'){
			# se arriva dall'azione di MODIFICA non deve fare nulla
			return 1;	
		} elsif($last_action eq 'FINE_LAVORI_SUBAPPALTO'){
			return $activity->step(ACTION => 'CARICAMENTO_AS_BUILT');
		} else {
			
			# se non è una gestione OpenFiber non devo fare nulla
			if ($activity->system()->info('SYSTEM_TYPE_NAME') =~ /^(CANTIERE|CAVO|NODO)$/){
			
				# serve per il caricamento iniziale ed eventuali sanity: non crea problemi
				return 1 unless defined $activity_property->{"subContractCode"};
				
				# in caso di pianificazione verifico se esiste già un sistema di tipo SUBAPPALTO
				my $subs = $activity->get_coll_subappalto()->cerca(
					"customerId"		=> $activity_property->{"customerId"}
					,"contractId"		=> $activity_property->{"contractId"}
					,"subContractCode"	=> $activity_property->{"subContractCode"}
				);
				
				return undef unless defined $subs;
				
				my $subappalto;
				
				if (scalar @{$subs} > 1) { # se è più di uno è un errore
					$art->last_error(__x("Found more than one subcontract for subContractCode {code}", code => $activity_property->{"subContractCode"}));
					return undef;
				} elsif (scalar @{$subs} == 1) { # sull'unico effettuo l'eventuale aggiornamento del subContractName
					$subappalto = $subs->[0];
					if ($subappalto->property('subContractName') ne $activity_property->{"subContractName"}){
						return undef unless $subappalto->set_property(
							PROPERTIES => {
								subContractName => $activity_property->{"subContractName"}
							}
						);
					}
				} else { # non esiste quindi lo creo
					$subappalto = $activity->get_coll_subappalto()->crea(
						"customerId" 		=> $activity_property->{"customerId"}
						,"contractId"		=> $activity_property->{"contractId"}
						,"subContractCode"	=> $activity_property->{"subContractCode"}
						,"subContractName"	=> $activity_property->{"subContractName"}
					);
					return undef unless defined $subappalto;
				}
				
				# se non è ancora adottato significa che è alla prima assegnazione, altrimenti è un loop di PIANIFICAZIONE
				unless ($activity->system()->parent_id()){
					return $subappalto->adopt_children( CHILDREN => [$activity->system()] );
				}
			} else { #gestione fiber construnction

				my $system_property = $activity->system_property();

				# se l'ultima azione è RIPRESA_LAVORI devo mandare aggiornamneto
				if (
					$last_action =~/^(RIPRESA_LAVORI|PRESA_IN_CARICO|CHIUSURA_TT)$/
				){
					my $sender = $activity->get_sender();
					return undef unless defined $sender;

					my $data = $activity->get_data_for_notify_works();
					return undef
						unless defined $data;
					
					$self->art()->last_error()
						&& return undef
							unless defined $sender->notify_work_update(%{$data});
				}
			}
		}

		# se è un operazione contabile viene chiuso direttamente
		if ($activity_property->{accountingOperation}){
			return $activity->step(
				ACTION => 'FINE_LAVORI'
				, DESCRIPTION => 'Chiusura automatica per operazione contabile'
				, PROPERTIES => {
					startWorkDate => $activity_property->{'startPlannedDate'},
					endWorkDate => $activity_property->{'endPlannedDate'}
				}
			);
		}
	}
		
	return 1;
};

1;
