package API::ART::APP::Activity::LC_CUSTOMER_PROJECT::Binding::Status::APERTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	return $activity->step(ACTION => 'RICHIESTA_DOCUMENTAZIONE');
};

1;
