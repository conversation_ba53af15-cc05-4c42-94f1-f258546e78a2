package API::ART::APP::Activity::LC_CUSTOMER_PROJECT::Binding::Action::_NOTIFICA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	my $activity_property = $activity->activity_property();

	for ('assignmentDate', 'projectTypology', 'central'){
		$params->{PROPERTIES}->{$_} = $activity_property->{$_}
			unless exists $params->{PROPERTIES}->{$_};
	}
	if (exists $params->{PROPERTIES}->{networkIds}){
		return undef
			unless $activity->system()->set_property(
				PROPERTIES => {
					networkIds => $params->{PROPERTIES}->{networkIds}
				}
			);
		delete $params->{PROPERTIES}->{networkIds};
	}

	return 1;
}

1;
