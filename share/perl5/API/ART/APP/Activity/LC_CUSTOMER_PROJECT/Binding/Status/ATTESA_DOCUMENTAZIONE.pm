package API::ART::APP::Activity::LC_CUSTOMER_PROJECT::Binding::Status::ATTESA_DOCUMENTAZIONE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $art = $self->art();

	my $history = $activity->history();
	return undef unless defined $history;

	my $last = $history->last();
	return undef unless defined $last;

	if ($last->action_name() eq 'RICHIESTA_DOCUMENTAZIONE'){
		# il primo send invia la richiesta al bot
		my $date_format = $art->get_default_date_format();
		my $expiry_schedule_date = $art->_dbh()->fetch_minimalized ("select to_char(sysdate+".$ENV{CUSTOMER_PROJECT_DOCUMENTATION_TIMEOUT}."/60/60/24,".$art->_dbh()->quote($date_format).") from dual");
		my $props = $activity->activity_property('RO', 'projectCode');
		$props->{EXPIRY_DATE} = $expiry_schedule_date;
		$props->{EXPIRY_DATE_FORMAT} = $date_format;
		$props->{TARGET_SERVICE} = 'ENFTTH_AP';
		$props->{TARGET_CONTEXT} = 'CUSTOMER_PROJECT';
		$props->{URL_FOR_UPLOAD_FILES} = $ENV{BOTGWWS_URL_FOR_UPLOAD_FILES};
		my $id_speedark = $activity->get_sender()->send_bot_speedark(
			SOURCE_REF => $activity->id(),
			RO => $props->{RO},
			DATA => $props
		);

		# il terzo send invia la richiesta per la gestione timeout
		return undef unless $activity->get_sender()->send_documentation_timeout(
			SOURCE_REF => $activity->id(),
			SCHEDULE_DATE => $expiry_schedule_date,
			DATA => $props
		);
	}
	
	return 1;

};

1;
