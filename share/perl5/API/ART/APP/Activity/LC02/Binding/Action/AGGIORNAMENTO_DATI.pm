package API::ART::APP::Activity::LC02::Binding::Action::AGGIORNAMENTO_DATI;

use strict;
use warnings;
use Array::Utils qw(:all);
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LC02::Binding::Action::AGGIORNA_CENTRO_LAVORO);

use API::ART::APP::Activity::LC02::Binding::Action::AGGIORNA_CENTRO_LAVORO;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};
	
	my $errmsg;

	my $activity_property = $activity->activity_property();

	# normalizzo la network
	$params->{PROPERTIES}->{paymentNetwork}= sprintf '%012d', $params->{PROPERTIES}->{paymentNetwork} if defined $params->{PROPERTIES}->{paymentNetwork};

	my $new_activity_properties = {};
	my $new_system_properties = {};
	my $action_properties = $art->get_action_properties( 'LC02', 'AGGIORNAMENTO_DATI' );
	for my $record ( @{$action_properties} ) {
		my $key = $record->{NOME_TDTA};
		my $obb = $record->{OBBLIGATORIO};
		if ( exists $params->{PROPERTIES}->{$key} ) {
			if ( !defined $params->{PROPERTIES}->{$key} and $obb eq 'Y' ) {
				$art->last_error(__x('Missing mandatory parameter: {key}', key => $key));
				return;
			}
			$new_activity_properties->{$key} = $params->{PROPERTIES}->{$key};
			$new_system_properties->{$key} = $params->{PROPERTIES}->{$key};
		} else {
			# se non esiste ed è obbligatorio significa che da GUI non è stato modificato e quindi non viene passato
			# lo imposto con l'attuale valore
			if ($obb eq 'Y'){
				$params->{PROPERTIES}->{$key} = $activity_property->{$key};
			}
		}
		
	}
	$new_activity_properties->{workingGroupCode} = $activity_property->{workingGroupCode};

	# elimino le chiavi eventualmente non necessarie
	my $keys = $art->enum_system_property(SYSTEM_TYPE_NAME => 'EL02');
	for my $k (keys %{$new_system_properties}){
		delete $new_system_properties->{$k} unless exists $keys->{$k};
	}

	# le estraggo per efficienza
	my $system_property = $activity->system_property(
		'id',
		'centralId',
		'cabinet',
		'idCNO',
		'city',
		'address',
		'zipCode',
		'province',
		'privatePermitsStatus',
		'wSurveyStatus',
		'wInfrastructureStatus',
		'wLayingStatus',
		'wJunctionStatus',
		'wUpdateDatabaseF1Status',
		'wUpdateDatabaseF2Status',
		'wTestAppStatus',
		'wTestOTDRStatus',
		'wRestorationStatus',
		'wPlanningStatus',
		'paymentNetwork',
		'customerId',
		'contractId',
		'WBELevel3',
		'externalWorkTypeId',
		'externalAccountingType'
	);

	# se l'attività è nello stato LAVORATO (finale) non devo fare null'altro
	if ($activity->get_current_status_name ne 'LAVORATO') {

		# WPSOC-316 permette di non bypassare i controlli di validità e sovrascrivere con una stringa vuota con un file di aggiornamento
		for my $key ( qw(PTEType PTEPosition PTEPotential location) ) {
			delete $params->{PROPERTIES}->{$key} if exists $params->{PROPERTIES}->{$key} && (!defined $params->{PROPERTIES}->{$key} || $params->{PROPERTIES}->{$key} eq '');
		}
		
		# verifico parametri che hanno una lista predefinita e che sono aggiornabili
		$art->last_error($errmsg)
			and return undef
				unless $art->check_named_params(
					ERRMSG		=> \$errmsg
					,PARAMS		=> $params->{PROPERTIES}
					,MANDATORY	=> {}
					,OPTIONAL	=> {
						PTEType				=> { isa => 'SCALAR', list => ['Optical Core','Completo','Da pozzetto']}
						,PTEPosition			=> { isa => 'SCALAR', list => ['Terminale', 'Passante'] }
						,PTEPotential			=> { isa => 'SCALAR', list => ['PTE Large (48)','PTE Small (24)','PTE 12','ROE 16','ROE 32','ROE 48'] }
						,location               => { isa => 'SCALAR', list => ['INTERNO INGRESSO','INTERNO CANTINE','INTERNO GARAGE','INTERNO CORTILE','MURO ESTERNO','MURO INTERNO','ANDRONE','SOTTOSCALA','CENTRALINO','PALO','SEMINTERRATO','MARCIAPIEDE','COLONNINA O COLONNA','POZZETTO'] }
					}
					,IGNORE_EXTRA_PARAMS => 1
		);


		# verifico la coerenza tra oda e wbe

		my $contract = $activity->parent()->get_contract();

		my $oda = $params->{PROPERTIES}->{workOrderId}||$activity_property->{'workOrderId'};
		my $wbe = $params->{PROPERTIES}->{WBELevel3}||$activity_property->{'WBELevel3'};
		if (
				(defined $oda && (!defined $activity_property->{'workOrderId'} || $oda ne $activity_property->{'workOrderId'}))
				||
				$wbe ne $activity_property->{'WBELevel3'}
		) {

			my $check_oda_wbe = $activity->wpsocore->check_oda_wbe_data(
				companyAbbreviation => $contract->property('companyAbbreviation'), 
				workOrderId			=> $oda,
				WBELevel3			=> $wbe,
				externalWorkTypeId	=> $activity->system_property('externalWorkTypeId'),
			);
			return undef unless defined $check_oda_wbe;

			unless ($check_oda_wbe){
				$art->last_error(__(q{Inconsistency between work order and wbe}));
				return;
			}
		}

		if (defined $params->{PROPERTIES}->{workPerformance}){
			unless ( $activity->_workPerformance->check( workPerformance => $params->{PROPERTIES}->{workPerformance} ) ) {
				$art->last_error(__x(q{Invalid workPerformance {workPerformance}: valid values are {workPerformances} },
					workPerformance => $params->{PROPERTIES}->{workPerformance},
					workPerformances => join(', ',@{$activity->_workPerformance->workPerformances()}),
				));
				return;
			}
		}
		
		my $input_property  = $params->{PROPERTIES};

		# aggiungo le coordinate se presenti i dati dell'indirizzo
		if (defined $system_property->{city} && defined $input_property->{address} && $input_property->{address} ne $system_property->{address}){
			my $geo = $activity->wpsocore->get_geo_object;
			return undef unless $geo;

			# costruisco l'indirizzo in funzione dei parametri che ho in input
			# costruisco l'indirizzo in funzione dei parametri che ho in input
			my $address = $input_property->{address};
			$address .= sprintf ' %s', $input_property->{zipCode}||$system_property->{zipCode} if defined $system_property->{zipCode} || defined $input_property->{zipCode};
			$address .= sprintf ' %s', $input_property->{city}||$system_property->{city} if defined $system_property->{city} || defined $input_property->{city};
			$address .= sprintf ' (%s)', $input_property->{province}||$system_property->{province} if defined $system_property->{province} || defined $input_property->{province};
			
			my $location = $geo->query_by_address($address);
			
			unless (defined $location){
				$params->{PROPERTIES}->{geoCoordinatesWarning} = $geo->last_error;
			} else {
				if ($location->{partial_match}){
					$params->{PROPERTIES}->{geoCoordinatesWarning} = 'Partial';
				}
				my $geo_location = $geo->get_geo_location(LOCATION => $location, FORMAT => 'STRING');
				return undef
					unless defined $geo_location;
				$params->{PROPERTIES}->{geoCoordinates} = $geo_location;
			}	
		}

		my $map = $activity->remap_works_system_property;
		for my $key ( values %$map, 'privatePermits' ) {
			my $tdt = $key . 'Status';
			if ( defined $params->{PROPERTIES}->{$tdt} ) {
				if ( defined $system_property->{$tdt} and $system_property->{$tdt} eq 'N.N.' ) {
					if ( $params->{PROPERTIES}->{$tdt} ne 'TODO' ) {
						$params->{PROPERTIES}->{$tdt} = $system_property->{$tdt}; #N.N. può essere aggiornata solo a TODO
					}
				}
			}
		}

		$params->{PROPERTIES}->{id} =~ s{^\s+|\s+$}{}g;
		my $id = $params->{PROPERTIES}->{id};
		my $idCNO = $system_property->{idCNO};
		if ( $id ne $system_property->{id} ) {
			for my $lc02 ( @{$activity->parent->get_children( ACTIVITY_TYPE_NAME => ['LC02'] )} ) {
				next if $lc02->id == $activity->id;
				if ( $lc02->system_property('cabinetType') eq 'CNO' ) {
					if ( $lc02->system_property('idCNO') eq $idCNO && $lc02->system_property('id') eq $id ) {
						my $message = __x(q{A different PTE with id {id} already exists},id => $idCNO.'/'.$id);
						$self->art->last_error($message);
						return;
					}

				} else {
					if ( $lc02->system_property('id') eq $id ) {
						my $message = __x(q{A different PTE with id {id} already exists},id => $id);
						$self->art->last_error($message);
						return;
					}
				}
			}
			my $system = $activity->system;
			my $description = sprintf '%s-%s-%s-%s',
				$system_property->{centralId},
				$system_property->{cabinet},
				$system_property->{idCNO} // 'N/A',
				$id,
			;
			$activity->set_description($description) or return;
			$system->set_description($description) or return;
			$system->set_property(PROPERTIES => {id => $id }) or return;
		}

		$params->{PROPERTIES} = $new_activity_properties;
		$params->{PROPERTIES}->{paymentNetwork}= sprintf '%012d', $params->{PROPERTIES}->{paymentNetwork} if defined $params->{PROPERTIES}->{paymentNetwork};
		
		if (defined $params->{PROPERTIES}->{workingGroupCodeChoice} && $activity_property->{'workingGroupCode'} ne $params->{PROPERTIES}->{workingGroupCodeChoice}){
			$params->{PROPERTIES}->{__ONLY_CDL__} = 1;
			my $s = $self->SUPER::pre($activity, $params);
			return undef unless defined $s;
			delete $params->{PROPERTIES}->{__ONLY_CDL__};
		} else {
			$params->{PROPERTIES}->{workingGroupCodeChoice} = $activity_property->{'workingGroupCode'};
		}

		# CdL, WBE e ODA possono essere modificati finché il PTE non è stato preso in carico dall'AT
		my $actProps = $art->enum_activity_property(
			ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'),
			EXTENDED_OUTPUT => 1
		);
		return undef unless defined $actProps;

		if ($activity->has_external_sync()){
			for ('workingGroupCode', 'workOrderId', 'WBELevel3'){
				if (($activity_property->{$_}||'') ne ($params->{PROPERTIES}->{$_}||'')){
					$art->last_error(__x("You can not modify param '{key}' if CS01 task is already opened", key => $actProps->{$_}->{LABEL}));
					return;
				}
			}
		}

		$system_property->{paymentNetwork}= sprintf '%012d', $system_property->{paymentNetwork} if defined $system_property->{paymentNetwork};

		# se sto o impostando la prima volta o sto sbiancando paymentNetwork e ho un UpdateDatabaseF2 in corso
		# allora devo invocare il booking verso WORKS per aggiornarlo
		if (
			(defined $system_property->{wUpdateDatabaseF2Status} && $system_property->{wUpdateDatabaseF2Status} =~/^(ONGOING|SUSP)$/)
			&& (
				(defined $params->{PROPERTIES}->{paymentNetwork} && ! defined $system_property->{paymentNetwork})
				||
				(! defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork})
				||
				($params->{PROPERTIES}->{paymentNetwork} ne $system_property->{paymentNetwork})
			)
		) {
			$self->{UPD_DB2_BOOKING} = 1;
		} else {
			$self->{UPD_DB2_BOOKING} = 0;
		}

		# se sto o impostando la prima volta o sto sbiancando CLLI e ho un UpdateDatabaseF1 in corso
		# allora devo invocare il booking verso WORKS per aggiornarlo
		if (
			(defined $system_property->{wUpdateDatabaseF1Status} && $system_property->{wUpdateDatabaseF1Status} =~/^(ONGOING|SUSP)$/)
			&& (
				(defined $params->{PROPERTIES}->{CLLI} && ! defined $activity_property->{CLLI})
				||
				(! defined $params->{PROPERTIES}->{CLLI} && defined $activity_property->{CLLI})
				||
				(
					(defined $params->{PROPERTIES}->{paymentNetwork} && ! defined $system_property->{paymentNetwork})
					||
					(! defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork})
					||
					($params->{PROPERTIES}->{paymentNetwork} ne $system_property->{paymentNetwork})
				)
			)
		) {
			$self->{UPD_DB1_BOOKING} = 1;
		} else {
			$self->{UPD_DB1_BOOKING} = 0;
		}

		# gestisco l'aggancio/sgancio con la network: quando necessario
		if (
			(defined $params->{PROPERTIES}->{paymentNetwork} && ! defined $system_property->{paymentNetwork})
			||
			(
				defined $system_property->{paymentNetwork}
				&&
				defined $params->{PROPERTIES}->{workOrderId} && ! defined $activity_property->{'workOrderId'}
			)
		) {
			my $result = $activity->_get_lc02_collection->manage_network_association(
					TYPE			=> 'ASSOCIATE',
					paymentNetwork	=> $params->{PROPERTIES}->{paymentNetwork},
					LC02			=> $activity,
					workOrderId		=> $params->{PROPERTIES}->{workOrderId}||$activity_property->{'workOrderId'},
					WBELevel3		=> $params->{PROPERTIES}->{WBELevel3}||$activity_property->{'WBELevel3'},
					workingGroupCode	=> $params->{PROPERTIES}->{workingGroupCode}||$activity_property->{'workingGroupCode'},
				);
			return undef unless defined $result;
			for (
				'externalMacroActivityId',
				'externalActivityId',
			) {
				if ($activity_property->{'PTEScope'} eq 'Creation'){
					$new_system_properties->{$_} = $new_activity_properties->{$_} = ('_'.$result->{NETWORK}->system_property($_))||$activity_property->{$_};
				} else {
					$new_system_properties->{$_} = $new_activity_properties->{$_} = ($result->{NETWORK}->system_property($_))||$activity_property->{$_};
				}
			}
			for (
				'workOrderId',
				'externalMacroActivity',
				'externalActivity',
				'externalAccountingType',
			) {
				$new_system_properties->{$_} = $new_activity_properties->{$_} = ($result->{NETWORK}->system_property($_))||$activity_property->{$_};
			}
		} elsif(
			! defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork}
		) {
			return undef
				unless $activity->_get_lc02_collection->manage_network_association(
					TYPE				=> 'DEASSOCIATE',
					oldPaymentNetwork	=> $system_property->{paymentNetwork},
					LC02				=> $activity
				);
		} elsif(
			(
				defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork}
				&&
				$params->{PROPERTIES}->{paymentNetwork} ne $system_property->{paymentNetwork}
			)
			||
			(
				defined $system_property->{paymentNetwork}
				&&
				defined $params->{PROPERTIES}->{workOrderId} && defined $activity_property->{'workOrderId'}
				&&
				$params->{PROPERTIES}->{workOrderId} ne $activity_property->{'workOrderId'}
			)
		) {
			my $result = $activity->_get_lc02_collection->manage_network_association(
					TYPE				=> 'CHANGE',
					paymentNetwork		=> $params->{PROPERTIES}->{paymentNetwork},
					oldPaymentNetwork	=> $system_property->{paymentNetwork},
					LC02				=> $activity,
					workOrderId			=> $params->{PROPERTIES}->{workOrderId}||$activity_property->{'workOrderId'},
					WBELevel3			=> $params->{PROPERTIES}->{WBELevel3}||$activity_property->{'WBELevel3'},
					workingGroupCode	=> $params->{PROPERTIES}->{workingGroupCode}||$activity_property->{'workingGroupCode'},
				);
			return undef unless defined $result;
			for (
				'workOrderId',
				'externalMacroActivity',
				'externalActivity',
				'externalAccountingType',
			) {
				$new_system_properties->{$_} = $new_activity_properties->{$_} = ($result->{NETWORK}->system_property($_))||$activity_property->{$_};
			}
			for (
				'externalMacroActivityId',
				'externalActivityId',
			) {
				if ($activity_property->{'PTEScope'} eq 'Creation'){
					$new_system_properties->{$_} = $new_activity_properties->{$_} = ('_'.$result->{NETWORK}->system_property($_))||$activity_property->{$_};
				} else {
					$new_system_properties->{$_} = $new_activity_properties->{$_} = ($result->{NETWORK}->system_property($_))||$activity_property->{$_};
				}
			}
		}
	}
	
	$params->{PROPERTIES} = $new_activity_properties;

	# recupero le info dalla Wbe in caso di cambio
	if (!exists $params->{PROPERTIES}->{externalMacroActivityId} && !exists $params->{PROPERTIES}->{externalActivityId} && 
		($params->{PROPERTIES}->{WBELevel3}||'') ne ($system_property->{WBELevel3}||'')
	){
		my $contract = $activity->get_contract();
		my $wbes_info = $activity->wpsocore->get_fc_info_wbe_for_roe(
			customerId          => $system_property->{customerId},
			WBELevel3           => $params->{PROPERTIES}->{WBELevel3},
			workingGroupCode    => $params->{PROPERTIES}->{workingGroupCode},
			companyAbbreviation => $contract->property('companyAbbreviation'),
			PTEScope            => $activity_property->{PTEScope},
		);
		return undef
			unless (defined $wbes_info);

		if (scalar @{$wbes_info} == 0){
			$art->last_error(__x("No wbe info found for wbe {wbe} and working group code {wgc}", wbe => $params->{PROPERTIES}->{WBELevel3}, wgc => $params->{PROPERTIES}->{workingGroupCode}));
			return;
		} elsif (scalar @{$wbes_info} > 1){
			if ($activity_property->{PTEScope} eq 'FTTH'){
				$self->_art->last_error(__x("Found more than one wbe info for wbe {wbe} and working group code {wgc}", wbe => $params->{PROPERTIES}->{WBELevel3}, wgc => $params->{PROPERTIES}->{workingGroupCode}));
				return;
			}
		} elsif (scalar @{$wbes_info} == 1){

			if ($wbes_info->[0]->{externalWorkTypeId} ne $system_property->{externalWorkTypeId}){
				$art->last_error(__x("Unable to change WBE {wbe}: it is not possible to change external work type id", wbe => $params->{PROPERTIES}->{WBELevel3}));
				return;
			}

			for my $k ('externalMacroActivityId','externalMacroActivity','externalActivityId','externalActivity','externalAccountingType'){
				if (!defined $wbes_info->[0]->{$k}){
					$self->art->last_error(__x("{param} not found for wbe: {wbe}", param => $k, wbe => $params->{PROPERTIES}->{WBELevel3}));
					return;
				} else {
					$new_system_properties->{$k} = $params->{PROPERTIES}->{$k} = $wbes_info->[0]->{$k};
				}
			}
		}
	}

	# Verifico l'indice di penetrazione se è ftth
	if ( $activity->is_ftth ) {
		my $externalAccountingType = $new_system_properties->{externalAccountingType}||$system_property->{externalAccountingType};
		if (defined $externalAccountingType && $externalAccountingType =~/^(A CORPO|A CORPO 24)$/){
			my $cluster = $activity_property->{'clusterJobReport'};
			if  ( defined $cluster ) {
				my $penetrationIndex = $params->{PROPERTIES}->{penetrationIndex};
				unless ( defined $penetrationIndex ) {
					$art->last_error(__(q{Parameter penetrationIndex must be present for a FTTH PTE}));
					return;
				}
				unless ( $activity->_cluster->checkPenetrationIndex( clusterJobReport => $cluster, penetrationIndex => $penetrationIndex ) ) {
					$art->last_error(__x(q{Invalid penetrationIndex {penetrationIndex} for cluster {cluster} valid values are {allowedPIs} },
						penetrationIndex => $penetrationIndex,
						cluster => $cluster,
						allowedPIs => join(', ',@{$activity->_cluster->penetrationIndexes($cluster)}),
					));
					return;
				}
			} else {
				# Errore in caso di pregresso non sanato
				$art->last_error(__(q{Parameter cluster must be defined for a FTTH PTE, please contact system administrator}));
				return;
			}
		} else {
			for ('clusterJobReport', 'penetrationIndex'){
                if ( defined $params->{PROPERTIES}->{$_} ) {
                    $art->last_error(__x(q{Parameter {param} must not be present for a FTTH PTE with accounting {accounting}}, param => $_, accounting => $externalAccountingType));
                    return;
                }
				# in ogni caso devo sbiancarli
				$new_system_properties->{$_} = $params->{PROPERTIES}->{$_} = undef;
            }
		}
	}

	my $externalMacroActivityId = $params->{PROPERTIES}->{externalMacroActivityId}||$activity_property->{'externalMacroActivityId'};
	if ($externalMacroActivityId eq 'FCOP2024'){
		$params->{PROPERTIES}->{subcontractItem} = $activity->wpsocore()->get_subcontract_item_name(
			customerId  => $system_property->{'customerId'},
			contractId  => $system_property->{'contractId'},
			name        => $externalMacroActivityId,
			DETAILS     => {
				centralId => $system_property->{centralId},
				penetrationIndex => $params->{PROPERTIES}->{penetrationIndex}||$activity_property->{'penetrationIndex'}
			}
		); 
		return undef unless defined $params->{PROPERTIES}->{subcontractItem};
		$new_system_properties->{subcontractItem} = $params->{PROPERTIES}->{subcontractItem};
	}

	# aggiorno le property del sistema (quelle dell'attività sono successivamente filtrate)
	return undef
		unless $activity->system->set_property(PROPERTIES => $new_system_properties);

	return 1;
}

sub post {

	my ( $self, $activity, $params) = @_;

    my $art = $self->{ART};	

	# se sto o impostando la prima volta o sto sbiancando paymentNetwork e ho un UpdateDatabaseF2 in corso
	# allora devo invocare il booking verso WORKS per aggiornarlo
	if ($self->{UPD_DB1_BOOKING} || $self->{UPD_DB2_BOOKING}){
		my $system_property = $activity->system_property('customerId','contractId','wUpdateDatabaseF1Status','wUpdateDatabaseF2Status');
		if ($self->{UPD_DB2_BOOKING}){
			return undef
				unless $activity->booking(
					bookingType		=> 'STEP',
					bookingStatus	=> $system_property->{wUpdateDatabaseF2Status},
					action			=> "AGGIORNAMENTO_DATI",
					ref00			=> $activity->system()->parent()->id(),
					ref01			=> $activity->system()->id(),
					customerId		=> $system_property->{customerId},
					contractId		=> $system_property->{contractId},
					details			=> [
						{
							type => 'updateDatabaseF2'
						}
					],
					operatorLogin => $art->user()->name(),
					FORCE => 1
				);
		}
		if ($self->{UPD_DB1_BOOKING}){
			return undef
				unless $activity->booking(
					bookingType		=> 'STEP',
					bookingStatus	=> $system_property->{wUpdateDatabaseF1Status},
					action			=> "AGGIORNAMENTO_DATI",
					ref00			=> $activity->system()->parent()->id(),
					ref01			=> $activity->system()->id(),
					customerId		=> $system_property->{customerId},
					contractId		=> $system_property->{contractId},
					details			=> [
						{
							type => 'updateDatabaseF1'
						}
					],
					operatorLogin => $art->user()->name(),
					FORCE => 1
				);
		}
	}

	return 1;
}

#UNIT TEST
if (__FILE__ eq $0) {

    use API::ART;
    use API::ART::Activity::Factory;
    use Data::Dumper;

    my $art;
    eval {
        $art = API::ART->new(
            ARTID => $ENV{ARTID},
            USER => $ENV{WPSOCORE_ADMIN_USER},
            PASSWORD => $ENV{WPSOCORE_ADMIN_PASSWORD},
        );
    };
    if ($@) {
        die "Login error: $@";
    }

    my %params = (
		PROPERTIES => {
			PTEPosition => 'Terminale',
		},
    );

	my $params = { %params };

    my $activity = API::ART::Activity::Factory->new( ART => $art, ID => 63948) or die $art->last_error;
    my $binding = API::ART::APP::Activity::LC02::Binding::Action::AGGIORNAMENTO_DATI->new($activity) or die $art->last_error;
    print Dumper($activity->system_property);
    $binding->pre($activity,$params) or warn $art->last_error;
    print Dumper($activity->system_property);

    $art->cancel;
}

1;
