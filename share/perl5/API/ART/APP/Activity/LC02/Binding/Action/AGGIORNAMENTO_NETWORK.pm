package API::ART::APP::Activity::LC02::Binding::Action::AGGIORNAMENTO_NETWORK;

use strict;
use warnings;
use Array::Utils qw(:all);
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::LC02::Binding::Action::AGGIORNAMENTO_DATI);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};
	
	# normalizzo la network
	$params->{PROPERTIES}->{paymentNetwork}= sprintf '%012d', $params->{PROPERTIES}->{paymentNetwork} if defined $params->{PROPERTIES}->{paymentNetwork};

	# le estraggo per efficienza
	my $system_property = $activity->system_property('paymentNetwork','wUpdateDatabaseF1Status','wUpdateDatabaseF1LAS','wUpdateDatabaseF2Status','wUpdateDatabaseF2LAS');

	# gestisco l'aggancio/sgancio con la network: quando necessario
	if (defined $params->{PROPERTIES}->{paymentNetwork} && ! defined $system_property->{paymentNetwork}) {
		my $result = $activity->_get_lc02_collection->manage_network_association(
				TYPE			=> 'ASSOCIATE',
				paymentNetwork	=> $params->{PROPERTIES}->{paymentNetwork},
				LC02			=> $activity,
				workOrderId		=> $params->{PROPERTIES}->{workOrderId}||$activity->activity_property('workOrderId')
			);
		return undef unless defined $result;
		for (
			'workOrderId',
			'externalMacroActivity',
			'externalActivity',
			'externalAccountingType',
		) {
			$params->{PROPERTIES}->{$_} = defined $activity->activity_property($_) ? $activity->activity_property($_) : $result->{NETWORK}->system_property($_);
		}
		for (
			'externalMacroActivityId',
			'externalActivityId',
		) {
			if ($activity->activity_property('PTEScope') eq 'Creation') {
				$params->{PROPERTIES}->{$_} = ('_'.$result->{NETWORK}->system_property($_))||$activity->activity_property($_);
			} else {
				$params->{PROPERTIES}->{$_} = $result->{NETWORK}->system_property($_)||$activity->activity_property($_);
			}
		}
	} elsif(! defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork}) {
		return undef
			unless $activity->_get_lc02_collection->manage_network_association(
				TYPE				=> 'DEASSOCIATE',
				oldPaymentNetwork	=> $system_property->{paymentNetwork},
				LC02				=> $activity
			);
	} elsif(
		(
			defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork}
			&&
			$params->{PROPERTIES}->{paymentNetwork} ne $system_property->{paymentNetwork}
		)
	) {
		my $result = $activity->_get_lc02_collection->manage_network_association(
				TYPE				=> 'CHANGE',
				paymentNetwork		=> $params->{PROPERTIES}->{paymentNetwork},
				oldPaymentNetwork	=> $system_property->{paymentNetwork},
				LC02				=> $activity,
				workOrderId	=> $params->{PROPERTIES}->{workOrderId}||$activity->activity_property('workOrderId')
			);
		return undef unless defined $result;
		for (
                        'workOrderId',
                        'externalMacroActivity',
                        'externalActivity',
                        'externalAccountingType',
                ) {
                        $params->{PROPERTIES}->{$_} = defined $activity->activity_property($_) ? $activity->activity_property($_) : $result->{NETWORK}->system_property($_);
                }
                for (
                        'externalMacroActivityId',
                        'externalActivityId',
                ) {
			if ($activity->activity_property('PTEScope') eq 'Creation') {
				$params->{PROPERTIES}->{$_} = ('_'.$result->{NETWORK}->system_property($_))||$activity->activity_property($_);
			} else {
				$params->{PROPERTIES}->{$_} = $result->{NETWORK}->system_property($_)||$activity->activity_property($_);
			}
                }
	}

	# aggiorno le property del sistema (quelle dell'attività sono successivamente filtrate)
	return undef
		unless $activity->system->set_property(PROPERTIES => {
			paymentNetwork	=> $params->{PROPERTIES}->{paymentNetwork},
			workOrderId		=> ($params->{PROPERTIES}->{workOrderId} || $activity->activity_property('workOrderId')),
			externalMacroActivityId		=> ($params->{PROPERTIES}->{externalMacroActivityId} || $activity->activity_property('externalMacroActivityId')),
			externalMacroActivity		=> ($params->{PROPERTIES}->{externalMacroActivity} || $activity->activity_property('externalMacroActivity')),
			externalActivityId		=> ($params->{PROPERTIES}->{externalActivityId} || $activity->activity_property('externalActivityId')),
			externalActivity		=> ($params->{PROPERTIES}->{externalActivity} || $activity->activity_property('externalActivityId')),
			externalAccountingType		=> ($params->{PROPERTIES}->{externalAccountingType} || $activity->activity_property('externalAccountingType')),
		});

	# se sto o impostando la prima volta o sto sbiancando paymentNetwork e ho un UpdateDatabaseF2 in corso
	# allora devo invocare il booking verso WORKS per aggiornarlo
	if (
		(defined $system_property->{wUpdateDatabaseF2Status} && $system_property->{wUpdateDatabaseF2Status} =~/^(ONGOING|SUSP)$/)
		&& (
			(defined $params->{PROPERTIES}->{paymentNetwork} && ! defined $system_property->{paymentNetwork})
			||
			(! defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork})
			||
			($params->{PROPERTIES}->{paymentNetwork} ne $system_property->{paymentNetwork})
		)
	) {
		$self->{UPD_DB2_BOOKING} = 1;
	} else {
		$self->{UPD_DB2_BOOKING} = 0;
	}

	if (
		(defined $system_property->{wUpdateDatabaseF1Status} && $system_property->{wUpdateDatabaseF1Status} =~/^(ONGOING|SUSP)$/)
		&& (
			(defined $params->{PROPERTIES}->{paymentNetwork} && ! defined $system_property->{paymentNetwork})
			||
			(! defined $params->{PROPERTIES}->{paymentNetwork} && defined $system_property->{paymentNetwork})
			||
			($params->{PROPERTIES}->{paymentNetwork} ne $system_property->{paymentNetwork})
		)
	) {
		$self->{UPD_DB1_BOOKING} = 1;
	} else {
		$self->{UPD_DB1_BOOKING} = 0;
	}

	return 1;
}

1;
