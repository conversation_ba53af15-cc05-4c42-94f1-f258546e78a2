package API::ART::APP::Activity::BUILDING_LC::Binding::Action::CHIUSURA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	# recupero tutti i figli e li chiudo
	my $children = $activity->get_children();
	return undef unless defined $children;
	
	for my $ch(@{$children}){
		# se è già in uno stato finale non devo fare nulla
		next if $ch->is_closed();
		$art->last_error(__x("Unable to close activity {id} because the child activity {childId} is not close", id => $activity->id(), childId => $ch->id()))
			&& return undef
				unless $ch->can_do_action(NAME => 'CHIUSURA');
		return undef
			unless $ch->step(ACTION => 'CHIUSURA');
	}

	return 1;
}

1;
