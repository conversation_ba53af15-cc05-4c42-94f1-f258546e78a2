package API::ART::APP::Activity::BUILDING_LC::Binding::Status::ACQUISITA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Status::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $list_ap = $self->art()->enum_activity_property(ACTIVITY_TYPE_NAME => $activity->info('ACTIVITY_TYPE_NAME'));
	
	my $activity_property = $activity->activity_property();
	my $system_property = $activity->system()->property();
	
	my %properties_to_set = ();
	my @properties_to_delete = ();
	for my $dt (@{$activity->system()->info('PROPERTIES')}) {
		# modifico solo di dt sistema che sono anche activity_property
		next unless exists($list_ap->{$dt});
		my $act_prop = $activity_property->{$dt};
		my $system_prop = $system_property->{$dt};
		if(
			defined $act_prop && !defined $system_prop
			||
			defined $act_prop && $act_prop ne $system_prop
		) {
			# Property {property} to add or modify
			$properties_to_set{$dt} = $act_prop;
		}
		if(!defined $act_prop && defined $system_prop) {
			# Property {property} to delete
			push @properties_to_delete, $dt;
		}
	}

	if(scalar keys %properties_to_set) {
		return undef
			unless $activity->system()->set_property( PROPERTIES => \%properties_to_set );
	}
	if(scalar @properties_to_delete) {
		return undef
			unless $activity->system()->delete_property( @properties_to_delete );
	}

	return 1;
};

1;
