package API::ART::APP::Activity::NETWORK::Binding::Action::AGGIORNAMENTO_ARMADIO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub can_do {
	my ( $self, $activity) = @_;

	return 1 if $activity->art->user()->is_admin();
	
	return 1 if $activity->{__CALLER__};

	return 1 if ($activity->is_ftth && defined $activity->activity_property('networkPurpose') && $activity->activity_property('networkPurpose') =~/^(Primaria)$/);

	$activity->art()->last_error(__("Action not available for non primary FTTH network"));
	return undef;
}

sub pre {
	my ( $self, $activity, $params) = @_;
	my $errmsg;

	my $art = $self->art;

	my $sys_property = $activity->system->property;
	my $centralId		= $sys_property->{centralId};

	# Verifico che l'AT sia ancora valido
	my $ats = $activity->get_teams()->cerca(
		companyCard                     => $activity->property('technicalAssistantId'),
		companyAbbreviation     => $activity->system()->parent()->property('companyAbbreviation'),
	);
	return undef unless defined $ats;

	$art->last_error(__x("Technical assistant {technicalAssistantName} not more available: please first execute action {action}"
		, technicalAssistantName => $activity->property('technicalAssistantId') 
		, action => 'AGGIORNA_AT'
	))
		&& return undef
				unless scalar @{$ats};

	# se mi arrivano sia cabinet che cabinetId significa che è già stato trovato l'armadio di riferimento e mi fido
	# devo solo controllare che l'armadio non sia già associato
	if (
		defined $params->{PROPERTIES}->{cabinet}
		&&
		defined $params->{PROPERTIES}->{cabinetId}
		&&
		!$params->{PROPERTIES}->{__DEASSOCIATE__}
	){
		if (defined $activity->activity_property('networkPurpose') && $activity->activity_property('networkPurpose') =~/^(Gestione armadio)$/){
			if (grep {$_ eq $params->{PROPERTIES}->{cabinetId}}@{$sys_property->{'centralCabinetId'}}){
				return 1;
			}
		} else {
			$art->last_error(__x("Network already associated to cabinet {cabinet}", cabinet => $sys_property->{id}))
				&& return undef
					if scalar @{$sys_property->{'centralCabinetId'}} && $sys_property->{'centralCabinetId'}->[0] ne $params->{PROPERTIES}->{cabinetId};
		}
		return undef unless $activity->system->set_property(
			PROPERTIES => {
				centralCabinetId	=> [@{$sys_property->{'centralCabinetId'}}, $params->{PROPERTIES}->{cabinetId}],
				centralCabinet		=> [@{$sys_property->{'centralCabinet'}}, $params->{PROPERTIES}->{cabinet}],
			}
		);

		# non serve nelle activity property quindi lo rimuovo
		delete $params->{PROPERTIES}->{cabinetId};

		return 1;
	} elsif (
		defined $params->{PROPERTIES}->{cabinet}
		&&
		!defined $params->{PROPERTIES}->{cabinetId}
	){ # qui ci entra solo per la gestione Primaria
		if ($activity->activity_property('networkPurpose') =~/^(Gestione armadio)$/){
			$art->last_error(__x("Action not available for network with scope {scope}", scope => $activity->activity_property('networkPurpose')));
			return undef;
		}

		if ($activity->is_creation){
			$art->last_error(__x("Action not available for network {scope}", scope => 'Creation'));
			return undef;
		}

		# si tratta di un'azione scatenata dalla network e quindi devo agganciarmi al relativo armadio se lo trovo
		my $coll_lc01 = $activity->get_lc01_collection or return;
		my $cerca = $coll_lc01->cerca(
			customerId	=> $sys_property->{customerId},
			contractId	=> $sys_property->{contractId},
			centralId	=> $centralId,
			id			=> $params->{PROPERTIES}->{cabinet},

		) or return;

		unless (scalar @{$cerca}){
			$art->last_error(__x("Cabinet {cabinet} not found", cabinet => $params->{PROPERTIES}->{cabinet}));
			return undef;
		}

		my $lc01_activity = $cerca->[0];
		# se trovato armadio ed già lo stesso presente non devo fare nulla
		return 1 if grep {$_ eq $lc01_activity->id()} @{$sys_property->{'centralCabinetId'}};

		# Sovrascrivo con il nuovo armadio
		return undef unless $activity->system->set_property(
			PROPERTIES => {
				centralCabinetId => [ $lc01_activity->id()],
				centralCabinet => [ $lc01_activity->system_property('centralId').'-'.$lc01_activity->system_property('id')],
			}
		);

		return 1;
	} elsif ( # se Gestione armadio lo sgancio può avvenire solo a partire dai PTE
		!defined $params->{PROPERTIES}->{cabinet}
		&&
		defined $activity->activity_property('networkPurpose') && $activity->activity_property('networkPurpose') =~/^(Gestione armadio)$/
		&&
		!defined $activity->{__CALLER__}
	){
		$art->last_error(__x("Unable to deassociate cabinet {cabinet} from network {network}", cabinet => $params->{PROPERTIES}->{cabinet}, network => $activity->activity_property('networkId')));
		return undef;
	} elsif ( # se Gestione armadio lo sgancio può avvenire solo a partire dai PTE che hanno già valutato se necessario sganciare la network
		defined $activity->activity_property('networkPurpose') && $activity->activity_property('networkPurpose') =~/^(Gestione armadio)$/
		&&
		defined $activity->{__CALLER__}
		&&
		$params->{PROPERTIES}->{__DEASSOCIATE__}
	) {
		my $new_central_cabinet = [];
		my $new_central_cabinetId = [];
		for my $pp (@{$sys_property->{centralCabinetId}}){
			push @{$new_central_cabinetId}, $pp if $pp ne $params->{PROPERTIES}->{cabinetId};
		}
		for my $pp (@{$sys_property->{centralCabinet}}){
			push @{$new_central_cabinet}, $pp if $pp ne $params->{PROPERTIES}->{cabinet};
		}

		return undef unless $activity->system->set_property(
			PROPERTIES => {
				centralCabinetId => $new_central_cabinetId,
				centralCabinet => $new_central_cabinet,
			}
		);

		my $add_description = __x("Deassociation for cabinet {cabinet}", cabinet => $params->{PROPERTIES}->{cabinet});

		if (defined $params->{DESCRIPTION}) {
			$params->{DESCRIPTION} .= '|'.$add_description;
		} else {
			$params->{DESCRIPTION} = $add_description;
		}

		for ('__DEASSOCIATE__', 'cabinetId'){
			delete $params->{PROPERTIES}->{$_};
		}
		$params->{PROPERTIES}->{'cabinet'} = '';
	} elsif ( # se Gestione armadio lo sgancio può avvenire solo a partire dai PTE che hanno già valutato se necessario sganciare la network
		!defined $params->{PROPERTIES}->{cabinet}
		&&
		$activity->activity_property('networkPurpose') =~/^(Primaria)$/
		&& 
		scalar @{$sys_property->{'centralCabinet'}}
	) {
		return undef unless $activity->system->set_property(
			PROPERTIES => {
				centralCabinetId => [],
				centralCabinet => [],
			}
		);

	} elsif (
		$activity->is_creation()
		&&
		$params->{PROPERTIES}->{__DEASSOCIATE__}
	) {
		return undef unless $activity->system->set_property(
			PROPERTIES => {
				centralCabinetId => [],
				centralCabinet => [],
			}
		);

		#delete $params->{PROPERTIES}->{cabinet};
		my $add_description = __x("Deassociation for cabinet {cabinet}", cabinet => $params->{PROPERTIES}->{cabinet});
		$params->{DESCRIPTION} = $add_description;
		$params->{PROPERTIES}->{'cabinet'} = '';
	}
	
	for ('cabinetId','__DEASSOCIATE__'){
		delete $params->{PROPERTIES}->{$_} if exists $params->{PROPERTIES}->{$_};
	}

	return 1;
}

1;
