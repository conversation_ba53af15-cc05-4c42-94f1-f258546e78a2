package API::ART::APP::Activity::LC06;

use strict;
use warnings;
use JSON;
use base qw(API::ART::Activity::Binding);
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Array::Utils qw(:all);

use WPSOAP;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

our $DEFAULT_SYSTEM_CLASS = 'WPSOAP::System::EL06';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art->_dbh }

sub wpsoap {
	my $self = shift;
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art()) unless exists $self->{WPSOAP};
	
	return $self->{WPSOAP};
}

# effettuiamo ovverride metodo step per gestire le system_property
sub step(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return undef unless $self->SUPER::step(%params);

	# faccio in modo che tutte le eventuali property del sistema vengano aggiornate
	my @supported_sp = @{$self->system->info('PROPERTIES')}; 
	my $activity_property = $self->activity_property();
	my @defined_properties = keys %{$activity_property };
	my @filtered_keys = intersect(@defined_properties,@supported_sp);
	my %filtered_params = ();
	@filtered_params{@filtered_keys}=@{$activity_property }{@filtered_keys};
	return undef unless $self->system()->set_property(PROPERTIES => \%filtered_params);
	return 1;
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub add_documentation(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return $self->SUPER::add_documentation(%params);
}

sub _manage_attachments {
	my $self = shift;
	my %params = @_;

	my $meta = {
		activityId		=> $self->id(),
		activityType	=> $self->info('ACTIVITY_TYPE_NAME'),
		siteId			=> $self->activity_property('siteId'),
		centralId		=> $self->activity_property('centralId'),
	};
	$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
		&& return undef 
			if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
	for my $att (@{$params{ATTACHMENTS}}){
		if (!ref ($att)){
			# lo trasformo in una struttura per gestire i meta
			$att = {
				FILENAME => $att,
				META => $meta
			};
		} elsif (ref($att) eq 'HASH'){
			# aggiungo la chiave META se non esiste
			if (!exists $att->{META}){
				$att->{META} = $meta
			} else {
				# aggiungo le chiavi sovrascrivendole se già presenti
				for my $k (keys %{$meta}){
					$att->{META}->{$k} = $meta->{$k};
				}
			}
			
		} else {
			$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
			return undef; 
		}
	}

	return 1;

}

if (__FILE__ eq $0) {

    use API::ART;
    use API::ART::Activity::Factory;
    use Data::Dumper;

	my $art = API::ART->new(
		ARTID => $ENV{ARTID},
		USER => 'VASILE',  # ADMIN
		#USER => 'FC_AT_106656', # AT
		#USER => 'PM_TIM', # PM
		#USER => 'XT05420', # SERVICE
		#USER => 'XT03870', # SERVICE
		PASSWORD => 'pippo123'
	) or die 'Internal error';

    my %params = (
			"bookingType" => "STEP",
			"bookingStatus" => "OK",
			"action" => "FINE_LAVORI",
			"properties" => {
				"startWorkDate" => "2021-03-26T02:00:00.000000000+02:00",
				"endWorkDate" => "2021-03-26T02:00:00.000000000+02:00"
			},
			"details" => [
				{
					"type" => "laying"
				},
				{
					"type" => "privatePermits"
				},
			],
    );

	my $activity = API::ART::Activity::Factory->new( ART => $art, ID => 63468 ) or die $art->last_error;
	$activity->booking(%params) or die $art->last_error;

    $art->cancel;
}

1;


