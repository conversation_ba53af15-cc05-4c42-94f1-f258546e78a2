package API::ART::APP::Activity::AP_LC;

use strict;
use warnings;
use JSON;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art()->_dbh() }

sub get_totals_ap{
	my $self = shift;
	
	# recupero i figli
	my $children = $self->get_children(ACTIVITY_TYPE_NAME => ['PERMESSO_LAVORI']);
	return undef unless $children;
	
	my $ret = {
		totalPermits	=> 0,
		grantedPermits	=> 0,
		deniedPermits	=> 0
	};
	
	for my $ch (@{$children}){
		if ($ch->get_current_status_name eq 'OTTENUTA'){
			$ret->{totalPermits}++;
			$ret->{grantedPermits}++;
		} elsif ($ch->get_current_status_name eq 'RESPINTA'){
			$ret->{totalPermits}++;
			$ret->{deniedPermits}++;
		} elsif ($ch->is_active()){
			$ret->{totalPermits}++;
		} else {
			# in tutti gli altri stati finali non devono essere conteggiati in nessuna chiave
		}
	}
	
	return $ret;
}

sub update_permits_count{
	my $self = shift;
	
	# recupero i figli
	my $children = $self->get_children(ACTIVITY_TYPE_NAME => ['PERMESSO_LAVORI']);
	return undef unless $children;
	
	my $update = {
		ongoingPermits		=> 0,
		acceptedPermits		=> 0,
		deniedPermits		=> 0,
		cancelledPermits	=> 0
	};
	
	for my $ch (@{$children}){
		if ($ch->get_current_status_name eq 'OTTENUTA'){
			$update->{acceptedPermits}++;
		} elsif ($ch->get_current_status_name eq 'RESPINTA'){
			$update->{deniedPermits}++;
		} elsif (!$ch->is_active()){
			# tutti gli altri stati finali defono essere conteggiati come cancelledPermits
			$update->{cancelledPermits}++;
		} else { # $ch->is_active()
			# tutti gli stati non finali eccetto OTTENUTA e RESPINTA
			$update->{ongoingPermits}++;
		}
	}
	
	return $self->system()->set_property( PROPERTIES => $update );
}

sub cancellazione{
	my $self = shift;
	
	# conto il numero di step APERTURA_TT e CHIUSURA_TT: se sono uguali procedo con la cancellazione 
	# altrimenti registro il tentativo di cancellazione
	## capisco da dove sono partito prima di andare in SOSPESA per sapere dove tornare
	my $history = $self->history();
	return undef unless $history;

	# imposto il filtro
	return undef unless defined $history->set_filter(
		ACTION => [
			'APERTURA_TT'
			,'CHIUSURA_TT'
		]
	);
	
	# sommo 1 in caso di apertura e sottraggo in caso di chisura: alla fine mi aspetto 0
	my $count = 0;
	while( my $transition = $history->next ) {
		if ($transition->action_name() eq 'APERTURA_TT'){
			$count++;
		} else {
			$count--;
		} 
	}
	
	if ($count){
		return $self->step(
			ACTION => 'TENTATIVO_CANCELLAZIONE'
			, PROPERTIES => {
				reasonCancelKO => __x("Found {n} tts ongoing", n => $count)
			}
		);
	} else {
		return $self->step(
			ACTION => 'CANCELLAZIONE'
		);
	}
}

1;
