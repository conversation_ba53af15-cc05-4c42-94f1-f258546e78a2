package API::ART::APP::Activity::TT001;

use strict;
use warnings;
use JSON;
use base qw(API::ART::Activity::Binding);
use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);
use Array::Utils qw(:all);

use WPSOAP;
use WPSOAP::Teams;
use WPSOAP::Subcontracts;
use WPSOAP::MQ::Sender::TT001;
use WPSOAP::Collection::Activity::TT001;
use WPSOAP::Collection::System::CUSTOMER;
use WPSOAP::Collection::System::CONTRACT;
use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

our $DEFAULT_SYSTEM_CLASS = 'WPSOAP::System::TT001';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art->_dbh }

sub get_collection_customer {
	my $self = shift;
	
	$self->{WPSOAP_COLL_CUSTOMER} = WPSOAP::Collection::System::CUSTOMER->new(ART => $self->art()) unless exists $self->{WPSOAP_COLL_CUSTOMER};
	
	return $self->{WPSOAP_COLL_CUSTOMER};
}

sub get_customer {
	my $self = shift;
	unless (defined $self->{WPSOAP_CUSTOMER}){
		my $cerca = $self->get_collection_customer->cerca(customerId => $self->system_property('customerId'));
	
		$self->{WPSOAP_CUSTOMER} = $cerca->[0];
	}

	return $self->{WPSOAP_CUSTOMER};
}

sub get_collection_contract {
	my $self = shift;
	
	$self->{WPSOAP_COLL_CONTRACT} = WPSOAP::Collection::System::CONTRACT->new(ART => $self->art(), CUSTOMER => $self->get_customer()) unless exists $self->{WPSOAP_COLL_CONTRACT};
	
	return $self->{WPSOAP_COLL_CONTRACT};
}

sub get_contract {
	my $self = shift;
	unless (defined $self->{WPSOAP_CONTRACT}){
		my $cerca = $self->get_collection_contract->cerca(contractId => $self->system_property('contractId'));
	
		$self->{WPSOAP_CONTRACT} = $cerca->[0];
	}

	return $self->{WPSOAP_CONTRACT};
}

sub wpsoap {
	my $self = shift;
	
	$self->{WPSOAP} = WPSOAP->new(ART => $self->art()) unless exists $self->{WPSOAP};
	
	return $self->{WPSOAP};
}

sub get_teams {
	my $self = shift;
	
	$self->{TEAMS} = WPSOAP::Teams->new(ART => $self->art()) unless exists $self->{TEAMS};
	
	return $self->{TEAMS};
}

sub get_subContracts {
	my $self = shift;
	
	$self->{SUBCONTRACTS} = WPSOAP::Subcontracts->new(ART => $self->art()) unless exists $self->{SUBCONTRACTS};
	
	return $self->{SUBCONTRACTS};
}

sub get_coll_activity {
	my $self = shift;
	
	$self->{WPSOAP_COLL_ACT} = WPSOAP::Collection::Activity::TT001->new(ART => $self->art()) unless exists $self->{WPSOAP_COLL_ACT};
	
	return $self->{WPSOAP_COLL_ACT};
}

# effettuiamo ovverride metodo step per gestire le system_property
sub step(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return undef unless $self->SUPER::step(%params);

	# faccio in modo che tutte le eventuali property del sistema vengano aggiornate
	my @supported_sp = @{$self->system->info('PROPERTIES')}; 
	my $activity_property = $self->activity_property();
	my @defined_properties = keys %{$activity_property };
	my @filtered_keys = intersect(@defined_properties,@supported_sp);
	my %filtered_params = ();
	@filtered_params{@filtered_keys}=@{$activity_property }{@filtered_keys};
	return undef unless $self->system()->set_property(PROPERTIES => \%filtered_params);

	my $data = $self->get_data_for_notify_event(ACTION => $params{ACTION});
	# use Data::Dumper;
	# print STDERR Dumper $data;
	$self->art()->last_error()
		&& return undef
			unless defined $self->get_sender()->notify_event(%{$data});
	return 1;
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub add_documentation(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return $self->SUPER::add_documentation(%params);
}

sub _manage_attachments {
	my $self = shift;
	my %params = @_;

	my $meta = {
		activityId		=> $self->id(),
		activityType	=> $self->info('ACTIVITY_TYPE_NAME'),
		networkId		=> $self->activity_property('networkId'),
		customerSystem	=> $self->activity_property('customerSystem'),
	};
	$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
		&& return undef 
			if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
	for my $att (@{$params{ATTACHMENTS}}){
		if (!ref ($att)){
			# lo trasformo in una struttura per gestire i meta
			$att = {
				FILENAME => $att,
				META => $meta
			};
		} elsif (ref($att) eq 'HASH'){
			# aggiungo la chiave META se non esiste
			if (!exists $att->{META}){
				$att->{META} = $meta
			} else {
				# aggiungo le chiavi sovrascrivendole se già presenti
				for my $k (keys %{$meta}){
					$att->{META}->{$k} = $meta->{$k};
				}
			}
			
		} else {
			$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
			return undef; 
		}
	}

	return 1;

}

sub get_data_for_notify_event{
	my $self = shift;
	my %params = @_;
	my $errmsg;
	$self->art()->last_error($errmsg)
		and return undef
			unless $self->art()->check_named_params(
				 ERRMSG		=> \$errmsg
				,PARAMS		=> \%params
				,MANDATORY	=> {
				}
				,OPTIONAL	=> {
					ACTION		=> { isa => 'SCALAR' }
				}
				,IGNORE_EXTRA_PARAMS => 1
	);
	#my $property = $self->activity_property();
		
	#my $system_property = $self->system_property();
	
	my $data = {
		SOURCE_REF => $self->id()
		#, TYPE => uc($property->{targetAsset})
		, DATA => {
			#ACTIVITY_ID => $self->id(),
			ACTIVITY	=> to_json($self->dump()),
			# STATUS		=> $self->get_current_status_name(),
			# customerId => $system_property->{customerId},
			# contractId => $system_property->{contractId},
		}
	};

	if($self->is_closed){
		$data->{DATA}->{FINAL_STATUS_TYPE} = $self->get_current_status_name() =~/^(RISOLTA|RISOLTA_CLIENTE)$/ ? 'OK' : 'KO';
	}
	$data->{DATA}->{ACTION} = $params{ACTION} if defined $params{ACTION};
	$data->{DATA}->{STATS_TOTAL} = 0;
	$data->{DATA}->{STATS_KO} = 0;
	$data->{DATA}->{STATS_OK} = 0;
	$data->{DATA}->{STATS_ONGOING} = 0;
	# imposto per poter vedere tutte le attività
	$self->art()->user()->su()->activity()->enable( $self->art()->get_group_id($ENV{ART_BINDING_SUDO_GROUP}) );
	my $cerca = $self->get_coll_activity()->cerca(
           customerId => $self->activity_property('customerId'),
           networkId => $self->activity_property('networkId'),
	   __FIND_ALL__ => 1
        );
        return undef unless defined $cerca;
        for my $ah (@{$cerca}){
		$data->{DATA}->{STATS_TOTAL}++;
		if ($ah->is_active()){
			$data->{DATA}->{STATS_ONGOING}++;
		} else {
			if ($ah->get_current_status_name() =~/^(RISOLTA|RISOLTA_CLIENTE)$/){
				$data->{DATA}->{STATS_OK}++;
			} else {
				$data->{DATA}->{STATS_KO}++;
			}
		}
		if (!exists $data->{DATA}->{LAST_ID} || $ah->id() > $data->{DATA}->{LAST_ID}){
			$data->{DATA}->{LAST_ID} = $ah->id();
			$data->{DATA}->{LAST_STATUS} = $ah->get_current_status_name();
			$data->{DATA}->{LAST_CUSTOMER_SYSTEM} = $ah->activity_property('customerSystem');
			$data->{DATA}->{LAST_CUSTOMER_TICKET_ID} = $ah->activity_property('customerTicketId');
		}
        }
	$self->art()->user()->su()->activity()->disable();
	return $data;
}

sub get_sender {
	my $self = shift;
	$self->{SENDER} = WPSOAP::MQ::Sender::TT001->instance(DB => $self->get_db());
	return $self->{SENDER};
}

if (__FILE__ eq $0) {

    use API::ART;
    use API::ART::Activity::Factory;
    use Data::Dumper;

	my $art = API::ART->new(
		ARTID => $ENV{ARTID},
		USER => 'VASILE',  # ADMIN
		#USER => 'FC_AT_106656', # AT
		#USER => 'PM_TIM', # PM
		#USER => 'XT05420', # SERVICE
		#USER => 'XT03870', # SERVICE
		PASSWORD => 'pippo123'
	) or die 'Internal error';

    my %params = (
			"bookingType" => "STEP",
			"bookingStatus" => "OK",
			"action" => "FINE_LAVORI",
			"properties" => {
				"startWorkDate" => "2021-03-26T02:00:00.000000000+02:00",
				"endWorkDate" => "2021-03-26T02:00:00.000000000+02:00"
			},
			"details" => [
				{
					"type" => "laying"
				},
				{
					"type" => "privatePermits"
				},
			],
    );

	my $activity = API::ART::Activity::Factory->new( ART => $art, ID => 63468 ) or die $art->last_error;
	$activity->booking(%params) or die $art->last_error;

    $art->cancel;
}

1;


