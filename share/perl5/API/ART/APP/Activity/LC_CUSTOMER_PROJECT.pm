package API::ART::APP::Activity::LC_CUSTOMER_PROJECT;

use strict;
use warnings;
use JSON;

use base qw(API::ART::Activity::Binding);

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use WPSOAP::MQ::Sender::LC_CUSTOMER_PROJECT;

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

our $DEFAULT_SYSTEM_CLASS = 'WPSOAP::System::CUSTOMER_PROJECT';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art()->_dbh() }

sub get_sender {
	my $self = shift;
	$self->{SENDER} = WPSOAP::MQ::Sender::LC_CUSTOMER_PROJECT->instance(DB => $self->get_db());
	return $self->{SENDER};
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub step(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		my $meta = {
			activityId => $self->id(),
			activityType => $self->info('ACTIVITY_TYPE_NAME'),
			projectCode => $self->activity_property('projectCode'),
		};
		$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
			&& return undef 
				if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
		for my $att (@{$params{ATTACHMENTS}}){
			if (!ref ($att)){
				# lo trasformo in una struttura per gestire i meta
				$att = {
					FILENAME => $att,
					META => $meta
				};
			} elsif (ref($att) eq 'HASH'){
				# aggiungo la chiave META se non esiste
				if (!exists $att->{META}){
					$att->{META} = $meta
				} else {
					# aggiungo le chiavi sovrascrivendole se già presenti
					for my $k (keys %{$meta}){
						$att->{META}->{$k} = $meta->{$k};
					}
				}
				
			} else {
				$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
				return undef; 
			}
		}
	}
	
	return $self->SUPER::step(%params);
}


1;
