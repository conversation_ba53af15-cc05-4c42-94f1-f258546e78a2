package API::ART::APP::Activity::BUILDING_LC;

use strict;
use warnings;
use JSON;

use base qw(API::ART::Activity::Binding);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art()->_dbh() }

sub modifica {
	my $self = shift;
	my %params = @_;
	
	my %props = %params;
	if(defined $props{placeName} || defined $props{streetName} || defined $props{streetNumber}) {
		$props{completeAddress} = '';
		$props{completeAddress} .= $props{placeName}." " if defined $props{placeName};
		$props{completeAddress} .= $props{streetName}.", " if defined $props{streetName};
		$props{completeAddress} .= $props{streetNumber} if defined $props{streetNumber};
	}

	return $self->step(
		 ACTION => 'MODIFICA'
		,PROPERTIES => \%props
		,SAVE_ALL_PROPERTIES => 0
	);
}

sub sospendi {
	my $self = shift;
	
	return $self->step(
		 ACTION => 'SOSPENSIONE'
	);
}

sub desospendi {
	my $self = shift;
	my %params = @_;
	
	my %props = %params;
	if(defined $props{placeName} || defined $props{streetName} || defined $props{streetNumber}) {
		$props{completeAddress} = '';
		$props{completeAddress} .= $props{placeName}." " if defined $props{placeName};
		$props{completeAddress} .= $props{streetName}.", " if defined $props{streetName};
		$props{completeAddress} .= $props{streetNumber} if defined $props{streetNumber};
	}

	return $self->step(
		 ACTION => 'DESOSPENSIONE'
		,PROPERTIES => \%props
		,SAVE_ALL_PROPERTIES => 0
	);
}

sub update_permits_count{
	my $self = shift;
	
	# recupero i figli
	my $children = $self->get_children(ACTIVITY_TYPE_NAME => ['PERMESSO_BUILDING']);
	return undef unless $children;
	
	my $update = {
		ongoingPermits		=> 0,
		acceptedPermits		=> 0,
		deniedPermits		=> 0,
		cancelledPermits	=> 0
	};
	
	for my $ch (@{$children}){
		if ($ch->get_current_status_name eq 'OTTENUTA'){
			$update->{acceptedPermits}++;
		} elsif ($ch->get_current_status_name eq 'RESPINTA'){
			$update->{deniedPermits}++;
		} elsif (!$ch->is_active()){
			# tutti gli altri stati finali defono essere conteggiati come cancelledPermits
			$update->{cancelledPermits}++;
		} else { # $ch->is_active()
			# tutti gli stati non finali eccetto OTTENUTA e RESPINTA
			$update->{ongoingPermits}++;
		}
	}
	
	return $self->system()->set_property( PROPERTIES => $update );
}

1;
