package API::ART::APP::Activity::LC01;

use strict;
use warnings;
use JSON;
use WPSOCORE;
use WPSOCORE::Collection::System::CUSTOMER;
use WPSOCORE::Collection::System::CONTRACT;
use WPSOCORE::Collection::Activity::NETWORK;
use WPSOCORE::MQ::Sender::LC01;
use WPSOCORE::MQ::Sender::RepositoryNotice;
use WPSOCORE::Teams;

use base qw(API::ART::Activity::Binding);

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

use Encode;
use Text::CSV_XS;
use Excel::Writer::XLSX;
use POSIX qw(strftime);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

our $DEFAULT_SYSTEM_CLASS = 'WPSOCORE::System::EL01';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art->_dbh }

sub wpsocore {
	my $self = shift;
	
	$self->{WPSOCORE} = WPSOCORE->new(ART => $self->art()) unless exists $self->{WPSOCORE};
	
	return $self->{WPSOCORE};
}

sub _get_customer_collection {
	my $self = shift;

	return $self->{COLL_CUSTOMER} if defined $self->{COLL_CUSTOMER};

	$self->{COLL_CUSTOMER} = WPSOCORE::Collection::System::CUSTOMER->new(ART => $self->art) unless exists $self->{COLL_CUSTOMER};
	return $self->{COLL_CUSTOMER};
}

sub _get_contract_collection {
	my $self = shift;

	return $self->{COLL_CONTRACT} if defined $self->{COLL_CONTRACT};

	$self->{COLL_CONTRACT} = WPSOCORE::Collection::System::CONTRACT->new(ART => $self->art, CUSTOMER => $self->get_customer()) unless exists $self->{COLL_CONTRACT};
	return $self->{COLL_CONTRACT};
}

sub _get_network_collection {
      my $self = shift;
      $self->{COLL_NETWORK} = WPSOCORE::Collection::Activity::NETWORK->new(ART => $self->art) unless exists $self->{COLL_NETWORK};
      return $self->{COLL_NETWORK};
}

sub _get_sender_repository {
	my $self = shift;
	$self->{SENDER_REPOS} = WPSOCORE::MQ::Sender::RepositoryNotice->instance(DB => $self->get_db) unless exists $self->{SENDER_REPOS};
	return $self->{SENDER_REPOS};
}

sub _get_sender {
	my $self = shift;
	$self->{SENDER} = WPSOCORE::MQ::Sender::LC01->instance(DB => $self->get_db) unless exists $self->{SENDER};
	return $self->{SENDER};
}

sub get_customer {
	my $self = shift;

	unless (defined $self->{CUSTOMER}){
		my $customers = $self->_get_customer_collection()->cerca(
			customerId => $self->system_property('customerId')
		);

		return undef unless defined $customers;

		if (scalar @{$customers} > 1){
			$self->art()->last_error(__x("Find more than one customer for {customerId}", customerId => $self->system_property('customerId')));
			return undef;
		} elsif (scalar @{$customers} == 0){
			$self->art()->last_error(__x("Unable to find customer for {customerId}", customerId => $self->system_property('customerId')));
			return undef;
		} else {
			$self->{CUSTOMER} = $customers->[0];
		}
	}

	return $self->{CUSTOMER};
}

sub get_contract {
	my $self = shift;

	unless (defined $self->{CONTRACT}){
		my $contracts = $self->_get_contract_collection()->cerca(
			contractId => $self->system_property('contractId')
		);

		return undef unless defined $contracts;

		if (scalar @{$contracts} > 1){
			$self->art()->last_error(__x("Find more than one contract for {contractId}", contractId => $self->system_property('contractId')));
			return undef;
		} elsif (scalar @{$contracts} == 0){
			$self->art()->last_error(__x("Unable to find contract for {contractId}", contractId => $self->system_property('contractId')));
			return undef;
		} else {
			$self->{CONTRACT} = $contracts->[0];
		}
	}

	return $self->{CONTRACT};
}

sub get_teams {
    my $self = shift;

    $self->{TEAMS} = WPSOCORE::Teams->new(ART => $self->art()) unless exists $self->{TEAMS};

    return $self->{TEAMS};
}

sub get_networkWorkOrderList {
	my ( $self ) = @_;

	my $contract = $self->get_contract();
	return undef unless defined $contract;

	my $companyAbbreviation = $contract->property('companyAbbreviation');

    # Non in cache per seguire gli aggiornamenti
	my $sql = q{
		with es as (
			select oda.wbe, oda.oda, max(oda.data_Evento) DATA_EVENTO
			from core.mv_oda_Wbe oda
				join CORE.mv_wbe_tipo_avviso_macroatt m on m.wbe_3_livello = oda.wbe and m.soc = oda."companyAbbreviation"
			where 1=1
			and oda.wbe = ?
			and m.tipo_Avviso = ?
			and oda."companyAbbreviation" = ?
			group by  oda.wbe, oda.oda
		)
		select oda2.oda||' - '||oda2.descrizione_oda
		from es
			join core.mv_oda_Wbe oda2 on
				oda2.wbe = es.wbe and
				oda2.oda = es.oda and
				oda2.data_Evento = es.data_Evento
				and oda2."companyAbbreviation" = ?
		order by 1
	};
	my $prepare = $self->art->_create_prepare(__PACKAGE__.'_WORKORDERLIST', $sql) or return;
	my $result = $prepare->fetchall_arrayref(
		$self->activity_property('networkWBELevel3'),
		$self->system_property('externalWorkTypeId'),
		$companyAbbreviation,
		$companyAbbreviation,
	);
	my $networkWorkOrderList = [ map { $_->[0] } @{$result} ];
	return $networkWorkOrderList;
}

sub has_network { return defined shift->property('networkId'); }

sub has_primary_network { return defined shift->property('primaryNetworkId'); }

sub get_network_activity {
    my $self = shift;
	if ( !defined $self->{NETWORK_ACTIVITY} ) {
        my $coll_network = $self->_get_network_collection or return;
        my $networks = $self->_get_network_collection->cerca(
            customerId => $self->system_property('customerId'),
            contractId => $self->system_property('contractId'),
            networkId  => $self->property('networkId'),
        );
		if ( @{$networks} == 1 ) {
			$self->{NETWORK_ACTIVITY} = $networks->[0];
		} else {
			$self->art->last_error(__('Network not found'));
			return;
		}
	}
	return $self->{NETWORK_ACTIVITY};
}

sub get_primary_network_activity {
    my $self = shift;
	if ( !defined $self->{PRIMARY_NETWORK_ACTIVITY} ) {
        my $coll_network = $self->_get_network_collection or return;
        my $networks = $self->_get_network_collection->cerca(
            customerId => $self->system_property('customerId'),
            contractId => $self->system_property('contractId'),
            networkId  => $self->property('primaryNetworkId'),
        );
		if ( @{$networks} == 1 ) {
			$self->{PRIMARY_NETWORK_ACTIVITY} = $networks->[0];
		} else {
			$self->art->last_error(__('Primary network not found'));
			return;
		}
	}
	return $self->{PRIMARY_NETWORK_ACTIVITY};
}

sub sender_send_visibility_notice {
	my ( $self, %params ) = @_;
	return $self->_get_sender_repository->send_visibility_notice(%params);
}

sub remap_works_system_property{
	my $self = shift;
	return {
		'civil'		=> 'wCivil',
		'laying'	=> 'wLaying',
		'junction'	=> 'wJunction',
		'restoration'	=> 'wRestoration',
	};
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub step(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return undef unless $self->SUPER::step(%params);
		
	return undef unless $self->refresh_ptes();

	return 1;

}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub add_documentation(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return undef unless $self->SUPER::add_documentation(%params);

	return undef unless $self->refresh_ptes();

	return 1;
}

# effettuiamo ovverride metodo delete_documentation per gestire il refresh dei ptes
sub delete_attachment(){
	my $self = shift;
	my %params = @_;
	
	return undef unless $self->SUPER::delete_attachment(%params);

	return undef unless $self->refresh_ptes();

	return 1;
}

sub _manage_attachments {
	my $self = shift;
	my %params = @_;

	my $meta = {
		activityId => $self->id(),
		activityType => $self->info('ACTIVITY_TYPE_NAME'),
		cabinetId => $self->system_id(),
	};
	$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
		&& return undef 
			if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
	for my $att (@{$params{ATTACHMENTS}}){
		if (!ref ($att)){
			# lo trasformo in una struttura per gestire i meta
			$att = {
				FILENAME => $att,
				META => $meta
			};
		} elsif (ref($att) eq 'HASH'){
			# aggiungo la chiave META se non esiste
			if (!exists $att->{META}){
				$att->{META} = $meta
			} else {
				# aggiungo le chiavi sovrascrivendole se già presenti
				for my $k (keys %{$meta}){
					$att->{META}->{$k} = $meta->{$k};
				}
			}
			
		} else {
			$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
			return undef; 
		}
	}

	return 1;

}

sub refresh_ptes {
	my $self = shift;
	my %params = @_;

	my $ptes = $self->get_children(ACTIVITY_TYPE_NAME => ['LC02'], ACTIVE => 1);
	return undef unless defined $ptes;

	for my $pte (@$ptes){
		return undef unless $pte->refresh_pte();
	}

	return 1;

}

sub pteJobReport {
	my $self = shift;
	my %params = @_;

	my $errmsg;
	$self->art->last_error($errmsg)
		and return undef
			unless $self->art->check_named_params(
					ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					FILE_TMP    => { isa => undef, inherits => [ 'GLOB' ] },
					EXPORT_TYPE => { isa => 'SCALAR', list => [ 'csv', 'xlsx' ] },
				}
				,OPTIONAL   => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);

	my $file_tmp  = $params{FILE_TMP};

	my ( $csv, $wbk, $wsh );

	if ( $params{EXPORT_TYPE} eq 'csv' ) {
		$csv = Text::CSV_XS->new({binary => 1, eol => $/, sep_char => ';', quote_char => '"'});
	} else {
		$wbk = Excel::Writer::XLSX->new($file_tmp);
		$wbk->set_tempdir($ENV{ART_REPOSITORY_TMP_TMPFILES});
		$wsh = $wbk->add_worksheet;
		# $wsh->set_margins(0);
		# $wsh->set_header('',1);
		# $wsh->set_footer('',1);
		$wsh->keep_leading_zeros;
	}

	my $header = [
		'In proposta di Ses',
		'Ordine' ,
		'CdL' ,
		'Tratta',
		'Descr. Tratta',
		'Attività',
		'Cd Materiale Cliente',
		'Variante di Macroattività',
		'Descrizione',
		'Qtà Voce',
		'Qtà Prestazione/Materiale',
		'Qtà Reimpiegata',
		'Area',
		'Discount',
		'Surcharge',
		'CV_CL',
		'Magazzino'
	];
	# necessario per avere l'enconding corretto in uft-8
	$header = [map {decode_utf8($_)} @{$header}];

	my $r = 0;
	if ( $params{EXPORT_TYPE} eq 'csv' ) {
		# necessario per avere l'enconding corretto in uft-8
		binmode( $file_tmp, ":utf8" );
		$csv->print($file_tmp, $header);
	} else {
		$wsh->write_row( $r++, 0, $header );
	}

	# Workaround per query rendicontazione, che viene rigenerata ogni giorno per evitare un'errata ottimizzazione Oracle
    my @l = localtime;
    my $today = strftime('%Y%m%d',@l);
    $l[3]--;
    my $yesterday = strftime('%Y%m%d',@l);
    my $name = __PACKAGE__ . '_ESTRAZ_' . $today;
    my $oldname =  __PACKAGE__ . '_ESTRAZ_' . $yesterday;
	$self->art->_destroy_prepare($oldname);
    my $sql_estraz = sprintf q{
        select *
        from core.v_misurato_fibercop
        where  id_attivita_lc01 = ?
        and    to_char(sysdate,'YYYYMMDD') = %s
        order by ods, tipo_lavoro, variante
    }, $self->art->_dbh->quote($today);
    #delete $self->art->{'_PREPARE'}->{$oldname} if exists $self->art->{'_PREPARE'}->{$oldname};

	my $prepare_estraz = $self->art->_create_prepare(__PACKAGE__ . '_ESTRAZ',$sql_estraz);

	my $results = $prepare_estraz->fetchall_hashref($self->id());
	my $var = {};

	# preprocesso i risultati per spalmare le quantità
	for my $res (@{$results}){
		# necessario in quanto utilizzando gli env di LANG impostati con l'italiano i numeri decimali escono con la virgola e non sono poi gestibili correttamente da perl
		$res->{QUANTITA_RIFERIMENTO} =~ s/,/./g;
		if (
			(!$var->{ODS} || $var->{ODS} ne $res->{ODS}) #cambia ods
			||
			($var->{TIPO_LAVORO} ne $res->{TIPO_LAVORO}) # stesso ODS ma cambia TIPO_LAVORO
			||
			($var->{VARIANTE} ne $res->{VARIANTE}) # stesso ODS e TIPO_LAVORO ma cambia variante
		){
			$var = {}; #resetto
			$var->{VARIANTE} = $res->{VARIANTE};
			$var->{TIPO_LAVORO} = $res->{TIPO_LAVORO};
			$var->{TOTALE} = scalar grep {$_->{ODS} eq $res->{ODS} && $_->{TIPO_LAVORO} eq $res->{TIPO_LAVORO} && $_->{VARIANTE} eq $res->{VARIANTE}} @{$results};
			$var->{POSIZIONE} = 1;
			$var->{ODS} = $res->{ODS};
			# incapsuloato per non avere problemi con le divisioni con i float
			# altrimenti viene arrontodanto (esempio: 0.49999999 al posto di 0.5)
			{
				use bignum;
				my $q = $res->{QUANTITA_RIFERIMENTO}/$var->{TOTALE};
				$q =~ s/,/./g;
				$q =~s/\.(\d{2}).*/.$1/;
				$var->{PRIMI_ELEMENTI} = $q;
				$var->{ULTIMO_ELEMENTO} = $res->{QUANTITA_RIFERIMENTO} - ($var->{PRIMI_ELEMENTI}*($var->{TOTALE}-1));
				for ('PRIMI_ELEMENTI','ULTIMO_ELEMENTO'){
					$var->{$_} =~ s/,/./g;
				}
			}
		} else {
			$var->{POSIZIONE}++;
		}

		if ($var->{POSIZIONE} ne $var->{TOTALE}){
			$res->{QUANTITA} = sprintf("%.2f",$var->{PRIMI_ELEMENTI});
		} else {
			$res->{QUANTITA} = sprintf("%.2f",$var->{ULTIMO_ELEMENTO});
		}
		$res->{QUANTITA} =~ s/,/./g;

		my $line = [
			(defined $res->{ATTIVITA} ? 'X' : undef), # In proposta di Ses
			$res->{ODS},
			$res->{ESECUTORE}, # CdL
			$res->{TRATTA},     # Tratta
			$res->{DESCRIZIONE_TRATTA},     # Descrizione Tratta
			$res->{ATTIVITA},     # Attività
			'',     # Cd Materiale Cliente
			$res->{VARIANTE}, # Variante di macroattività
			$res->{VARIANTE_DESCRIZIONE}, # DESCRIZIONE
			$res->{QUANTITA},
			(defined $res->{ATTIVITA} ? 1 : undef),     # Quantità prestazione/materiale
			'',     # Quantità reimpiegata
			'',     # AREA (sempre null)
			'',     # SCONTO
			'',     # Surcharge
			'',     # CV_CL
			'',     # Magazzino
		];
		# necessario per avere l'enconding corretto in uft-8
		$line = [map {decode_utf8($_)} @{$line}];
		if ( $params{EXPORT_TYPE} eq 'csv' ) {
			$csv->print($file_tmp, $line);
		} else {
			$wsh->write_row( $r++, 0, $line );
		}
	}

	if ( $params{EXPORT_TYPE} eq 'xlsx' ) {
		$wbk->close;
	}

	my $sql_insert = q{
		insert into core.MISURATO_FIBERCOP_LOG (ID_ATTIVITA_LC01, LOGIN_OPERATORE) values (?,?)
	};

	my $prepare_insert = $self->art->_create_prepare(__PACKAGE__ . '_INSERREP',$sql_insert);

	unless ($prepare_insert->do($self->id(), $self->art()->user()->name())){
		$self->art()->last_error($self->get_db()->get_errormessage());
		return undef;
	}

	return 1
}

sub _api_clone {
	my ( $self ) = @_;
	use API::ART;
	return $self->art->new(
		ARTID          => $ENV{ARTID},
		USER_REFERENCE => $self->art->user,
	);
}

sub _activity_clone {
	my ( $self, $api_clone ) = @_;
	return $self->new( ART => $api_clone, ID => $self->id );
}

sub update_networkWorkOrderList {
	my ( $self ) = @_;
	my $old_values = $self->system->property('networkWorkOrderList');
	my $new_values = $self->get_networkWorkOrderList;

	my %count;
	foreach my $e ( @{$old_values}, @{$new_values}) { $count{$e}++ };
	my @diff = grep { $count{$_} == 1 } keys %count;

	if (@diff) {
		my $ap = $self->_api_clone or return;
		my $ac = $self->_activity_clone($ap) or do { warn $ap->last_error; return; };
		$ac->system->set_property( PROPERTIES => { networkWorkOrderList => $new_values } ) or do { warn $ap->last_error; return; };
		warn sprintf 'Property networkWorkOrderList set to [ %s ] for activity %d', join(', ',@{$ac->system->property('networkWorkOrderList')}), $ac->id;
		$ap->save;
	}

	return 1;
}

sub refresh_cabinet_by_tt {
	my $self = shift;
	my %params = @_;
	my $errmsg;
	my $art = $self->art();
	$self->art->last_error($errmsg)
		and return undef
			unless $self->art->check_named_params(
					ERRMSG     => \$errmsg
				,PARAMS     => \%params
				,MANDATORY  => {
					ACTION				=> { isa => 'SCALAR', list => ['RESTORE', 'SUSP'] },
					networkId 			=> { isa => 'SCALAR' },
					ttId				=> { isa => 'SCALAR' },
					ttCustomerSystem	=> { isa => 'SCALAR' }
				}
				,OPTIONAL   => {
				}
				,IGNORE_EXTRA_PARAMS => 0
	);
	# recuperare tutti i PTE dell'armadio agganciati alla networkId passata
	my $ptes = $self->get_children(ACTIVITY_TYPE_NAME => ['LC02']);
	return undef unless defined $ptes;
	# per ogni PTE verifica se c'è un lavoro in corso di tipo updateDatabaseF1 o updateDatabaseF2
	for my $pte (@$ptes){
		# i PTE già chiusi li ignoro
		next if $pte->is_closed();
		# gestisco solo i PTE relativi alla specifica network
		next if $pte->activity_property('paymentNetwork') ne $params{networkId};
		my @update_type;
		my $system_props = $pte->system_property('wUpdateDatabaseF1Status', 'wUpdateDatabaseF1LAS', 'wUpdateDatabaseF2Status', 'wUpdateDatabaseF2LAS', 'customerId', 'contractId');
		if (
			defined $system_props->{wUpdateDatabaseF1Status}
			&& (
				$system_props->{wUpdateDatabaseF1Status} eq 'ONGOING'
				||
				(
					defined $system_props->{wUpdateDatabaseF1LAS}
					&&
					$system_props->{wUpdateDatabaseF1LAS} ne 'BLOCCO_SISTEMA_CLIENTE'
					&&
					$system_props->{wUpdateDatabaseF1Status} eq 'SUSP'
				)
				|| 
				(
					defined $system_props->{wUpdateDatabaseF1LAS}
					&&
					$params{ACTION} eq 'RESTORE'
					&&
					$system_props->{wUpdateDatabaseF1LAS} eq 'BLOCCO_SISTEMA_CLIENTE'
					&&
					$system_props->{wUpdateDatabaseF1Status} eq 'SUSP'
				)
			)
		) {
			push @update_type,{type =>'updateDatabaseF1',status => $system_props->{wUpdateDatabaseF1Status}};
		}
		if (
			defined $system_props->{wUpdateDatabaseF2Status}
			&& (
				$system_props->{wUpdateDatabaseF2Status} eq 'ONGOING'
				|| 
				(
					defined $system_props->{wUpdateDatabaseF2LAS}
					&&
					$params{ACTION} eq 'SUSP'
					&&
					$system_props->{wUpdateDatabaseF2LAS} ne 'BLOCCO_SISTEMA_CLIENTE'
					&&
					$system_props->{wUpdateDatabaseF2Status} eq 'SUSP'
				)
				|| 
				(
					defined $system_props->{wUpdateDatabaseF2LAS}
					&&
					$params{ACTION} eq 'RESTORE'
					&&
					$system_props->{wUpdateDatabaseF2LAS} eq 'BLOCCO_SISTEMA_CLIENTE'
					&&
					$system_props->{wUpdateDatabaseF2Status} eq 'SUSP'
				)
			)
		) {
			push @update_type,{type =>'updateDatabaseF2',status => $system_props->{wUpdateDatabaseF2Status}};
		}

		# Se c'è chiama il booking,da controllare se sono giusti i parametri
		for my $up (@update_type) {
			return undef unless $pte->booking(
				bookingType 	=> 'STEP',
				bookingStatus	=>	$up->{status},
				action			=>	$params{ACTION} eq 'SUSP' ? 'SOSPENSIONE_CAUSA_TT' : 'CHIUSURA_TT',
				ref00			=>	$pte->system()->parent()->id(),
				ref01			=>	$pte->system()->id(),
				customerId 		=>	$system_props->{customerId},
				contractId 		=>	$system_props->{contractId},
				details       => [
					{
						type => $up->{type}
					}
				],
				operatorLogin => $art->user()->name(),
				FORCE => 1,
				EXTRA => {
					ttId				=> $params{ttId},
					ttCustomerSystem	=> $params{ttCustomerSystem}
				}
			);
		}
    }
	return 1;
}
1;
