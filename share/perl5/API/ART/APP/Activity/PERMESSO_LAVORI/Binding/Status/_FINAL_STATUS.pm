package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Status::_FINAL_STATUS;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;

	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
	
		return 1 if $activity->is_of;
		
		my $sender = $activity->get_sender();
		return undef unless defined $sender;
		
		my $data = $activity->get_data_for_notify_permit();

		if ($activity->get_current_status_name() eq 'CHIUSA'){
			$self->art()->last_error()
				&& return undef
					unless defined $sender->notify_permit_close_ok(%{$data});
		} else {
			$self->art()->last_error()
				&& return undef
					unless defined $sender->notify_permit_close_ko(%{$data});
		}
	}

	return 1;
};

1;
