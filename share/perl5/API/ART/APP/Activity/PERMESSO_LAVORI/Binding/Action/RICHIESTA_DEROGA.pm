package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Action::RICHIESTA_DEROGA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	# effettuo check date
	if ($art->get_date_from_iso_date($params->{PROPERTIES}->{"expectedAuthorizationDate"}) > $art->get_date_from_iso_date($params->{PROPERTIES}->{"expectedEndAuthorizationDate"})){
		$art->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => __('expectedEndAuthorizationDate'), date2 => __('expectedAuthorizationDate')));
		return undef;
	}
	
	return 1;
}

sub post {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};
	
	return 1 unless $activity->is_of;
	
	#aggiorno SiNFO relativamente ai permessi
	return undef unless defined $activity->update_stato_area_permessi();
	
	return $activity->parent()->update_permits_count();
}

1;
