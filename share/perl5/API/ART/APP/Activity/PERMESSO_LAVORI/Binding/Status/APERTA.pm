package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Status::APERTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
		if ($activity->is_of){
			#aggiorno SiNFO relativamente ai permessi
			return undef unless $activity->update_stato_area_permessi();
		} else {
			my $sender = $activity->get_sender();
			return undef unless defined $sender;
			
			my $data = $activity->get_data_for_notify_permit();
			
			$self->art()->last_error()
				&& return undef
					unless defined $sender->notify_permit_open(%{$data});
		}
		
		return $activity->step(ACTION => 'NUOVO_PERMESSO');
	}

	return 1;
};

1;
