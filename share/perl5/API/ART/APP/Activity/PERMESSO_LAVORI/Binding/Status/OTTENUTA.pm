package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Status::OTTENUTA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Status::_CHECK_BINDING);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub on_enter {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::on_enter($activity);
	return undef unless defined $s;

	# se ritorna 1 significa che devo fare il binding altrimenti no
	if ($s){
		return 1 unless $activity->is_of;
		
		#aggiorno SiNFO relativamente ai permessi
		return undef unless defined $activity->update_stato_area_permessi();
		
		return $activity->parent()->update_permits_count();
	}

	return 1;
	
};

1;
