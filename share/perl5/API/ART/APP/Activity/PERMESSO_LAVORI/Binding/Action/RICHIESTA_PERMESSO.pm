package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Action::RICHIESTA_PERMESSO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	my $activity_property = $activity->activity_property;

	# infarcisco, se possibile, eventuali valori non presenti perchè potrebbero essere già stati passati in apertura
	for ('expectedAuthorizationDate','expectedEndAuthorizationDate', 'requestDate'){
		next if defined $params->{PROPERTIES}->{$_};
		$params->{PROPERTIES}->{$_} = $activity_property->{$_};
	}

	# effettuo check date
	if ($art->get_date_from_iso_date($params->{PROPERTIES}->{"expectedAuthorizationDate"}) > $art->get_date_from_iso_date($params->{PROPERTIES}->{"expectedEndAuthorizationDate"})){
		$art->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => __('expectedEndAuthorizationDate'), date2 => __('expectedAuthorizationDate')));
		return undef;
	}

	if (! defined $params->{PROPERTIES}->{requestDate}){
		$params->{PROPERTIES}->{requestDate} = $art->get_iso_date_from_date($art->_dbh()->get_sysdate());
	}	
	
	return 1;
}

1;
