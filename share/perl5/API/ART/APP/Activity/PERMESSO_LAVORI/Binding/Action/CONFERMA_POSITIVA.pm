package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Action::CONFERMA_POSITIVA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Action::_CHECK_OC);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub can_do {
	my ( $self, $activity) = @_;
	
	my $s = $self->SUPER::can_do($activity, 'CONFERMA_POSITIVA');
	return undef unless defined $s;

	return 1;
}

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	# verifico che l'account sia effettivamente un number
	if (defined $params->{PROPERTIES}->{amount} && $params->{PROPERTIES}->{amount}!~/^\d+$/){
		my $msg = __x('Param {name} must be a {type}', name => __('amount'), type => __('integer'));
		$art->last_error($msg);
		return undef;	
	}
	
	# effettuo check date
	if ($art->get_date_from_iso_date($params->{PROPERTIES}->{"authorizationDate"}) > $art->get_date_from_iso_date($params->{PROPERTIES}->{"endAuthorizationDate"})){
		$art->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => __('endAuthorizationDate'), date2 => __('authorizationDate')));
		return undef;
	}
	
	return 1;
}

1;
