package API::ART::APP::Activity::LC06::Binding::Action::ANNULLAMENTO;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub can_do {
	my ( $self, $activity) = @_;
	
	for("sitePrimaryNetwork",
		"siteConstructionNetwork", 
		"siteRestorationNetwork",
		"siteOtherNetwork"
	){
		if ( defined $activity->system_property($_)){
			$activity->art()->last_error(__x("Unable to cancel the site because it is connected to the network {p}",p=>$activity->system_property($_)));
			return undef;
		}
	}
	
	return 1;
}

1;
