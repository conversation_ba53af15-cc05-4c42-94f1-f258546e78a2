package API::ART::APP::Activity::LC06::Binding::Action::SOCIETARIZZAZIONE_CLIENTE;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	for ('siteConstructionNetwork','siteOtherNetwork','sitePrimaryNetwork','siteRestorationNetwork'){
		if (defined $params->{PROPERTIES}->{$_}){
			$params->{PROPERTIES}->{$_.'Old'} = $activity->activity_property($_);
		}
	}

	return 1;
}

1;
