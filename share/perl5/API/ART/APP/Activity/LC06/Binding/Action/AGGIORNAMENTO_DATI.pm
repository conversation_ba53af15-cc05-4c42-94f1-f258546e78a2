package API::ART::APP::Activity::LC06::Binding::Action::AGGIORNAMENTO_DATI;

use strict;
use warnings;
use Carp qw(verbose croak);

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub pre {
	my ( $self, $activity, $params ) = @_;
	
	my $art = $self->{ART};	
	my $errmsg;

	my $new_system_property = {};
    my $keys = $art->enum_system_property(SYSTEM_TYPE_NAME => 'EL06');
    for my $k (keys %{$keys}){
        $new_system_property->{$k} = $params->{PROPERTIES}->{$k} if exists $params->{PROPERTIES}->{$k};
    }
    return undef
        unless $activity->system->set_property(PROPERTIES => $new_system_property);

	return 1;
}

1;
