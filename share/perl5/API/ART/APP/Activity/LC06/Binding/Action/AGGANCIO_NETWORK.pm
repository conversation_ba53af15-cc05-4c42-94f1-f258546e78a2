package API::ART::APP::Activity::LC06::Binding::Action::AGGANCIO_NETWORK;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;

	my $art = $self->{ART};

	if ($params->{PROPERTIES}->{networkSiteType} eq 'Primaria'){
		$params->{PROPERTIES}->{sitePrimaryNetwork} = exists $params->{PROPERTIES}->{oldNetworkId} ? '' : $params->{PROPERTIES}->{networkId};
	} elsif ($params->{PROPERTIES}->{networkSiteType} eq 'Realizzazione sito'){
		$params->{PROPERTIES}->{siteConstructionNetwork} = exists $params->{PROPERTIES}->{oldNetworkId} ? '' : $params->{PROPERTIES}->{networkId};
	} elsif ($params->{PROPERTIES}->{networkSiteType} eq 'Ripristino'){
		$params->{PROPERTIES}->{siteRestorationNetwork} = exists $params->{PROPERTIES}->{oldNetworkId} ? '' : $params->{PROPERTIES}->{networkId};
	} elsif ($params->{PROPERTIES}->{networkSiteType} eq 'Realizzazione sito + Ripristino'){
		$params->{PROPERTIES}->{siteConstructionNetwork} = $params->{PROPERTIES}->{siteRestorationNetwork} = exists $params->{PROPERTIES}->{oldNetworkId} ? '' : $params->{PROPERTIES}->{networkId};
	} elsif ($params->{PROPERTIES}->{networkSiteType} eq 'Altro'){
		$params->{PROPERTIES}->{siteOtherNetwork} = exists $params->{PROPERTIES}->{oldNetworkId} ? '' : $params->{PROPERTIES}->{networkId};
	}

	for ('networkSiteType', 'networkId','oldNetworkId'){
		delete $params->{PROPERTIES}->{$_};
	}

	return 1;
}

1;
