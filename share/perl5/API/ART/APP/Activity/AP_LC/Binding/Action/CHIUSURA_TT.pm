package API::ART::APP::Activity::AP_LC::Binding::Action::CHIUSURA_TT;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub post {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	my $ongoingTT = $activity->system()->property('ongoingTT');
	$ongoingTT--;
	return $activity->system()->set_property(
		PROPERTIES => {
			ongoingTT => $ongoingTT
		}
	);
}

1;
