package API::ART::APP::Activity::AP_LC::Binding::Action::FORECASTING;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	# effettuo check date
	if ($art->get_date_from_iso_date($params->{PROPERTIES}->{"forecastStartDate"}) > $art->get_date_from_iso_date($params->{PROPERTIES}->{"forecastEndDate"})){
		$art->last_error(__x("Date {date1} must be equal or greater than {date2}", date1 => __('forecastEndDate'), date2 => __('forecastStartDate')));
		return undef;
	}
	
	return 1;
}

1;
