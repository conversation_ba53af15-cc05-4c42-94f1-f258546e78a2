package API::ART::APP::Activity::AP_LC::Binding::Action::MODIFICA_AREA;

use strict;
use warnings;
use Carp qw(verbose croak);
use JSON;

use base qw(API::ART::Activity::Binding::Action::Base);

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

our $DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

sub pre {
	my ( $self, $activity, $params) = @_;
	
	my $art = $self->{ART};

	# se presente verifico che la polygon sia un JSON
	if (defined $params->{PROPERTIES}->{polygon}){
		eval {
			$params->{PROPERTIES}->{polygon} = encode_json($params->{PROPERTIES}->{polygon});
		};
		if ($@){
			my $msg = __x('Invalid param {name}: {error}', name => __('polygon'), error => $@ );
			$art->last_error($msg);
			return undef;	
		}
	}

	return 1;
}

1;
