package API::ART::APP::Activity::LC02;

use strict;
use warnings;
use JSON;
use HTTP::Status qw(:constants);

use base qw(API::ART::Activity::Binding);
use WPSOCORE;
use WPSOCORE::Subcontracts;
use WPSOCORE::Cluster;
use WPSOCORE::WorkPerformance;
use WPSOCORE::MQ::Sender::LC02;
use WPSOCORE::FibercopNotifiche;
use WPSOCORE::Collection::Activity::EXTERNAL_SYNC;
use WPSOCORE::Collection::Activity::LC02;
use WPSOCORE::FieldService;
use WPSOCORE::Collection::System::CUSTOMER;
use WPSOCORE::Teams;

use Data::Dumper;
use Log::Log4perl qw(get_logger :levels :nowarn);

our $DEFAULT_CHILDREN_CLASS = 'API::ART::Activity::Factory';

our $DEFAULT_SYSTEM_CLASS = 'WPSOCORE::System::EL02';

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

sub get_db { shift->art->_dbh }

sub wpsocore {
	my $self = shift;
	
	$self->{WPSOCORE} = WPSOCORE->new(ART => $self->art()) unless exists $self->{WPSOCORE};
	
	return $self->{WPSOCORE};
}

sub get_teams {
	my $self = shift;
	
	$self->{TEAMS} = WPSOCORE::Teams->new(ART => $self->art()) unless exists $self->{TEAMS};
	
	return $self->{TEAMS};
}

sub _get_external_sync_collection {
	my $self = shift;
	$self->{EXT_SYNC} = WPSOCORE::Collection::Activity::EXTERNAL_SYNC->new(ART => $self->art) unless exists $self->{EXT_SYNC};
	return $self->{EXT_SYNC};
}

sub _get_lc02_collection {
	my $self = shift;
	$self->{COLL_LC02} = WPSOCORE::Collection::Activity::LC02->new(ART => $self->art) unless exists $self->{COLL_LC02};
	return $self->{COLL_LC02};
}

sub _get_customer_collection {
	my $self = shift;
	$self->{COLL_CUSTOMER} = WPSOCORE::Collection::System::CUSTOMER->new(ART => $self->art()) unless exists $self->{COLL_CUSTOMER};
	return $self->{COLL_CUSTOMER};
}

sub _get_sender {
	my $self = shift;
	$self->{SENDER} = WPSOCORE::MQ::Sender::LC02->instance(DB => $self->get_db) unless exists $self->{SENDER};
	return $self->{SENDER};
}

sub _check_send_to_external_sync {
	my $self = shift;
	return (!$self->has_external_sync || $self->system_property('externalSyncSuspended')) ? 0 : 1;
}

sub _cluster {
	my $self = shift;
	$self->{CLUSTER} = WPSOCORE::Cluster->new(
		ART => $self->art,
		customerId => $self->system_property('customerId'),
		contractId => $self->system_property('contractId'),
	) unless exists $self->{CLUSTER};
	return $self->{CLUSTER};
	
}

sub _workPerformance {
	my $self = shift;
	$self->{WORK_PERFORMANCE} = WPSOCORE::WorkPerformance->new(
		ART => $self->art,
		customerId => $self->system_property('customerId'),
		contractId => $self->system_property('contractId'),
	) unless exists $self->{WORK_PERFORMANCE};
	return $self->{WORK_PERFORMANCE};
	
}

sub get_notifiche {
	my $self = shift;
	$self->{NOTIFICHE} = WPSOCORE::FibercopNotifiche->new(ART => $self->art()) unless exists $self->{NOTIFICHE};
	return $self->{NOTIFICHE};
}

sub has_external_sync {
	my $self = shift;
	my $external_sync = $self->get_children(ACTIVITY_TYPE_NAME => ['EXTERNAL_SYNC']);
	return unless defined $external_sync;
	return scalar @{$external_sync};
}

sub sender_send_external_sync_notice {
	my $self = shift;
	my %params = @_;
	# prima verifico l'azione
	my $history = $self->history();
	return unless $history;
	# recupero l'ultimo step che ha effettuato questa azione
	my $transition = $history->last;
	return unless defined $transition;
	my $action_name = $transition->action_name();
	if ($action_name =~/^(DATI_COLLAUDO_CLIENTE|DATI_COLLAUDO_CLIENTE_RIMOZIONE)$/){ #in questo caso non invia nulla perche' non serve
		return 1;
	}

	# verifico che esista l'at che viene passato
	my $teamAssistant = $self->activity_property('teamAssistant');
	if ($teamAssistant){
		if ($action_name !~/^(APERTURA_LAVORO|AGGIORNAMENTO_LAVORO|CHIUSURA_LAVORO_KO|CHIUSURA_LAVORO_OK|APERTURA_PERMESSO_BUILDING|AGGIORNAMENTO_PERMESSO_BUILDING|CHIUSURA_PERMESSO_BUILDING_OK|CHIUSURA_PERMESSO_BUILDING_KO|FINE_ATTIVITA)$/){
			$self->art->last_error(__x("Technical assistant not available: please first execute action {action}"
				, action => 'AGGIORNA_AT'
			))
				&& return undef
					unless defined $teamAssistant;
			
			my $contract = $self->parent()->get_contract();
			return undef unless defined $contract;
		
			my $companyAbbreviation = $contract->property('companyAbbreviation');
			my $ats = $self->get_teams()->cerca(
				companyCard			=> $teamAssistant,
				companyAbbreviation	=> $companyAbbreviation
			);
			return undef unless defined $ats;
		
			$self->art->last_error(__x("Technical assistant {teamAssistantName} not more available: please first execute action {action}"
				, teamAssistantName => $self->activity_property('teamAssistantName')
				, action => 'AGGIORNA_AT'
			))
				&& return undef
					unless scalar @{$ats};
		}
	}

	return 1 unless $self->_check_send_to_external_sync;
	return $self->_get_sender->send_external_sync_notice(%params);
}

sub sender_send_external_sync_close {
	my $self = shift;
	my %params = @_;
	return 1 unless $self->has_external_sync;
	return $self->_get_sender->send_external_sync_close(%params);
}

sub sender_send_external_sync_phase {
	my $self = shift;
	my %params = @_;
	return 1 unless $self->_check_send_to_external_sync;
	return $self->_get_sender->send_external_sync_phase(%params);
}

sub add_external_sync {
	my $self = shift;
	my %params = @_;
	$params{ACTIVITY} = $self;
	return $self->_get_external_sync_collection->crea(%params);
}

sub get_contract {
	my $self = shift;

	return $self->{CONTRACT} if exists $self->{CONTRACT};

	my $customerId = $self->system_property('customerId');
	my $contractId = $self->system_property('contractId');

	# aggiungo i dati desunti dalla WBE di terzo livello
	my $customers = $self->_get_customer_collection()->cerca(
		customerId => $customerId
	);

	return undef unless (defined $customers);

	my $customer;
	if (scalar @{$customers} > 1){
		$self->art()->last_error(__x("Find more than one customer for {customerId}", customerId => $customerId));
		return undef;
	} elsif (scalar @{$customers} == 0){
		$self->art()->last_error(__x("Unable to find customer for {customerId}", customerId => $customerId));
		return undef;
	} else {
		$customer = $customers->[0];
	}

	my $collContract = eval {WPSOCORE::Collection::System::CONTRACT->new(ART => $self->art, CUSTOMER => $customer)};
	if ($@){
		$self->art->last_error($@);
		return;
	}

	my $contracts = $collContract->cerca(
		contractId => $contractId
	);

	return undef
		unless (defined $contracts);

	my $contract;
	if (scalar @{$contracts} > 1){
		$self->art->last_error(__x("Find more than one contract for {contractId}", contractId => $contractId));
		return undef;
	} elsif (scalar @{$contracts} == 0){
		$self->art->last_error(__x("Unable to find contract for {contractId}", contractId => $contractId));
		return undef;
	} else {
		$contract = $contracts->[0];
	}
	$self->{CONTRACT} = $contract;

	return $contract;
}

sub manage_works_notify {
	my $self = shift;
	my %params = @_;
	
	my $art = $self->art();
	
	my $errmsg;
	
	$art->last_error($errmsg)
		&& return undef
			unless $art->check_named_params(
				 ERRMSG		 => \$errmsg
				,PARAMS		 => \%params
				,MANDATORY	  => {
					__TYPE__	=> { isa => 'SCALAR', list => ['PTE_NOTIFY_OPEN', 'PTE_NOTIFY_CLOSE_OK', 'PTE_NOTIFY_CLOSE_KO', 'PTE_NOTIFY_UPDATE' ] },
					ACTIVITY_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} },
				}
				,IGNORE_EXTRA_PARAMS	=> 1
			);
	
	my $action =
		$params{__TYPE__} eq 'PTE_NOTIFY_OPEN'     ? 'APERTURA_LAVORO' :
		$params{__TYPE__} eq 'PTE_NOTIFY_UPDATE'   ? 'AGGIORNAMENTO_LAVORO' :
		$params{__TYPE__} eq 'PTE_NOTIFY_CLOSE_OK' ? 'CHIUSURA_LAVORO_OK' :
		$params{__TYPE__} eq 'PTE_NOTIFY_CLOSE_KO' ? 'CHIUSURA_LAVORO_KO' :
		'CHIUSURA_LAVORO_KO'
	;

	my $properties = {
		workActivityId => $params{ACTIVITY_ID},
		__RAW__ => \%params
	};

	$properties->{ACTIVITY} = $params{ACTIVITY} if defined $params{ACTIVITY};

	if ( $action eq 'APERTURA_LAVORO' ) {
		$properties->{SUBCONTRACT_CODE} = $params{SUBCONTRACT_CODE} if defined $params{SUBCONTRACT_CODE};
	}

	my %step_params = (
		ACTION => $action,
		PROPERTIES => $properties,
	);

	return $self->step(%step_params);
}

sub manage_private_permits_notify {
	my $self = shift;
	my %params = @_;
	
	my $art = $self->art();
	
	my $errmsg;
	
	$art->last_error($errmsg)
		&& return undef
			unless $art->check_named_params(
				 ERRMSG		 => \$errmsg
				,PARAMS		 => \%params
				,MANDATORY	  => {
					__TYPE__	=> { isa => 'SCALAR', list => ['PTE_NOTIFY_OPEN', 'PTE_NOTIFY_CLOSE_OK', 'PTE_NOTIFY_CLOSE_KO','PTE_NOTIFY_UPDATE'] },
					ACTIVITY_ID	=> { isa => 'SCALAR', pattern => qr{^\d+$} },
				}
				,IGNORE_EXTRA_PARAMS	=> 1
			);
	
	my $action =
		$params{__TYPE__} eq 'PTE_NOTIFY_OPEN'     ? 'APERTURA_PERMESSO_BUILDING' :
		$params{__TYPE__} eq 'PTE_NOTIFY_UPDATE'   ? 'AGGIORNAMENTO_PERMESSO_BUILDING' :
		$params{__TYPE__} eq 'PTE_NOTIFY_CLOSE_OK' ? 'CHIUSURA_PERMESSO_BUILDING_OK' :
		$params{__TYPE__} eq 'PTE_NOTIFY_CLOSE_KO' ? 'CHIUSURA_PERMESSO_BUILDING_KO' :
		'CHIUSURA_PERMESSO_BUILDING_KO' 
	;

	my $properties = {
		privatePermitsActivityId => $params{ACTIVITY_ID},
		__RAW__ => \%params
	};

	$properties->{ACTIVITY} = $params{ACTIVITY} if defined $params{ACTIVITY};

	if ( $action eq 'APERTURA_PERMESSO_BUILDING' ) {
		$properties->{SUBCONTRACT_CODE} = $params{SUBCONTRACT_CODE} if defined $params{SUBCONTRACT_CODE};
	}

	my %step_params = (
		ACTION => $action,
		PROPERTIES => $properties,
	);

	return $self->step(%step_params);

}

sub remap_works_system_property{
	my $self = shift;
	
	return {
		'survey'				=> 'wSurvey',
		'infrastructure'		=> 'wInfrastructure',
		'laying'			    => 'wLaying',
		'junction'				=> 'wJunction',
		'updateDatabaseF1'		=> 'wUpdateDatabaseF1',
		'updateDatabaseF2'		=> 'wUpdateDatabaseF2',
		'testApp'				=> 'wTestApp',
		'testOTDR'				=> 'wTestOTDR',
		'restoration'			=> 'wRestoration',
		'planning'				=> 'wPlanning',
	};
}

sub get_lc01_activity {
	my $self = shift;

	return $self->{LC01_ACTIVITY} if $self->{LC01_ACTIVITY};

	my $lc01_activity = $self->parent();
	return undef unless defined $lc01_activity;

	$self->{LC01_ACTIVITY} = $lc01_activity;

	return $self->{LC01_ACTIVITY};
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub step(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}

	my $status_name = $self->get_current_status_name();

	# gestisco ad hoc l'azione AGGIORNAMENTO_DATI per dare un migliore feedback all'utente
	if ($params{ACTION} eq 'AGGIORNAMENTO_DATI'){
		# se siamo nello stato finale di LAVORATO è possibile solo aggiornare la fascia di rendicontazione
		if ($status_name eq 'LAVORATO'){
			# sovrascrivo con l'attuale valore tutte le altre properties (volutamente non le activity_properties)
			my $properties = $self->property();
			for my $k(keys %{$params{PROPERTIES}}){
				next if $k eq 'penetrationIndex';
				$params{PROPERTIES}->{$k} = $properties->{$k};
			}
			$params{VIRTUAL} = 1;
			$params{DEST_STATUS} = $status_name;
			$params{IGNORE_FINAL_STATUS} = 1;
		} else {
			unless ($self->can_do_action(NAME => $params{ACTION})){
				$self->art()->last_error(__x("Unable to execute action {action} for activity in status {status}", action => $params{ACTION}, status => $status_name));
				return undef;
			}
		}
	} elsif ($params{ACTION} eq 'AGGIORNA_FASCIA_RENDICONTAZIONE'){
		$self->system->set_property( PROPERTIES => {penetrationIndex => $params{PROPERTIES}->{penetrationIndex}} ) or return;
	}

	# imposto il metaData quando serve
	if (exists $params{PROPERTIES}->{testData016} || exists $params{PROPERTIES}->{paymentNetwork}){
		my $paymentNetwork = exists $params{PROPERTIES}->{paymentNetwork} ? $params{PROPERTIES}->{paymentNetwork} : $self->activity_property('paymentNetwork');
		my $testData016 = exists $params{PROPERTIES}->{testData016} ? $params{PROPERTIES}->{testData016} : $self->activity_property('testData016');
		if (defined $paymentNetwork && defined $testData016){
			if ($paymentNetwork eq $testData016){
				$params{PROPERTIES}->{testDataMeta001} = 'NW NgNeer = NW WPSO'
			} else{
				$params{PROPERTIES}->{testDataMeta001} = 'NW NgNeer <> NW WPSO';
			}
		} elsif (defined $testData016){
			$params{PROPERTIES}->{testDataMeta001} = 'NW NgNeer si / NW WPSO no';
		} elsif (defined $paymentNetwork){
			my $add;
			if ($params{ACTION} eq 'DATI_COLLAUDO_CLIENTE'){
				$add = 1;
			} else {
				# verifico se nella storia c'è almeno una volta l'azione DATI_COLLAUDO_CLIENTE
				my $history = $self->history();
				return undef unless defined $history;

				my $set_filter = $history->set_filter(
						ACTION => ['DATI_COLLAUDO_CLIENTE']
				);
				return undef unless defined $set_filter;
				$add = 1 
					if $history->count();
			}
			$params{PROPERTIES}->{testDataMeta001} = 'NW NgNeer no / NW WPSO si' if $add;
		}
	}
	
	return $self->SUPER::step(%params);
}

# effettuiamo ovverride metodo step per gestire le meta informaazioni di qualsiasi allegato se presente
sub add_documentation(){
	my $self = shift;
	my %params = @_;
	
	if (exists $params{ATTACHMENTS}){
		return undef unless($self->_manage_attachments(%params));
	}
	
	return $self->SUPER::add_documentation(%params);
}

sub _manage_attachments {
	my $self = shift;
	my %params = @_;

	my $system_property = $self->system_property();
	my $meta = {
		activityId		=> $self->id(),
		activityType	=> $self->info('ACTIVITY_TYPE_NAME'),
		cabinetId		=> $self->system()->parent_id(),
		pteId			=> $self->system()->id(),
	};
	$self->art()->last_error(__x("Param {param} must be an ARRAY", param => 'ATTACHMENTS'))
		&& return undef 
			if (ref ($params{ATTACHMENTS}) ne 'ARRAY');
	for my $att (@{$params{ATTACHMENTS}}){
		if (!ref ($att)){
			# lo trasformo in una struttura per gestire i meta
			$att = {
				FILENAME => $att,
				META => $meta
			};
		} elsif (ref($att) eq 'HASH'){
			# aggiungo la chiave META se non esiste
			if (!exists $att->{META}){
				$att->{META} = $meta
			} else {
				# aggiungo le chiavi sovrascrivendole se già presenti
				for my $k (keys %{$meta}){
					$att->{META}->{$k} = $meta->{$k};
				}
			}
			
		} else {
			$self->art()->last_error(__x("Param {param} can be an ARRAY of scalar o hashes", param => 'ATTACHMENTS'));
			return undef; 
		}
	}

	return 1;

}

sub refresh_pte(){
	my $self = shift;
	my %params = @_;

	my $system_property = $self->system_property('customerId','contractId','wUpdateDatabaseF2Status');
	if ($system_property->{'wUpdateDatabaseF2Status'} =~/^(ONGOING|SUSP)$/){
		return undef
			unless $self->booking(
				bookingType		=> 'STEP',
				bookingStatus	=> $system_property->{wUpdateDatabaseF2Status},
				action			=> "AGGIORNAMENTO_DATI",
				ref00			=> $self->system()->parent()->id(),
				ref01			=> $self->system()->id(),
				customerId		=> $system_property->{customerId},
				contractId		=> $system_property->{contractId},
				details			=> [
					{
						type => 'updateDatabaseF2'
					}
				],
				operatorLogin => $self->art()->user()->name(),
				FORCE => 1
			);
	}

	return 1;
}

sub booking(){
	my $self = shift;
	my %params = @_;

	# use Data::Dumper;
	# print STDERR Dumper \%params;

	my $art = $self->art();

	# Si verifica se l'operatore è AT, PM o SERVICE, in caso contrario gli si nega il permesso di eseguire l'azione
	my $profilo = WPSOCORE::Profilo->new( ART => $art );
	unless ( $profilo->is_assistente_tecnico || $profilo->is_project_manager || $profilo->is_service ) {
		$art->last_error(__('Permission denied'));
		return undef;
	}

	my $works_map = $self->remap_works_system_property();
	return undef
		unless defined $works_map;

	my $map = { %$works_map };
	$map->{privatePermits} = 'privatePermits';

	my @keys_map = keys %$map;

	my $errmsg;

	$art->last_error($errmsg)
		&& return undef
			unless $art->check_named_params(
				ERRMSG   => \$errmsg
				,PARAMS   => \%params
				,MANDATORY   => {
					bookingType   => { isa => 'SCALAR', list => ['CREATE', 'STEP'] },
					bookingStatus   => { isa => 'SCALAR', list => ['ONGOING', 'OK', 'KO', 'SUSP', 'N.N.', 'PIC', 'SUSP_BD'] },
					details     => { isa => 'ARRAY' },
				}
				,OPTIONAL     => {
					FORCE                       => { isa => 'SCALAR', list => [0, 1] },
					invalidatePreviousReferences    => { isa => 'SCALAR', list => [0, 1] },
					EXTRA                       => { isa => 'HASH' },
				}
				,IGNORE_EXTRA_PARAMS    => 1
			);

	# il bookingStatus PIC per ora è assorbito da ONGOING. Vedremo in futuro se necessario fargli delle logiche specifiche
	$params{bookingStatus} = 'ONGOING' if $params{bookingStatus} eq 'PIC';

	# il bookingStatus SUSP_BD è mutamente esclusivo rispetto al SUSP e quindi sono equiparabili
	$params{bookingStatus} = 'SUSP' if $params{bookingStatus} eq 'SUSP_BD';

	my $system_property = $self->system_property();

	# gli utenti SERVICE possono movimentare solo lavori e permessi ONGOING che siano loro assegnati
	if ( $profilo->is_service and !$profilo->is_admin ) {
		my $wpsocore = $self->wpsocore or return;
		my $subcontracts = WPSOCORE::Subcontracts->new(ART => $art) or return;
		my $subcontractcode = $subcontracts->get_company_from_username(
			username => $wpsocore->profilo->nome_utente
		);
		return undef unless defined $subcontractcode;
		$subcontractcode = $subcontractcode->{companyCode};
		for my $detail (@{$params{details}}){
			next unless $map->{$detail->{type}};
			my $wStatus      = $system_property->{$map->{$detail->{type}}."Status"} || '';
			my $wSubContract = $system_property->{$map->{$detail->{type}}."LASubContractCode"} || '';
			if ( $wStatus !~/^(ONGOING|SUSP)$/ ) {
				$art->last_error(__('Permission denied'));
				return undef;
			} 
			if ( $wSubContract ne $subcontractcode ) {
				$art->last_error(__('Permission denied'));
				return undef;
			} 
		}
	}

	# verifico che per tutti i details non ci sia già una prenotazioni in corso
	unless ($params{FORCE}){
		for my $detail (@{$params{details}}){
			$art->last_error($errmsg)
				&& return undef
					unless $art->check_named_params(
						ERRMSG   => \$errmsg
						,PARAMS   => \%{$detail}
						,MANDATORY   => {
							type    => { isa => 'SCALAR', list => \@keys_map },
						}
						,IGNORE_EXTRA_PARAMS    => 1
					);
			if (defined $system_property->{$map->{$detail->{type}}."Booking"}){
				$art->last_error(__x("Booking already done for {type}", type => $detail->{type}));
				return undef;
			}
		}
	}
	my %body = %params; # params in lettura e body in scrittura
	$body{customerId} = $system_property->{customerId};
	$body{contractId} = $system_property->{contractId};
	$body{operatorLogin} = $art->user->name;
	if ( $params{bookingType} eq 'STEP' ) {
		$body{ref00} = $self->system->parent->id;
		$body{ref01} = $self->system_id;
	}
	my $event = $params{bookingType} eq 'CREATE' ? 'CREATE'
				: $params{bookingType} eq 'STEP'   ? 'STEP'
				:                                    undef
	;
	my $source_ref = $self->id;
	unless ( defined $event ) {
		$art->last_error(__x("Unknown bookingType {type}", type => $params{bookingType} ));
		return undef;
	}

	$self->art->last_error(__x("Technical assistant not available: please first execute action {action}"
		, action => 'AGGIORNA_AT'
	))
		&& return undef
			unless defined $self->activity_property('teamAssistant');

	my $contract = $self->parent()->get_contract();
	return undef unless defined $contract;

	my $ats = [];
	if (defined $self->activity_property('teamAssistant')){
		$ats = $self->get_teams()->cerca(
			companyCard         => $self->activity_property('teamAssistant'),
			companyAbbreviation => $contract->property('companyAbbreviation')
		);
		return undef unless defined $ats;
	}

	my $sender = $self->_get_sender;
	for my $detail (@{$params{details}}){
		# sbianco cannotCloseWork in modo che venga poi impostato correttamente
		if ( $event eq 'CREATE' ) {
			delete $body{cannotCloseWork} if exists $body{cannotCloseWork};
		} else {
			delete $body{properties}->{cannotCloseWork} if exists $body{properties}->{cannotCloseWork};
		}
		
		next unless $map->{$detail->{type}};
		$body{details} = [ { type => $detail->{type} } ];
		my $detail_type = grep ({ $detail->{type} eq $_ } keys %{$works_map}) ? 'work' : 'permit';
		
		if ($detail->{type} eq 'updateDatabaseF2' || $detail->{type} eq 'updateDatabaseF1') {
			# verifico la presenza di un'eventuale TT in corso
			my $int_paymentNetwork = $self->activity_property('paymentNetwork');
			if ($int_paymentNetwork){
				my $is_fatal;
				my ($response_code, $response_content) = $self->wpsocore()->invoke_rest(
					RESOURCE		=> $ENV{LC02_TT001_RESOURCE}
					,METHOD			=> 'GET'
					,QUERY_PARAMS	=> {
						type					=> 'TT001',
						active					=> 1,
						ap_customerId_equal		=> $self->system_property('customerId'),
						ap_contractId_equal		=> uc($self->system_property('PTEScope')),
						ap_networkId_equal		=> $int_paymentNetwork,
						excludeInfo				=> 1
					}
					,HEADERS		=> {
						'content-type'	=> 'application/json'
						,'accept'		=> 'application/json'
						,'x-api-key'	=> $ENV{LC02_TT001_API_KEY}
					}
					,IS_FATAL		=> \$is_fatal
				);
				unless(defined $response_code) {
					$art->last_error(__("Unable to retrieve info for TT: please retry later"));
					return undef;
				}

				if ($response_code eq HTTP_OK){
					my $objs = from_json($response_content);
					if (scalar @$objs){
						for my $tt001 (@$objs){
							# se lo trovo devo verificare se si tratta di un TT con centrale/armadio o meno
							if (
								$params{bookingType} eq 'CREATE'
							) {
								if 
								(
									!defined $tt001->{properties}->{tt001TargetAsset}
									||
									$tt001->{properties}->{tt001TargetAsset} eq $self->system_property('centralId').'-'.$self->system_property('cabinet')
								){
									$art->last_error(__x("Unable to open works: found ongoing {tt} for network {networkId}",tt => 'TT '.$tt001->{properties}->{customerSystem}.' '.$tt001->{id}, networkId => $int_paymentNetwork));
									return undef;
								}
							} else {
								if ($detail->{type} eq 'updateDatabaseF1') {
									if ($self->system_property('wUpdateDatabaseF1Status') =~/^(ONGOING|SUSP)$/){
										$body{action} = 'SOSPENSIONE_CAUSA_TT';
										$params{EXTRA}->{ttId} = $tt001->{id};
										$params{EXTRA}->{ttCustomerSystem} = $tt001->{properties}->{customerSystem};
									}
								} elsif ($detail->{type} eq 'updateDatabaseF2') {
									if ($self->system_property('wUpdateDatabaseF2Status') =~/^(ONGOING|SUSP)$/){
										$body{action} = 'SOSPENSIONE_CAUSA_TT';
										$params{EXTRA}->{ttId} = $tt001->{id};
										$params{EXTRA}->{ttCustomerSystem} = $tt001->{properties}->{customerSystem};
									}
								}
							}
						}
					}
				} else {
					$art->last_error(__("Please retry later"));
					return undef;
				}
			} 
			else {
				if ($detail->{type} eq 'updateDatabaseF1') {
					if ($self->system_property('wUpdateDatabaseF1LAS') eq 'BLOCCO_SISTEMA_CLIENTE'){
						$body{action} = 'DEASSOCIAZIONE_NETWORK';
					}
				} elsif ($detail->{type} eq 'updateDatabaseF2') {
					if ($self->system_property('wUpdateDatabaseF2LAS') eq 'BLOCCO_SISTEMA_CLIENTE'){
						$body{action} = 'DEASSOCIAZIONE_NETWORK';
					}
				}
			}
			my $cannotCloseWork = 1;
			my $cannotCloseWorkReason = '';

			if ($detail->{type} eq 'updateDatabaseF2') {
				my $enum_activity_type = $self->art()->enum_activity_type(
					EXTENDED_OUTPUT          => 1,
					SHOW_ONLY_WITH_VISIBILITY  => 1
				);
				return undef unless defined $enum_activity_type;
				
				my @eats = grep {$_->{ACTIVITY_TYPE_NAME} eq 'LC01'} @{$enum_activity_type};
				
				my @cannotCloseWorkReasons;
				if (
					defined $self->activity_property('paymentNetwork')
					&&
					$self->parent()->property('__DTC_PLN_SERVICE')
					&&
					$self->parent()->property('__DTC_CWO_SERVICE')
					&&
					$self->parent()->property('__DTC_SIN_SERVICE')
				) {
					$cannotCloseWork = 0;
				} else {
					push @cannotCloseWorkReasons, __x("Unable to close work without network associated for PTE.") unless defined $self->activity_property('paymentNetwork');
					for my $file_lc01_type ('PLN', 'CWO', 'SIN'){
						my @eatsd = grep {$_->{NOME_TIPO_DATO_TECNICO_ATT} eq '__DTC_'.$file_lc01_type.'_SERVICE'} @{$eats[0]->{ACTIVITY_TYPE_DOCUMENT_TYPES}};
						push @cannotCloseWorkReasons, __x("Missing file of type \"{type}\" in cabinet's documentation.", type => $eatsd[0]->{DESCRIZIONE}) unless $self->parent()->property('__DTC_'.$file_lc01_type.'_SERVICE');
					}
				}
				$cannotCloseWorkReason = join (' ', @cannotCloseWorkReasons);
			}
			elsif ($detail->{type} eq 'updateDatabaseF1') {
				$cannotCloseWorkReason = __x("Unable to close work without CLLI defined for PTE");
				if (defined $self->activity_property('CLLI')) {
					$cannotCloseWork = 0;
					$cannotCloseWorkReason = '';
				}
			}

			if ( $event eq 'CREATE' ) {
				$body{cannotCloseWork} = $cannotCloseWork;
				$body{cannotCloseWorkReason} = $cannotCloseWorkReason if $cannotCloseWork;
			} else { # event eq 'STEP'
				$body{properties}->{cannotCloseWork} = $cannotCloseWork;
				$body{properties}->{cannotCloseWorkReason} = $cannotCloseWorkReason if $cannotCloseWork;
				if($body{action} eq 'SOSPENSIONE_CAUSA_TT'){
					# “TT” + <Sistema cliente> + <ID>. Ad esempio: TT Daphne 34567
					$body{properties}->{externalSuspensionId} = $params{EXTRA}->{ttId};
					$body{properties}->{externalSuspensionNote} = 'TT '.$params{EXTRA}->{ttCustomerSystem}.' '.$params{EXTRA}->{ttId};
				}
			}
		}
		
		my $data = to_json(\%body);
		my %ra_params = (
			SOURCE_REF => $source_ref,
			DATA       => $data,
		);

		my $new_system_properties;
		unless (scalar @{$ats}){
			if (defined $self->activity_property('teamAssistantName')){
				$new_system_properties = {
					$map->{$detail->{type}} . 'BookingWarning' => __x("Technical assistant {teamAssistantName} not more available: please first execute action {action}"
						, teamAssistantName => $self->activity_property('teamAssistantName')
						, action => 'AGGIORNA_AT'
					)
				};
			} else {
				$new_system_properties = {
					$map->{$detail->{type}} . 'BookingWarning' => __x("Technical assistant not available: please first execute action {action}"
						, action => 'AGGIORNA_AT'
					)
				};
			}
		} else {
			$new_system_properties = {
				$map->{$detail->{type}} . 'Booking' => $data,
				$map->{$detail->{type}} . 'BookingWarning' => undef
			};

			if ( $event eq 'CREATE' ) {
				$new_system_properties->{$map->{$detail->{type}} . 'BookingStatus' } = 'ONGOING';
				if ( $detail_type eq 'work' ) {
					$sender->send_booking_create_work(%ra_params) or return;
				} elsif ( $detail_type eq 'permit' ) {
					$sender->send_booking_create_permit(%ra_params) or return;
				}
			} elsif ( $event eq 'STEP' ) {
				$new_system_properties->{$map->{$detail->{type}} . 'BookingStatus' } = $params{bookingStatus};
				if ( $detail_type eq 'work' ) {
					$sender->send_booking_step_work(%ra_params) or return;
				} elsif ( $detail_type eq 'permit' ) {
					$sender->send_booking_step_permit(%ra_params) or return;
				}
			}
		}
		$self->system->set_property( PROPERTIES => $new_system_properties ) or return;
	}

	$self->refresh();

	return 1;
}

sub inserisci_misurato(){
	my $self = shift;
	my %params = @_;
	my $errmsg;

	my $art = $self->art();

	$art->last_error($errmsg)
		&& return undef
			unless $art->check_named_params(
				 ERRMSG		 => \$errmsg
				,PARAMS		 => \%params
				,MANDATORY	  => {
					type 				=> { isa => 'SCALAR', list => [keys %{$self->remap_works_system_property()}, 'privatePermits'] },
					externalSequence	=> { isa => 'SCALAR' },
				}
				,OPTIONAL		=> {
					subContractCode	=> { isa => 'SCALAR' },
					teamId	=> { isa => 'SCALAR' },
				}
				,IGNORE_EXTRA_PARAMS	=> 0
			);

	if (!defined $params{subContractCode} && !defined $params{teamId}){
		$self->art()->last_error(__x("At least on param between {param} and {param1} must be defined", param => 'subContractCode', param1 =>'teamId'));
		return undef;
	}

	my $sql = qq{
		insert into core.MISURATO_FIBERCOP_CLUSTER(
			ID_ATTIVITA_LC01,
			ID_ATTIVITA_LC02,
			OPERAZIONE,
			TIPO_LAVORO,
			"subContractCode",
			"teamId"
		) values (
			?,
			?,
			?,
			?,
			?,
			?
		)
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_IMISF', $sql);
	
	my @bind_params = (
		$self->parent_id(),
		$self->id(),
		$params{externalSequence},
		$params{type},
		$params{subContractCode}||undef,
		$params{teamId}||undef,
	);
	
	my $res = $prepare->do(@bind_params);
	
	unless ($res) {
		$self->art()->last_error($self->get_db->get_errormessage);
		return undef;
	}

	return 1;
}

sub rimuovi_precedente_misurato(){
	my $self = shift;
	my %params = @_;
	my $errmsg;

	my $art = $self->art();

	$art->last_error($errmsg)
		&& return undef
			unless $art->check_named_params(
				 ERRMSG		 => \$errmsg
				,PARAMS		 => \%params
				,MANDATORY	  => {
					type 				=> { isa => 'SCALAR', list => [keys %{$self->remap_works_system_property()}, 'privatePermits'] },
				}
				,OPTIONAL		=> {}
				,IGNORE_EXTRA_PARAMS	=> 0
			);

	my $sql = qq{
		delete core.MISURATO_FIBERCOP_CLUSTER
		where ID_ATTIVITA_LC02 = ?
		and TIPO_LAVORO = ?
	};
	
	my $prepare = $self->art()->_create_prepare(__PACKAGE__.'_RMISF', $sql);
	
	my @bind_params = (
		$self->id(),
		$params{type},
	);
	
	my $res = $prepare->do(@bind_params);
	
	unless ($res) {
		$self->art()->last_error($self->get_db->get_errormessage);
		return undef;
	}

	return 1;
}

if (__FILE__ eq $0) {

    use API::ART;
    use API::ART::Activity::Factory;
    use Data::Dumper;

	my $art = API::ART->new(
		ARTID => $ENV{ARTID},
		USER => 'VASILE',  # ADMIN
		#USER => 'FC_AT_106656', # AT
		#USER => 'PM_TIM', # PM
		#USER => 'XT04420', # SERVICE
		#USER => 'XT03870', # SERVICE
		PASSWORD => 'pippo123'
	) or die 'Internal error';

    my %params = (
			"bookingType" => "STEP",
			"bookingStatus" => "OK",
			"action" => "FINE_LAVORI",
			"properties" => {
				"startWorkDate" => "2021-03-26T02:00:00.000000000+02:00",
				"endWorkDate" => "2021-03-26T02:00:00.000000000+02:00"
			},
			"details" => [
				{
					"type" => "laying"
				},
				{
					"type" => "privatePermits"
				},
			],
    );

	my $activity = API::ART::Activity::Factory->new( ART => $art, ID => 63468 ) or die $art->last_error;
	$activity->booking(%params) or die $art->last_error;

    $art->cancel;
}

sub is_ftth {
	my ( $self ) = @_;
	if ( !exists $self->{IS_FTTH} ) {
		$self->{IS_FTTH} = $self->activity_property('PTEScope') eq 'FTTH' ? 1 : 0;
	}
	return $self->{IS_FTTH};
	
}

sub cfs  {
	my $self = shift;
	
	$self->{CFS} = WPSOCORE::FieldService->new(ART => $self->art) unless exists $self->{CFS};
	
	return $self->{CFS};
}

sub get_external_sync_fs {
    my $self = shift;
	my %params = @_;
	
	my $art = $self->art();
	
	my $errmsg;
	
	$art->last_error($errmsg)
		&& return undef
			unless $art->check_named_params(
				 ERRMSG		 => \$errmsg
				,PARAMS		 => \%params
				,MANDATORY	  => {
					fieldActivityOrigin	=> { isa => 'SCALAR', list => ['works','ap','core' ] },
				}
				,OPTIONAL	  => {
					externalSequence	=> { isa => 'SCALAR' },
					workId				=> { isa => 'SCALAR' },
				}
				,IGNORE_EXTRA_PARAMS	=> 1
			);

	if (!defined $params{externalSequence} && !defined $params{workId}){
		$art->last_error(__x("At least one param between {param} and {param1} must be defined", param => 'externalSequence', param1 => 'workId'));
		return undef;
	}

    my $external_sync_fs = $self->get_children(ACTIVITY_TYPE_NAME => ['EXTERNAL_SYNC_FS']);
    return unless defined $external_sync_fs;
	my @ext;
	if (defined $params{externalSequence} && defined $params{workId}){
		@ext = grep {$_->activity_property('workId') eq $params{workId} && $_->activity_property('externalSequence') eq $params{externalSequence} && $_->activity_property('fieldActivityOrigin') eq $params{fieldActivityOrigin}} @{$external_sync_fs};
	} elsif (defined $params{externalSequence}){
		@ext = grep {$_->activity_property('externalSequence') eq $params{externalSequence} && $_->activity_property('fieldActivityOrigin') eq $params{fieldActivityOrigin}} @{$external_sync_fs};
	} else { # defined $params{workId}
		@ext = grep {$_->activity_property('workId') eq $params{workId} && $_->activity_property('fieldActivityOrigin') eq $params{fieldActivityOrigin}} @{$external_sync_fs};
	}
    return $ext[0];
}

1;


