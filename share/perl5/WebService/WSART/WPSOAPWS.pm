package WebService::WSART::WPSOAPWS;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use HTTP::Status qw(:constants :is status_message);
use File::Copy;
use Data::Dumper;

use API::ART::Collection::Activity;

use WPSOAP::Collection::System::PROJECT;
use WPSOAP::Collection::Activity::AP_LC;
use WPSOAP::Collection::Activity::BUILDING_LC;
use WPSOAP::Collection::Activity::PERMESSO_LAVORI;
use WPSOAP::Collection::Activity::PERMESSO_BUILDING;
use WPSOAP::Collection::Activity::LC06;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use Dancer2 appname => 'API::ART::REST'; 

prefix defined $ENV{WPSOAPWS_WS_ROUTES_PREFIX} ? $ENV{WPSOAPWS_WS_ROUTES_PREFIX} : '/wpso/ap';

use WPSOAP;

############ HOOKS ################

### NOTA: l'hook viene eseguito sempre anche se fuori dal prefix

hook before => sub {
	
	debug __PACKAGE__.": sono in hook before";
	
	my $isOptionsNotPreflightAttach = 0;

	if (
		API::ART::REST::is_options() &&
		!API::ART::REST::is_preflight() &&
		(
			request->path() =~ m{/(.*)/contracts/(.*)/so/projects/(.*)/permits-areas/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/projects/(.*)/permits-areas/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/projects/(.*)/permits-areas/\d+/permits/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/projects/(.*)/permits-areas/\d+/permits/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/projects/(.*)/buildings/(.*)/permits/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/projects/(.*)/buildings/(.*)/permits/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/permits/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/permits/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/sites/\d+/attachments}
			||
			request->path() =~ m{/(.*)/contracts/(.*)/so/sites/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/customers/(.*)/tt001s/\d+/history/\d{14}/attachments/\d+}
                        ||
                        request->path() =~ m{/customers/(.*)/tt001s/\d+/attachments}
		)
	){
		$isOptionsNotPreflightAttach = 1;
		my ($result, $error_message, $error_internal_message) = API::ART::REST::init_api_art();
		# se e' un errore mi blocco
		if (API::ART::REST::is_error($result)){
			info "Error:\n" .Dumper {
				UUID		=> vars->{uuid}
				,message	=> $error_message
				,internalMessage => $error_internal_message
			};
			status($result);
			halt(); # FIXME: bisognerebbe ritornare la struttura $error ma la funzione halt() non lo permette
		}
	}

	unless ($isOptionsNotPreflightAttach){
		return
			if API::ART::REST::is_options();
	}
	
	# se è la rotta di login non devo fare nulla
	return if API::ART::REST::is_login_route();
	
	my $wpsoap;
	my $collProject;
	my $collAp;
	my $collPermessoLavori;
	my $collPermessoBuilding;
	my $collSites;
	
	my $collActivity = API::ART::Collection::Activity->new(ART => vars->{api_art});
		
	eval{
		$wpsoap = WPSOAP->new(ART => vars->{api_art});
		$collProject = WPSOAP::Collection::System::PROJECT->new(ART => vars->{api_art});
		$collAp = WPSOAP::Collection::Activity::AP_LC->new(ART => vars->{api_art});
		$collPermessoLavori = WPSOAP::Collection::Activity::PERMESSO_LAVORI->new( ART => vars->{api_art});
		$collPermessoBuilding = WPSOAP::Collection::Activity::PERMESSO_BUILDING->new( ART => vars->{api_art});
		$collSites = WPSOAP::Collection::Activity::LC06->new( ART => vars->{api_art});
	};
	
	if ($@ || ! $collActivity){
		status(HTTP_INTERNAL_SERVER_ERROR);
		$ENV{_WSART_UUID} = vars->{uuid};
		halt();
	}

	var wpsoap => $wpsoap;
	var collActivity => $collActivity;
	var collProj => $collProject;
	var collAp => $collAp;
	var collPermessoLavori => $collPermessoLavori;
	var collPermessoBuilding => $collPermessoBuilding;
	var collSites => $collSites;
	var "remap-blocks" => {
		PUBLIC_PERMITS			=> {
			ACTIVITY_TYPE_NAME => 'PERMESSO_LAVORI'
		},
		PRIVATE_PERMITS			=> {
			ACTIVITY_TYPE_NAME => 'PERMESSO_BUILDING'
		},
		SITES			=> {
			ACTIVITY_TYPE_NAME => 'LC06'
		},
	};

};

############ PRIVATE ################

# ritorna il rimappamento del profilo
sub _remap_profilo {
	
	my ($api, $profilo) = @_;
	
	my $remapped_info = {
		 "idUtente" => $profilo->id_utente()
		,"nomeUtente" => $profilo->nome_utente()
		,"nome" => $profilo->nome()
		,"cognome" => $profilo->cognome()
		,"email" => $profilo->email()
		,"gruppi" => $profilo->gruppi()
		,"telefoni" => $profilo->telefoni()
	};
	
	return $remapped_info;

}

sub _remap_config_profilo {
	
	my ($api, $profilo) = @_;
	
	my $remapped_info = {
		"groups" => $profilo->gruppi()
		,"isAdmin" => $profilo->is_admin() ? $JSON::true : $JSON::false
		,"isAssistenteTecnicoCivile" => $profilo->is_assistente_tecnico_civile() ? $JSON::true : $JSON::false
		,"isProgettista" => $profilo->is_progettista() ? $JSON::true : $JSON::false
		,"isProgettistaCp" => $profilo->is_progettista_cp() ? $JSON::true : $JSON::false
		,"isCityManager" => $profilo->is_city_manager() ? $JSON::true : $JSON::false
		,"isAreaManager" => $profilo->is_area_manager() ? $JSON::true : $JSON::false
		,"isCoordinatoreAssistenteTecnicoCivile" => $profilo->is_coordinatore_at_civile() ? $JSON::true : $JSON::false
		,"isCoordinatoreAssistenteTecnicoGiunzioni" => $profilo->is_coordinatore_at_giunzioni() ? $JSON::true : $JSON::false
		,"isAssistenteTecnicoGiunzioni" => $profilo->is_assistente_tecnico_giunzioni() ? $JSON::true : $JSON::false
		,"isSupportoProgettazione" => $profilo->is_supporto_progettazione() ? $JSON::true : $JSON::false
		,"isReferentePermessiAreaPermessi" => $profilo->is_referente_permessi_ap() ? $JSON::true : $JSON::false
		,"isReferentePermessiBuilding" => $profilo->is_referente_permessi_building() ? $JSON::true : $JSON::false
		,"isContabile" => $profilo->is_contabile() ? $JSON::true : $JSON::false
		,"isPlanAndProgram" => $profilo->is_plan_and_program() ? $JSON::true : $JSON::false
		,"isQualityServiceAssurance" => $profilo->is_quality_service_assurance() ? $JSON::true : $JSON::false
		,"isFocalPoint" => $profilo->is_focal_point() ? $JSON::true : $JSON::false
		,"isCoordinatoreDelivery" => $profilo->is_coordinatore_delivery() ? $JSON::true : $JSON::false
		,"isAssistenteTecnico" => $profilo->is_assistente_tecnico() ? $JSON::true : $JSON::false
		,"isProjectManager" => $profilo->is_project_manager() ? $JSON::true : $JSON::false
		,"isRegionalManager" => $profilo->is_regional_manager() ? $JSON::true : $JSON::false
		,"isService" => $profilo->is_service() ? $JSON::true : $JSON::false
	};
	
	return $remapped_info;

}

sub _get_project{
	
	my ($customerId, $contractId, $projectId) = @_;
	
	my $api = vars->{api_art};
	
	my $projects = vars->{collProj}->cerca(
		 customerId => $customerId
		,contractId => $contractId
		,projectId => $projectId
	);
	croak $api->last_error()
		unless defined $projects;
	
	$api->last_error(__("Project not found!"))
		unless scalar @{$projects};

	return $projects->[0];
}

sub _get_area_permesso{
	
	my ($project, $permitsAreaId) = @_;
	
	my $api = vars->{api_art};
	
	my $ap_systems = $project->get_children(SYSTEM_TYPE_NAME => ['AP'], SHOW_ONLY_WITH_VISIBILITY => 1); 
	croak $api->last_error()
		unless defined $ap_systems;
	
	my @found = grep {$_->property('permitsAreaId') eq $permitsAreaId} @{$ap_systems};
	$api->last_error(__("Permits Area not found!"))
		&& return undef
			unless scalar @found;
	
	my $acts = $found[0]->get_activities_object(
		ACTIVITY_TYPE_NAME => ['AP_LC']
		, ACTIVITY_PROPERTIES_EQUAL => {
			permitsAreaId => $permitsAreaId
		}
	);
	croak $api->last_error()
		unless defined $acts;
	
	$api->last_error(__("Permits Area not found!"))
		&& return undef
			unless scalar @{$acts};
	
	return $acts->[0];
}

sub _get_permesso_lavori{
	
	my ($ap, $permitId) = @_;
	
	my $api = vars->{api_art};
	
	my $acts = $ap->system()->get_activities_object(
		ACTIVITY_TYPE_NAME => ['PERMESSO_LAVORI']
		, ID_EQUAL => $permitId
	);
	croak $api->last_error()
		unless defined $acts;
	
	$api->last_error(__("Public permit not found!"))
		&& return undef
			unless scalar @{$acts};
		
	return $acts->[0];
}

sub _get_permesso_fc{
	
	my ($customerId, $contractId, $permitId, $params) = @_;
	
	my $api = vars->{api_art};
	my $collActivity = vars->{collActivity};

	my $p = {
		ACTIVITY_TYPE_NAME => ['PERMESSO_LAVORI', 'PERMESSO_BUILDING']
		, ID_EQUAL => $permitId
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, SYSTEM_PROPERTIES_EQUAL => {
			customerId	=> $customerId,
			contractId	=> $contractId
		}
	};
	
	my $acts;
	if ($params->{OBJECT}){
		$acts = $collActivity->find_object(%$p);
	} else {
		$acts = $collActivity->find_id(%$p);
	}
	croak $api->last_error()
		unless defined $acts;
	
	$api->last_error(__("Permit not found!"))
		&& return undef
			unless scalar @{$acts};
	
	return $acts->[0];
}

sub _get_sito{
	
	my ($customerId, $contractId, $siteId, $params) = @_;
	
	my $api = vars->{api_art};
	my $collActivity = vars->{collActivity};

	my $p = {
		ACTIVITY_TYPE_NAME => ['LC06']
		, ID_EQUAL => $siteId
		, SHOW_ONLY_WITH_VISIBILITY => 1
		, SYSTEM_PROPERTIES_EQUAL => {
			customerId	=> $customerId,
			contractId	=> $contractId
		}
	};
	
	my $acts;
	if ($params->{OBJECT}){
		$acts = eval{$collActivity->find_object(%$p)};
	} else {
		$acts = eval{$collActivity->find_id(%$p)};
	}
	$api->last_error($@)
		&& return undef
			unless defined $acts;
	
	$api->last_error(__("Site not found!"))
		&& return undef
			unless scalar @{$acts};
		
	return $acts->[0];
}

sub _get_buildings{
	
	my ($project, $buildingSinfoId) = @_;
	
	my $api = vars->{api_art};
	
	my $building_systems = $project->get_children(SYSTEM_TYPE_NAME => ['BUILDING'], SHOW_ONLY_WITH_VISIBILITY => 1); 
	croak $api->last_error()
		unless defined $building_systems;
	
	my @found = grep {$_->property('buildingSinfoId') eq $buildingSinfoId} @{$building_systems};
	$api->last_error(__("Building not found!"))
		&& return undef
			unless scalar @found;
	
	my $acts = $found[0]->get_activities_object(
		ACTIVITY_TYPE_NAME => ['BUILDING_LC']
		, ACTIVITY_PROPERTIES_EQUAL => {
			buildingSinfoId => $buildingSinfoId
		}
	);
	croak $api->last_error()
		unless defined $acts;
	
	$api->last_error(__("Building not found!"))
		&& return undef
			unless scalar @{$acts};
	
	return $acts->[0];
}

sub _get_permesso_building{
	
	my ($building, $permitId) = @_;
	
	my $api = vars->{api_art};
	
	my $acts = $building->system()->get_activities_object(
		ACTIVITY_TYPE_NAME => ['PERMESSO_BUILDING']
		, ID_EQUAL => $permitId
	);
	croak $api->last_error()
		unless defined $acts;
	
	$api->last_error(__("Private permit not found!"))
		&& return undef
			unless scalar @{$acts};
		
	return $acts->[0];
}

sub _manage_attachments{
	
	my ($attachments) = @_;
	
	my $api = vars->{api_art};
	
	my $attachment_list = [];
	
	foreach my $att (@{$attachments}) {
		my $filename = $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".API::ART::REST::get_sid()."_".$att;

		$api->last_error(__x("Not existent attachment"))
			&& return undef
				unless -f $filename && -f $filename.".idx";
		
		croak __x("Unable to open index file {file}.idx: {error}", file => $filename, error => $!)
			unless (open FD, $filename.".idx");
		
		my $idxjson = '';
		while(<FD>) {
			$idxjson .= $_;	
		}
		
		my $idx = eval{ from_json($idxjson) };
		croak __x("Unable to parse JSON idxfile {file}.idx: {error}", file => $filename, error => $@)
			if ($@);
		
		croak __x("Unable to copy file from {file} to {file1}: {error}", file => $filename, file1 => $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}, error => $!)
			unless (copy($filename,$ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}));
		
		my $attach_obj = {
			FILENAME => $ENV{ART_REPOSITORY_TMP_TMPFILES}."/".$idx->{filename}
		};
		$attach_obj->{'TITLE'} = $idx->{title} if exists $idx->{title};
		$attach_obj->{'DESCRIPTION'} = $idx->{description} if exists $idx->{description};
		$attach_obj->{'REVISION'} = $idx->{revision} if exists $idx->{revision};
		$attach_obj->{'REF_DATE'} = $idx->{refDate} if exists $idx->{refDate};
		$attach_obj->{'DOC_TYPE'} = $idx->{docType} if exists $idx->{docType};
		
		push @{$attachment_list}, $attach_obj;
	}
	
	return $attachment_list;
	
}

####### ROUTES ###########

######## ROUTE /ping ###########

options '/ping' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_ping_get {

	get '/ping' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		return API::ART::REST::send_ok(CODE => HTTP_NO_CONTENT);
	};
	
}

route_ping_get();

######## ROUTE /profilo ###########

options '/profilo' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_profilo_get {

	get '/profilo' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		return API::ART::REST::send_ok(MSG => _remap_profilo($api, $wpsoap->profilo()));
	};
}

route_profilo_get();

######## ROUTE /config ###########

options '/config' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_config_get {

	get '/config' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $ret = {};
		
		$ret->{userProfile} = _remap_config_profilo($api, $wpsoap->profilo());
		$ret->{profile} = $ret->{userProfile}; # FIXME: chiave da deprecare
		
		return API::ART::REST::send_ok(MSG => $ret);
	};

}

route_config_get();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET','POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_get {

	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap_systems = $project->get_children(SYSTEM_TYPE_NAME => ['AP'], SHOW_ONLY_WITH_VISIBILITY => 1); 
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless defined $ap_systems;
		
		$query_params{type_equal} = 'AP_LC';
		# ottimizzazione: Bisogna implementare in API::ART e API::ART::REST i filtri sulle gerarchie dei sistemi
		$query_params{systemId} = [map {$_->id()} @{$ap_systems}];
		
		# imposto filtro in modo che recuperi solo le attività legate al progetto
		$query_params{sp_customerId_equal} = param('customerId');
		$query_params{sp_contractId_equal} = param('contractId');
		$query_params{sp_projectId_equal} = param('projectId');
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
		
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $coll = vars->{collAp};
		
		my %create_params = (
			"contractId"			=> param('contractId'),
			"projectId"				=> param('projectId'),
			"customerId"			=> param('customerId'),
			"permitsAreaId"			=> $body{'permitsAreaId'},
			"name"					=> $body{'name'},
			"requestDate"			=> $body{'requestDate'},
			"polygon"				=> $body{'polygon'}
		);
		
		$create_params{infrastructureLength} = $body{'infrastructureLength'} if defined $body{'infrastructureLength'};
		$create_params{note} = $body{'note'} if defined $body{'note'};
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
		}
		
		my $ap = $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $ap );
		
		my $remap_results = API::ART::REST::remap_activity($api,$ap);
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);

	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_post();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT', 'DELETE' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_get {

	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $ap->id()});
		
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_put {
	
	put '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId' => sub {
		my $api = vars->{api_art};
			
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		#verifico la coerenza dei dati
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Mismatch on param permitsAreaId between uri and body"))
			if (defined $body{permitsAreaId} && param('permitsAreaId') ne $body{permitsAreaId});
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $step_param = {
			action => 'MODIFICA_AREA'
			, properties => {
				name => $body{name}
				,requestDate => $body{requestDate}
				,polygon => $body{polygon}
				,infrastructureLength => $body{infrastructureLength}
			}
		};
		
		$step_param->{properties}->{note} = $body{note} if defined $body{note};
		
		$step_param->{attachments} = $body{attachments} if defined $body{attachments};
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => $step_param, URI_PARAMS => {ID => $ap->id()});
	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_put();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_del {
	
	del '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId' => sub {
		my $api = vars->{api_art};
			
		my $wpsoap = vars->{wpsoap};
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless defined $ap->cancellazione();
		
		my $msg = $ap->activity_property("reasonCancelKO");
		
		# se lo stato finale è CANCELLATA allora restituisco un NO_CONTENT
		if ($ap->is_closed()){
			API::ART::REST::do_api_save( $api );
			return API::ART::REST::send_ok(CODE => HTTP_NO_CONTENT);	
		} else {
			API::ART::REST::do_api_save( $api );
			# restituisco un BAD_REQUEST
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $msg);
		}
	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_del();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_post {
	
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history' => sub {
		my $api = vars->{api_art};
			
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $ap->id()});
	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_post();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_get {
	
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
			
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $ap->id()});
	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_get;

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $ap ? $ap->id() : param('permitsAreaId')), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_transitionId_attachments_sequence_get {
	
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history/:transitionId/attachments/:sequence' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
			
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $ap->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_transitionId_attachments_sequence_get;

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
			
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $ap->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_history_transitionId_attachments_sequence_delete();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/attachments ###########

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_attachments_options {
	options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
			
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $ap->id()});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_attachments_options();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_attachments_get {
	
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/attachments' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
			
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $ap->id()});
	};
}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_attachments_get;

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_attachments_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %body = request->params('body');
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
			
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $ap->id()});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_attachments_post();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET','POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		$query_params{type_equal} = 'PERMESSO_LAVORI';
		# ottimizzazione: Bisogna implementare in API::ART e API::ART::REST i filtri sulle gerarchie dei sistemi
		$query_params{systemId} = [$ap->system()->id()];
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $coll = vars->{collPermessoLavori};
		
		my %create_params = (
			"AP_LC"						=> $ap # ne ho solo una: altrimenti ci sarebbe un bug in fase di creazione
			,"publicPermitDescription"	=> $body{'publicPermitDescription'}
			,"publicPermitCategory"		=> $body{'publicPermitCategory'}
			,"authority"				=> $body{'authority'}
			,"publicPermitType"			=> $body{'publicPermitType'}
		);
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
		}
		
		my $pl = $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $pl );
	
		my $remap_results = API::ART::REST::remap_activity($api,$pl);
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);

	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_post();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_get();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history' => sub {
		my $api = vars->{api_art};
			
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_post();		

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_get();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $permitId ? $permitId->id() : param('permitId')), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_transitionId_attachments_sequence_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_transitionId_attachments_sequence_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_history_transitionId_attachments_sequence_delete();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/attachments ###########

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_attachments_options {
	options '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_attachments_options();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_attachments_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/attachments' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_attachments_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_attachments_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/permits-areas/:permitsAreaId/permits/:permitId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %body = request->params('body');
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap = eval{_get_area_permesso($project, param('permitsAreaId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $ap;
		
		my $permitId = eval{_get_permesso_lavori($ap, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $permitId->id()});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_permitsAreaId_permits_permitId_attachments_post();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_buildings_get {

	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $ap_systems = $project->get_children(SYSTEM_TYPE_NAME => ['BUILDING'], SHOW_ONLY_WITH_VISIBILITY => 1); 
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless defined $ap_systems;
		
		$query_params{type_equal} = 'BUILDING_LC';
		# ottimizzazione: Bisogna implementare in API::ART e API::ART::REST i filtri sulle gerarchie dei sistemi
		$query_params{systemId} = [map {$_->id()} @{$ap_systems}];
		
		# imposto filtro in modo che recuperi solo le attività legate al progetto
		$query_params{sp_customerId_equal} = param('customerId');
		$query_params{sp_contractId_equal} = param('contractId');
		$query_params{sp_projectId_equal} = param('projectId');
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
		
	};

}

route_customerId_contracts_contractId_so_projects_projectId_buildings_get();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_get {

	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $building->id()});
		
	};

}

route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_get();

######### ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/history ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_history_get {
	
	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/history' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
			
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $building->id()});
	};
}

route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_history_get;

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET','POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_permits_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		$query_params{type_equal} = 'PERMESSO_BUILDING';
		# ottimizzazione: Bisogna implementare in API::ART e API::ART::REST i filtri sulle gerarchie dei sistemi
		$query_params{systemId} = [$building->system()->id()];  
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
	};
}
route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_permits_get();

sub route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_permits_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $coll = vars->{collPermessoBuilding};
		
		my %create_params = (
			"BUILDING_LC"				=> $building # ne ho solo una: altrimenti ci sarebbe un bug in fase di creazione
			,"privatePermitDescription"	=> $body{'privatePermitDescription'}
			,"privatePermitCategory"	=> $body{'privatePermitCategory'}
			,"privatePermitType"		=> $body{'privatePermitType'}
		);
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
		}
		
		my $pb = $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $pb );
	
		my $remap_results = API::ART::REST::remap_activity($api,$pb);
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);

	};
}
route_customerId_contracts_contractId_so_projects_projectId_buildings_buildingSinfoId_permits_post();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_get();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history ###########

options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history' => sub {
		my $api = vars->{api_art};
			
		my $wpsoap = vars->{wpsoap};
		
		### aggiungere check raml
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_post();		

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_get();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $permitId ? $permitId->id() : param('permitId')), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_transitionId_attachments_sequence_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_transitionId_attachments_sequence_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id(), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_history_transitionId_attachments_sequence_delete();

######## ROUTE /:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/attachments ###########

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_attachments_options {
	options '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_attachments_options();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_attachments_get {
	get '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/attachments' => sub {
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitId->id()});
	};
}
route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_attachments_get();

sub route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_attachments_post {
	post '/:customerId/contracts/:contractId/so/projects/:projectId/buildings/:buildingSinfoId/permits/:permitId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %body = request->params('body');
		
		my $project = eval{_get_project(param('customerId'), param('contractId'), param('projectId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $project;
		
		my $building = eval{_get_buildings($project, param('buildingSinfoId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $building;
		
		my $permitId = eval{_get_permesso_building($building, param('permitId'));};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitId;
		
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $permitId->id()});
	};

}

route_customerId_contracts_contractId_so_projects_projectId_permits_areas_buildings_buildingSinfoId_permitId_attachments_post();

######## ROUTE /:customerId/contracts/:contractId/so/permits ###########

options '/:customerId/contracts/:contractId/so/permits' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_post {
	post '/:customerId/contracts/:contractId/so/permits' => sub {
		
		my $api = vars->{api_art};
	
		my %query_params = defined request->params('query') ? request->params('query') : ();
	
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing param {param}", param => 'type'))
			unless defined $body{type};
		
		#metto default per noWait
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Param {param} can value only {values}", param => 'noWait', values => '0|1'))
			if (defined $query_params{noWait} && $query_params{noWait} !~/^(0|1)$/);
		$query_params{noWait} = 0
			unless exists $query_params{noWait};

		my $coll;
		if ($body{type} eq 'public'){
			$coll = vars->{collPermessoLavori};
		} elsif ($body{type} eq 'private'){
			$coll = vars->{collPermessoBuilding};
		} else {
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => __x("Unknown type {type}", type => $body{type}));
		}
		
		my %create_params = %body;
		
		$create_params{"customerId"}	= param('customerId');
		$create_params{"contractId"}	= param('contractId');
		
		# NB: i campi popId e ringId sono in base32
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
			delete $create_params{attachments};
		}
		
		my $permesso= $coll->crea( %create_params );
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless ( $permesso );
		
		my $remap_results = ref($permesso) eq 'ARRAY' ? [map{API::ART::REST::remap_activity($api,$_)} @{$permesso}] : API::ART::REST::remap_activity($api,$permesso);
		
		API::ART::REST::do_api_save( $api );
		
		# imposto uno sleep dinamico dopo il save e prima di dare la response per permettere al demone
		# su core di recepire la creazione dell'attività.
		if (!$query_params{noWait}){
			my $sleepTime = 5;
			if (ref ($remap_results) eq 'ARRAY'){
				$sleepTime += ((scalar @{$remap_results})-1)/2;
			}
			sleep($sleepTime);
		}
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);

	};
}
route_customerId_contracts_contractId_so_permits_post();


sub route_customerId_contracts_contractId_so_permits_get {
	get '/:customerId/contracts/:contractId/so/permits' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# imposto filtro in modo che recuperi solo le attività legate al progetto
		$query_params{sp_customerId_equal} = param('customerId');
		$query_params{sp_contractId_equal} = param('contractId');
		$query_params{showOnlyWithVisibility} = 1;
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
		
	};
}
route_customerId_contracts_contractId_so_permits_get();

######## ROUTE /:customerId/contracts/:contractId/so/permits/fibercop ###########

options '/:customerId/contracts/:contractId/so/permits/fibercop' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_fibercop_get {
	get '/:customerId/contracts/:contractId/so/permits/fibercop' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoworks = vars->{wpsoworks};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();

		for my $p ('ap_targetAsset_equal', 'ap_ref00_equal', 'type_equal'){
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {param}", param => $p))
				unless defined ($query_params{$p});
		}

		if (defined $query_params{sp_assetId_in} && !ref $query_params{sp_assetId_in}){
			$query_params{sp_assetId_in} = [$query_params{sp_assetId_in}];
		}

		my $sql;
		my $prepare_name;
		if ($query_params{type_equal} eq 'PERMESSO_BUILDING'){
			$sql = '
				select pt.id_attivita "id",
					pt."maker",
					pt."teamId",
					pt."teamName",
					pt."subContractCode",
					pt."subContractName",
					att.nome_tipo_Attivita "type",
					att.in_stato_finale "isClosed",
					att.stato_corrente "status"
					,dts3.valore "assetId"
				from pt_PERMESSO_BUILDING pt
					join v_attivita att on att.id = pt.id_attivita
					join permission_sistemi ps on ps.id_sistema = att.id_sistema and ps.id_gruppo_abilitato in ('.join (',', map {'?'} @{$api->user->groups()}).')
					join v_dt_sistemi dts on dts.id_Sistema = att.id_sistema and dts.nome = \'customerId\'
					join v_dt_sistemi dts2 on dts2.id_Sistema = att.id_sistema and dts2.nome = \'contractId\'
					join v_dt_sistemi dts3 on dts3.id_Sistema = att.id_sistema and dts3.nome = \'assetId\'
				where dts.valore = ?
					and dts2.valore = ?
					and pt."ref00" = ?
					and pt."targetAsset" = ?
				'.(defined $query_params{sp_assetId_in} ? ' and dts3.valore in ('.join (',', map {'?'} @{$query_params{sp_assetId_in}}).')' : '').'
				order by pt.id_attivita desc
			';
			$prepare_name = '__permits_fibercop__buil';
		} else {
			$sql = '
				select pt.id_attivita "id",
					pt."maker",
					pt."teamId",
					pt."teamName",
					pt."subContractCode",
					pt."subContractName",
					att.nome_tipo_Attivita "type",
					att.in_stato_finale "isClosed",
					att.stato_corrente "status"
					,pt."projectId"
				from pt_PERMESSO_LAVORI pt
					join v_attivita att on att.id = pt.id_attivita
					join permission_sistemi ps on ps.id_sistema = att.id_sistema and ps.id_gruppo_abilitato in ('.join (',', map {'?'} @{$api->user->groups()}).')
					join v_dt_sistemi dts on dts.id_Sistema = att.id_sistema and dts.nome = \'customerId\'
					join v_dt_sistemi dts2 on dts2.id_Sistema = att.id_sistema and dts2.nome = \'contractId\'
				where dts.valore = ?
					and dts2.valore = ?
					and pt."ref00" = ?
					and pt."targetAsset" = ?
				'.(defined $query_params{sp_assetId_in} ? ' and dts3.valore in ('.join (',', map {'?'} @{$query_params{sp_assetId_in}}).')' : '').'
				order by pt.id_attivita desc
			';
			$prepare_name = '__permits_fibercop__lav';
		}

		my @bind_params = (
			@{$api->user->groups()},
			params->{'customerId'},
			params->{'contractId'},
			$query_params{ap_ref00_equal},
			$query_params{ap_targetAsset_equal},
		);
		if (defined $query_params{sp_assetId_in}){
			push @bind_params, @{$query_params{sp_assetId_in}};
		}
		$prepare_name .= '_'.(scalar @{$api->user->groups()}).'_'.(scalar @{$query_params{sp_assetId_in}||[]});

		my $prepare = $api->_create_prepare($prepare_name, $sql);

		my $result = $prepare->fetchall_hashref(@bind_params);

		my $ret = [];

		for my $r (@{$result}){
			my @found = grep {$r->{id} eq $_->{id}} @{$ret};
			if (scalar @found){
				push @{$found[0]->{system}->{properties}->{assetId}}, $r->{assetId};
				next;
			}
			my $tmp = {
				id => $r->{id},
				info => {
					type => $r->{type},
					isClosed => $r->{isClosed} eq 'N' ? $JSON::false : $JSON::true,
					status => $r->{status},
				},
				properties => {
					maker => $r->{maker},
					subContractCode => $r->{subContractCode},
					subContractName => $r->{subContractName},
					teamName => $r->{teamName},
					teamId => $r->{teamId},
				},
				system => {
					properties => {
						assetId => [$r->{assetId}]
					}
				}
			};
			if (exists $r->{assetId}){
				$tmp->{system}->{properties}->{assetId} = [$r->{assetId}];
			} elsif (exists $r->{projectId}){
				$tmp->{properties}->{projectId} = $r->{projectId};
			}
			if ($tmp->{info}->{isClosed}){
				$tmp->{availableActions} = [];
			} else {
				my $activity = API::ART::Activity::Factory->new(ART => $api, ID => $r->{id});

				my $dump = $activity->dump(
					EXCLUDE_INFO => 1,
					EXCLUDE_HISTORY => 1,
					EXCLUDE_PROPERTIES => 1,
					EXCLUDE_AVAILABLE_ACTIONS => 0,
					SYSTEM => {
						EXCLUDE_ALL => 1
					}
				);

				$tmp->{availableActions} = $dump->{availableActions};
			}
			push @{$ret}, $tmp;
		}

		return API::ART::REST::send_ok(CODE => HTTP_OK, MSG => $ret);
		
	};
}
route_customerId_contracts_contractId_so_permits_fibercop_get();

######## ROUTE /:customerId/contracts/:contractId/so/permits/dashboards/:blockId ###########

options '/:customerId/contracts/:contractId/so/permits/dashboards/:blockId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_dashboards_blockId_get {

	get '/:customerId/contracts/:contractId/so/permits/dashboards/:blockId' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $block_id = params->{'blockId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $block_objects = vars->{"remap-blocks"};
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists ($block_objects->{$block_id});
		
		my $block_object = $block_objects->{$block_id};
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		$findParams{sp_contractId_equal} 	= $contract;
		
		# default ordinamento non sovrascrivibile al momento
		$findParams{sort} = "status";
		
		my %uri_params = (
			OBJECT_TYPE	=> 'activities'
			,TYPE		=> $block_object->{ACTIVITY_TYPE_NAME}
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customerId_contracts_contractId_so_permits_dashboards_blockId_get();



######## ROUTE /:customerId/contracts/:contractId/so/permits/dashboards/:blockId/contexts/:contextId/data ###########

options '/:customerId/contracts/:contractId/so/permits/dashboards/:blockId/contexts/:contextId/data' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_dashboards_blockId_contexts_contextId_data_get {

	get '/:customerId/contracts/:contractId/so/permits/dashboards/:blockId/contexts/:contextId/data' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $block_id = params->{'blockId'};
		my $context_id = params->{'contextId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $block_objects = vars->{"remap-blocks"};
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists ($block_objects->{$block_id});
		
		my $block_object = $block_objects->{$block_id};
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		$findParams{sp_contractId_equal} 	= $contract;
		
		my %uri_params = (
			OBJECT_TYPE	=> 'activities'
			,TYPE		=> $block_object->{ACTIVITY_TYPE_NAME}
			,CONTEXT	=> $context_id
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_CONTEXT_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customerId_contracts_contractId_so_permits_dashboards_blockId_contexts_contextId_data_get();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_get {

	get '/:customerId/contracts/:contractId/so/permits/:permitId' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound});
	};

};

route_customerId_contracts_contractId_so_permits_permitId_get();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/activityProperties ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/activityProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_activityProperties_get {

	get '/:customerId/contracts/:contractId/so/permits/:permitId/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_activityProperties_get();

sub route_customerId_contracts_contractId_so_permits_permitId_activityProperties_put{

	put '/:customerId/contracts/:contractId/so/permits/:permitId/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_activityProperties_put(BODY => \%body, URI_PARAMS => {ID => $permitIdFound});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_activityProperties_put();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/attachments ###########

sub route_customerId_contracts_contractId_so_permits_permitId_attachments_options {
	options '/:customerId/contracts/:contractId/so/permits/:permitId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_attachments_options();

sub route_customerId_contracts_contractId_so_permits_permitId_attachments_get {
	
	get '/:customerId/contracts/:contractId/so/permits/:permitId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound});
	};
};

route_customerId_contracts_contractId_so_permits_permitId_attachments_get;

sub route_customerId_contracts_contractId_so_permits_permitId_attachments_post {
	post '/:customerId/contracts/:contractId/so/permits/:permitId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %body = request->params('body');
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $permitIdFound});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_attachments_post();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/canDoAction/:ACTION ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/canDoAction/:ACTION' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_canDoAction_ACTION_get {

	get '/:customerId/contracts/:contractId/so/permits/:permitId/canDoAction/:ACTION' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_canDoAction_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound, ACTION => params->{'ACTION'}});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_canDoAction_ACTION_get();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/destUsers/:ACTION ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/destUsers/:ACTION' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_destUsers_ACTION_get {

	get '/:customerId/contracts/:contractId/so/permits/:permitId/destUsers/:ACTION' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_destUsers_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound, ACTION => param('ACTION')});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_destUsers_ACTION_get();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/hierarchy ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/hierarchy' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_hierarchy_get {

	get '/:customerId/contracts/:contractId/so/permits/:permitId/hierarchy' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_hierarchy_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_hierarchy_get();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/history ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_history_post {
	
	post '/:customerId/contracts/:contractId/so/permits/:permitId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $permitIdFound});
	};
};

route_customerId_contracts_contractId_so_permits_permitId_history_post();

sub route_customerId_contracts_contractId_so_permits_permitId_history_get {
	
	get '/:customerId/contracts/:contractId/so/permits/:permitId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound});
	};
};

route_customerId_contracts_contractId_so_permits_permitId_history_get;

######## ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_permits_permitId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $permitIdFound ? $permitIdFound : params->{'permitId'}), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_permits_permitId_history_transitionId_attachments_sequence_get {
	get '/:customerId/contracts/:contractId/so/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound, TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
};
route_customerId_contracts_contractId_so_permits_permitId_history_transitionId_attachments_sequence_get();

sub route_customerId_contracts_contractId_so_permits_permitId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/permits/:permitId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitIdFound, TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_history_transitionId_attachments_sequence_delete();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/lock ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/lock' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'DELETE', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_lock_del {

	del '/:customerId/contracts/:contractId/so/permits/:permitId/lock' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_lock_del(URI_PARAMS => {ID => $permitIdFound});
	};

}
route_customerId_contracts_contractId_so_permits_permitId_lock_del();

sub route_customerId_contracts_contractId_so_permits_permitId_lock_put{

	put '/:customerId/contracts/:contractId/so/permits/:permitId/lock' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my $permitIdFound = eval{_get_permesso_fc($customer, $contract, $permitId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitIdFound;
		
		return API::ART::REST::handler_activities_ID_lock_put(URI_PARAMS => {ID => $permitIdFound});
	};

}
route_customerId_contracts_contractId_so_permits_permitId_lock_put();

######### ROUTE /:customerId/contracts/:contractId/so/permits/:permitId/systemProperties ###########

options '/:customerId/contracts/:contractId/so/permits/:permitId/systemProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_permits_permitId_systemProperties_get {

	get '/:customerId/contracts/:contractId/so/permits/:permitId/systemProperties' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $permitId = params->{'permitId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $permitFound = eval{_get_permesso_fc($customer, $contract, $permitId, {OBJECT => 1});};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $permitFound;
		
		return API::ART::REST::handler_systems_ID_systemProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $permitFound->system_id()});
	};

}

route_customerId_contracts_contractId_so_permits_permitId_systemProperties_get();

######## ROUTE /:customerId/contracts/:contractId/so/sites ###########

options '/:customerId/contracts/:contractId/so/sites' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_get {
	get '/:customerId/contracts/:contractId/so/sites' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		# imposto filtro in modo che recuperi solo le attività legate al progetto
		$query_params{sp_customerId_equal} = param('customerId');
		$query_params{sp_contractId_equal} = param('contractId');
		$query_params{showOnlyWithVisibility} = 1;
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%query_params);
		
	};
}
route_customerId_contracts_contractId_so_sites_get();

options '/:customerId/contracts/:contractId/so/sites' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_post {
	post '/:customerId/contracts/:contractId/so/sites' => sub {
		
		my $api = vars->{api_art};
	
		my %query_params = defined request->params('query') ? request->params('query') : ();
	
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $coll = vars->{collSites};
		
		my %create_params = %body;
		
		$create_params{"customerId"}	= param('customerId');
		$create_params{"contractId"}	= param('contractId');
		
		# NB: i campi popId e ringId sono in base32
		
		if (defined $body{attachments}){
			
			$create_params{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachment param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$create_params{ATTACHMENTS} = eval{_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $create_params{ATTACHMENTS};
			
			delete $create_params{attachments};
		}

		my $permesso;
		#cerco sito
		my $sites = $coll->cerca(
			customerId  => param('customerId'),
			contractId  => param('contractId'),
			siteId      => $body{siteId}
		);

		my $lc06 = $sites && @$sites ? $sites->[0] : undef;
		
		if ($lc06 && $lc06->is_active()){
			my $step_prop = {};
			my $step_params ={
				ACTION     => 'AGGIORNAMENTO_DATI',
				PROPERTIES => $step_prop,
			};

			for (
				"regionId"
				,"cityId"
				,"centralCLLI"
				,"central"
				,"FOL"

			){
				$step_prop->{$_} = $body{$_} if defined $body{$_};
			}

			for (
				"primaryWorks"
				,"restorationWorks"
			){
				if (defined $body{$_}){
					$step_prop->{$_} = $body{$_} eq 'Y' ? 1 : 0;
				}
			}
			
			if (defined $body{__STATO__}) {
				my $status_list = $api->enum_activity_status(ACTIVITY_TYPE_NAME => 'LC06');

				# Controllo se lo stato è presente nel flusso LC06
				if (exists $status_list->{$body{__STATO__}} && $body{__STATO__} ne '___ANY_STATUS___') {
					$step_params->{VIRTUAL} = 1;
					$step_params->{DEST_STATUS} = $body{__STATO__};
					if ($body{__STATO__} eq 'ANNULLATA') {	
						$step_params->{ACTION} = 'ANNULLAMENTO';
						delete $step_params->{VIRTUAL};
   						delete $step_params->{DEST_STATUS};
						delete $step_params->{PROPERTIES};					
					}
				}else {
					return API::ART::REST::send_ko(
						CODE      => HTTP_BAD_REQUEST,
						ERROR_MSG => __x("Activity status: {stato} not valid", stato => $body{__STATO__})
					);
				}
			}
			# se esiste il sito lo aggiorno
			if ($lc06->step(%$step_params)) {
				$permesso = $lc06;
			}
			
		} else {
			# altrimenti creo nuovo sito
			eval {
				$permesso = $coll->crea(%create_params);
			};
		}

		if ($@ || !$permesso) {
			my $error_msg = $@ 
				? "Error during operation: $@" 
				: $api->last_error();
			return API::ART::REST::send_ko(
				CODE      => HTTP_BAD_REQUEST,
				ERROR_MSG => $error_msg
			);
		}

		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless ( $permesso );
		my $remap_results = ref($permesso) eq 'ARRAY' ? [map{API::ART::REST::remap_activity($api,$_)} @{$permesso}] : API::ART::REST::remap_activity($api,$permesso);
		
		API::ART::REST::do_api_save( $api );
		
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap_results);
	
	};
}
route_customerId_contracts_contractId_so_sites_post();

######## ROUTE /:customerId/contracts/:contractId/so/sites/lookups/siteIdList ###########

options '/:customerId/contracts/:contractId/so/sites/lookups/siteIdList' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_lookups_siteIdList_get {

	get '/:customerId/contracts/:contractId/so/sites/lookups/siteIdList' => sub {
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $api = vars->{api_art};

		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Missing mandatory param {paramname}", paramname => "q"))
			unless defined $query_params{'q'};

		my $collActivity = vars->{collActivity};
		
		my $acts = $collActivity->find_object(
			ACTIVITY_TYPE_NAME => ['LC06']
			, SHOW_ONLY_WITH_VISIBILITY => 1
			, SYSTEM_PROPERTIES_EQUAL => {
				customerId	=> params->{'customerId'},
				contractId	=> params->{'contractId'},
			}
			, SYSTEM_PROPERTIES_LIKE => {
				siteId	=> '%'.uc($query_params{'q'}).'%',
			}
			, ACTIVE => 1
		);

		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $api->last_error())
			unless defined $acts;

		my $res = [];

		for my $act (@{$acts}){
			push @{$res}, $act->activity_property('siteId');
		}

		return API::ART::REST::send_ok(MSG => $res);
	};
}

route_customerId_contracts_contractId_so_sites_lookups_siteIdList_get();

######## ROUTE /:customerId/contracts/:contractId/so/sites/dashboards/:blockId ###########

options '/:customerId/contracts/:contractId/so/sites/dashboards/:blockId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_dashboards_blockId_get {

	get '/:customerId/contracts/:contractId/so/sites/dashboards/:blockId' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $block_id = params->{'blockId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $block_objects = vars->{"remap-blocks"};
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists ($block_objects->{$block_id});
		
		my $block_object = $block_objects->{$block_id};
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		$findParams{sp_contractId_equal} 	= $contract;
		
		# default ordinamento non sovrascrivibile al momento
		$findParams{sort} = "status";
		
		my %uri_params = (
			OBJECT_TYPE	=> 'activities'
			,TYPE		=> $block_object->{ACTIVITY_TYPE_NAME}
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customerId_contracts_contractId_so_sites_dashboards_blockId_get();



######## ROUTE /:customerId/contracts/:contractId/so/sites/dashboards/:blockId/contexts/:contextId/data ###########

options '/:customerId/contracts/:contractId/so/sites/dashboards/:blockId/contexts/:contextId/data' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_dashboards_blockId_contexts_contextId_data_get {

	get '/:customerId/contracts/:contractId/so/sites/dashboards/:blockId/contexts/:contextId/data' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $block_id = params->{'blockId'};
		my $context_id = params->{'contextId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $block_objects = vars->{"remap-blocks"};
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists ($block_objects->{$block_id});
		
		my $block_object = $block_objects->{$block_id};
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		$findParams{sp_contractId_equal} 	= $contract;
		
		my %uri_params = (
			OBJECT_TYPE	=> 'activities'
			,TYPE		=> $block_object->{ACTIVITY_TYPE_NAME}
			,CONTEXT	=> $context_id
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_CONTEXT_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customerId_contracts_contractId_so_sites_dashboards_blockId_contexts_contextId_data_get();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_get {

	get '/:customerId/contracts/:contractId/so/sites/:siteId' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = eval{_get_sito($customer, $contract, $siteId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound});
	};

};

route_customerId_contracts_contractId_so_sites_permitId_get();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/activityProperties ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/activityProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_activityProperties_get {

	get '/:customerId/contracts/:contractId/so/sites/:siteId/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = eval{_get_sito($customer, $contract, $siteId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_activityProperties_get();

sub route_customerId_contracts_contractId_so_sites_permitId_activityProperties_put{

	put '/:customerId/contracts/:contractId/so/sites/:siteId/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my $siteIdFound = eval{_get_sito($customer, $contract, $siteId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_activityProperties_put(BODY => \%body, URI_PARAMS => {ID => $siteIdFound});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_activityProperties_put();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/attachments ###########

sub route_customerId_contracts_contractId_so_sites_permitId_attachments_options {
	options '/:customerId/contracts/:contractId/so/sites/:siteId/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = eval{_get_sito($customer, $contract, $siteId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_attachments_options();

sub route_customerId_contracts_contractId_so_sites_permitId_attachments_get {
	
	get '/:customerId/contracts/:contractId/so/sites/:siteId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = eval{_get_sito($customer, $contract, $siteId);};
		return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
			if $@;
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound});
	};
};

route_customerId_contracts_contractId_so_sites_permitId_attachments_get;

sub route_customerId_contracts_contractId_so_sites_permitId_attachments_post {
	post '/:customerId/contracts/:contractId/so/sites/:siteId/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %body = request->params('body');
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $siteIdFound});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_attachments_post();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/canDoAction/:ACTION ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/canDoAction/:ACTION' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_canDoAction_ACTION_get {

	get '/:customerId/contracts/:contractId/so/sites/:siteId/canDoAction/:ACTION' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_canDoAction_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound, ACTION => params->{'ACTION'}});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_canDoAction_ACTION_get();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/destUsers/:ACTION ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/destUsers/:ACTION' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_destUsers_ACTION_get {

	get '/:customerId/contracts/:contractId/so/sites/:siteId/destUsers/:ACTION' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_destUsers_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound, ACTION => param('ACTION')});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_destUsers_ACTION_get();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/hierarchy ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/hierarchy' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_hierarchy_get {

	get '/:customerId/contracts/:contractId/so/sites/:siteId/hierarchy' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_hierarchy_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_hierarchy_get();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/history ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'POST', 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_history_post {
	
	post '/:customerId/contracts/:contractId/so/sites/:siteId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $siteIdFound});
	};
};

route_customerId_contracts_contractId_so_sites_permitId_history_post();

sub route_customerId_contracts_contractId_so_sites_permitId_history_get {
	
	get '/:customerId/contracts/:contractId/so/sites/:siteId/history' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
			
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound});
	};
};

route_customerId_contracts_contractId_so_sites_permitId_history_get;

######## ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/history/:transitionId/attachments/:sequence ###########

sub route_customerId_contracts_contractId_so_sites_permitId_history_transitionId_attachments_sequence_options {
	options '/:customerId/contracts/:contractId/so/sites/:siteId/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'DELETE', 'OPTIONS' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);

		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $siteIdFound ? $siteIdFound : params->{'siteId'}), TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_history_transitionId_attachments_sequence_options();

sub route_customerId_contracts_contractId_so_sites_permitId_history_transitionId_attachments_sequence_get {
	get '/:customerId/contracts/:contractId/so/sites/:siteId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound, TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};
};
route_customerId_contracts_contractId_so_sites_permitId_history_transitionId_attachments_sequence_get();

sub route_customerId_contracts_contractId_so_sites_permitId_history_transitionId_attachments_sequence_delete {
	
	del '/:customerId/contracts/:contractId/so/sites/:siteId/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		# se lo trovo la passo all'API::ART::REST per avere tutte le funzionalità
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteIdFound, TRANSITION_ID => param('transitionId'), SEQUENCE => param('sequence')});
	};

}

route_customerId_contracts_contractId_so_sites_permitId_history_transitionId_attachments_sequence_delete();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/lock ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/lock' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'DELETE', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_permitId_lock_del {

	del '/:customerId/contracts/:contractId/so/sites/:siteId/lock' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_lock_del(URI_PARAMS => {ID => $siteIdFound});
	};

}
route_customerId_contracts_contractId_so_sites_permitId_lock_del();

sub route_customerId_contracts_contractId_so_sites_permitId_lock_put{

	put '/:customerId/contracts/:contractId/so/sites/:siteId/lock' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my $siteIdFound = _get_sito($customer, $contract, $siteId);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteIdFound;
		
		return API::ART::REST::handler_activities_ID_lock_put(URI_PARAMS => {ID => $siteIdFound});
	};

}
route_customerId_contracts_contractId_so_sites_permitId_lock_put();

######### ROUTE /:customerId/contracts/:contractId/so/sites/:siteId/systemProperties ###########

options '/:customerId/contracts/:contractId/so/sites/:siteId/systemProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customerId_contracts_contractId_so_sites_siteId_systemProperties_get {

	get '/:customerId/contracts/:contractId/so/sites/:siteId/systemProperties' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'customerId'};
		my $contract = params->{'contractId'};
		my $siteId = params->{'siteId'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $siteFound = _get_sito($customer, $contract, $siteId, {OBJECT => 1});
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $siteFound;
		
		return API::ART::REST::handler_systems_ID_systemProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $siteFound->system_id});
	};

}

route_customerId_contracts_contractId_so_sites_siteId_systemProperties_get();
1;
