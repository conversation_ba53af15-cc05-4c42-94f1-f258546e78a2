package WebService::WSART::WPSOAPWSTT001;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use HTTP::Status qw(:constants :is status_message);
use JSON;

use File::Copy;
use MIME::Base32;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use Dancer2 appname => 'API::ART::REST';

prefix defined $ENV{WPSOAPWS_WS_ROUTES_PREFIX} ? $ENV{WPSOAPWS_WS_ROUTES_PREFIX} : '/wpso/ap';

use SIRTI::Reports;

use WPSOAP::Collection::Activity::TT001;

############ HOOKS ################

### NOTA: l'hook viene eseguito sempre anche se fuori dal prefix

hook before => sub {
	
	debug __PACKAGE__.": sono in hook before";
	
	my $isOptionsNotPreflightAttach = 0;

	if (
		API::ART::REST::is_options() &&
		!API::ART::REST::is_preflight() &&
		(
			request->path() =~ m{/customers/(.*)/tt001s/\d+/history/\d{14}/attachments/\d+}
			||
			request->path() =~ m{/customers/(.*)/tt001s/\d+/attachments}
		)
	){
		$isOptionsNotPreflightAttach = 1;
	}

	unless ($isOptionsNotPreflightAttach){
		return
			if API::ART::REST::is_options();
	}
	
	# se è la rotta di login non devo fare nulla
	return if API::ART::REST::is_login_route();
	
	my $collTT001;
	
	eval{
		$collTT001 = WPSOAP::Collection::Activity::TT001->new(ART => vars->{api_art});
	};
	
	if ($@){
		error($@);
		status(HTTP_INTERNAL_SERVER_ERROR);
		$ENV{_WSART_UUID} = vars->{uuid};
		halt();
	}

	var collTT001 => $collTT001;

};

############ PRIVATE ################

sub _get_activity_for_tt001{
	
	my ($customer, $tt001Id) = @_;

	my $api = vars->{api_art};
	
	my %findParams = (
		ACTIVITY_TYPE_NAME_IN => ['TT001']
		, SYSTEM_TYPE_NAME_IN => ['TT001']
		,SYSTEM_PROPERTIES_EQUAL => {
			customerId => $customer,
		}
		,ID_EQUAL => $tt001Id
		,LIMIT => 1 #per efficienza
		,SHOW_ONLY_WITH_VISIBILITY => 1
	);
	
	my $activities = vars->{collActivity}->find_object(%findParams);
	
	$api->last_error(__("Activity not found"))
		&& return undef
			unless scalar @{$activities};
	
	return $activities->[0];
}

####### ROUTES ###########

######## ROUTE /customers/:CUSTOMER/tt001s ###########

options '/customers/:CUSTOMER/tt001s' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT', 'POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_get {

	get '/customers/:CUSTOMER/tt001s' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my $customer = params->{'CUSTOMER'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my %findParams = %query_params;
		
		$findParams{type_equal} = 'TT001';
			
		$findParams{sp_customerId_equal} 	= $customer;
		
		return API::ART::REST::handler_activities_get(QUERY_PARAMS => \%findParams);
	};
}

route_customers_customer_tt001s_get();

sub route_customers_customer_tt001s_post {

	post '/customers/:CUSTOMER/tt001s' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %body = request->params('body');
		
		my $customer = params->{'CUSTOMER'};
		
		$body{customerId} = $customer;
		
		my $collTT001 = vars->{collTT001};
		
		if (defined $body{attachments}){
			
			$body{ATTACHMENTS} = undef;
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, INTERNAL_ERROR_MSG => "attachments param must be an array")
				if (ref ($body{attachments}) ne 'ARRAY');
			
			$body{ATTACHMENTS} = eval{WebService::WSART::WPSOAPWS::_manage_attachments($body{attachments})};
			
			return API::ART::REST::send_ko(INTERNAL_ERROR_MSG => $@)
				if ($@);
			
			return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
				unless defined $body{ATTACHMENTS};
			delete $body{attachments};
		}

		my $tt001 = $collTT001->crea(%body);
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => $api->last_error())
			unless defined $tt001;
		
		my $remap = API::ART::REST::remap_activity($api,$tt001);
		API::ART::REST::do_api_save( $api );
		return API::ART::REST::send_ok(CODE => HTTP_CREATED, MSG => $remap);
		
	};

}
route_customers_customer_tt001s_post();

######## ROUTE /customers/:CUSTOMER/tt001s/dashboards/:BLOCK_ID ###########

options '/customers/:CUSTOMER/tt001s/dashboards/:BLOCK_ID' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_dashboards_block_id_get {

	get '/customers/:CUSTOMER/tt001s/dashboards/:BLOCK_ID' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my $customer = params->{'CUSTOMER'};
		my $block_id = params->{'BLOCK_ID'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists (vars->{"remap-blocks"}->{$block_id});
		
		# default ordinamento non sovrascrivibile al momento
		$findParams{sort} = "status";
		
		my %uri_params = (
			OBJECT_TYPE => 'activities'
			,TYPE => vars->{"remap-blocks"}->{$block_id}
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customers_customer_tt001s_dashboards_block_id_get();

######## ROUTE /customers/:CUSTOMER/tt001s/dashboards/:BLOCK_ID/contexts/:CONTEXT_ID/data ###########

options '/customers/:CUSTOMER/tt001s/dashboards/:BLOCK_ID/contexts/:CONTEXT_ID/data' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_dashboards_block_id_contexts_context_id_data_get {

	get '/customers/:CUSTOMER/tt001s/dashboards/:BLOCK_ID/contexts/:CONTEXT_ID/data' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my $customer = params->{'CUSTOMER'};
		
		my $block_id = params->{'BLOCK_ID'};
		my $context_id = params->{'CONTEXT_ID'};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my %findParams = %query_params;
		
		$findParams{sp_customerId_equal} 	= $customer;	
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __x("Unknown BLOCK_ID {value}", value => $block_id))
			unless exists (vars->{"remap-blocks"}->{$block_id});
			
		my %uri_params = (
			OBJECT_TYPE	=> 'activities'
			,TYPE		=> vars->{"remap-blocks"}->{$block_id}
			,CONTEXT	=> $context_id
		);
		
		return API::ART::REST::handler_dashboards_OBJECT_TYPE_TYPE_CONTEXT_get(URI_PARAMS => \%uri_params, QUERY_PARAMS => \%findParams);
	};
}

route_customers_customer_tt001s_dashboards_block_id_contexts_context_id_data_get();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id' => sub {
		
		my $api = vars->{api_art};
		
		my $wpsoap = vars->{wpsoap};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer, $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_get();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/activityProperties ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/activityProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_activityProperties_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_activityProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_activityProperties_get();

sub route_customers_customer_tt001s_tt001Id_activityProperties_put{

	put '/customers/:CUSTOMER/tt001s/:tt001Id/activityProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_activityProperties_put(BODY => \%body, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_activityProperties_put();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/attachments ###########

sub route_customers_customer_tt001s_tt001Id_attachments_options {
	options '/customers/:CUSTOMER/tt001s/:tt001Id/attachments' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'POST' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_attachments_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_attachments_options();

sub route_customers_customer_tt001s_tt001Id_attachments_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_attachments_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_attachments_get();

sub route_customers_customer_tt001s_tt001Id_attachments_post {
	post '/customers/:CUSTOMER/tt001s/:tt001Id/attachments' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_attachments_post(BODY => \%body, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_attachments_post();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/canDoAction/:action ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/canDoAction/:action' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_canDoAction_action_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/canDoAction/:action' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		my $action = params->{'action'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_canDoAction_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id(), ACTION => $action});
	};

}

route_customers_customer_tt001s_tt001Id_canDoAction_action_get();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/destUsers/:action ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/destUsers/:action' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_destUsers_action_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/destUsers/:action' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		my $action = params->{'action'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_destUsers_ACTION_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id(), ACTION => $action});
	};

}

route_customers_customer_tt001s_tt001Id_destUsers_action_get();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/hierarchy ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/hierarchy' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_hierarchy_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/hierarchy' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_hierarchy_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_hierarchy_get();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/history ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/history' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'POST' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_history_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/history' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_history_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_history_get();

sub route_customers_customer_tt001s_tt001Id_history_post{

	post '/customers/:CUSTOMER/tt001s/:tt001Id/history' => sub {
		
		my $api = vars->{api_art};
		
		my %body = request->params('body');
		
		return API::ART::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => __("Invalid JSON"))
			unless (keys %body);
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_history_post(BODY => \%body, URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_history_post();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/history/:transitionId/attachments/:sequence ###########

options '' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_history_transitionId_attachments_sequence_options {
	options '/customers/:CUSTOMER/tt001s/:tt001Id/history/:transitionId/attachments/:sequence' => sub {
		if (API::ART::REST::is_preflight()){
			API::ART::REST::handle_cors_request( METHODS => [ 'GET', 'OPTIONS', 'DELETE' ] );
			return API::ART::REST::send_ok(IGNORE_SESSION => 1);
		}
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		my $transitionId = params->{'transitionId'};
		my $sequence = params->{'sequence'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_options(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => (defined $activity ? $activity->id() : $tt001Id), TRANSITION_ID => $transitionId, SEQUENCE => $sequence});
	};

}

route_customers_customer_tt001s_tt001Id_history_transitionId_attachments_sequence_options();

sub route_customers_customer_tt001s_tt001Id_history_transitionId_attachments_sequence_get {
	
	get '/customers/:CUSTOMER/tt001s/:tt001Id/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		my $transitionId = params->{'transitionId'};
		my $sequence = params->{'sequence'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id(), TRANSITION_ID => $transitionId, SEQUENCE => $sequence});
	};

}

route_customers_customer_tt001s_tt001Id_history_transitionId_attachments_sequence_get();

sub route_customers_customer_tt001s_tt001Id_history_transitionId_attachments_sequence_delete {
	
	del '/customers/:CUSTOMER/tt001s/:tt001Id/history/:transitionId/attachments/:sequence' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		my $transitionId = params->{'transitionId'};
		my $sequence = params->{'sequence'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_history_TRANSITION_ID_attachments_SEQUENCE_delete(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->id(), TRANSITION_ID => $transitionId, SEQUENCE => $sequence});
	};

}

route_customers_customer_tt001s_tt001Id_history_transitionId_attachments_sequence_delete();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/lock ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/lock' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'DELETE', 'PUT' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_lock_del {

	del '/customers/:CUSTOMER/tt001s/:tt001Id/lock' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_lock_del(URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_lock_del();

sub route_customers_customer_tt001s_tt001Id_lock_put{

	put '/customers/:CUSTOMER/tt001s/:tt001Id/lock' => sub {
		
		my $api = vars->{api_art};
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_activities_ID_lock_put(URI_PARAMS => {ID => $activity->id()});
	};

}

route_customers_customer_tt001s_tt001Id_lock_put();

######### ROUTE /customers/:CUSTOMER/tt001s/:tt001Id/systemProperties ###########

options '/customers/:CUSTOMER/tt001s/:tt001Id/systemProperties' => sub { API::ART::REST::handle_cors_request( METHODS => [ 'GET' ] ); return API::ART::REST::send_ok(IGNORE_SESSION => 1); };

sub route_customers_customer_tt001s_tt001Id_systemProperties_get {

	get '/customers/:CUSTOMER/tt001s/:tt001Id/systemProperties' => sub {
		
		my $api = vars->{api_art};
		
		my %query_params = defined request->params('query') ? request->params('query') : ();
		
		my $customer = params->{'CUSTOMER'};
		
		my $tt001Id = params->{'tt001Id'};
		
		my $activity = _get_activity_for_tt001($customer,  $tt001Id);
		
		return API::ART::REST::send_ko(CODE => HTTP_NOT_FOUND, ERROR_MSG => $api->last_error())
			unless defined $activity;
		
		return API::ART::REST::handler_systems_ID_systemProperties_get(QUERY_PARAMS => \%query_params, URI_PARAMS => {ID => $activity->system_id()});
	};

}

route_customers_customer_tt001s_tt001Id_systemProperties_get();

1;
