package WebService::WS::BOTGWWS;

use strict;
use warnings FATAL => 'all';
use Carp;
use utf8;
use HTTP::Status qw(:constants :is status_message);
use File::Scan::ClamAV;

use API::ART;

sub _get_textdomain { my $textdomain = __PACKAGE__; $textdomain =~ s/:/_/g; return "LIB__".$textdomain; }
use Locale::TextDomain (_get_textdomain, "$ENV{ROOT}/share/locale" );

use Dancer2 appname => 'SIRTI::REST';

set serializer => 'JSON';

my $prefix = defined $ENV{BOTGWWS_WS_ROUTES_PREFIX} ? $ENV{BOTGWWS_WS_ROUTES_PREFIX} : '/botgwws/botgw';
$prefix =~ s#/+$##;

prefix $prefix;

############ HOOKS ################

### NOTA: l'hook viene eseguito sempre anche se fuori dal prefix

sub hook_before {}

hook before => sub {
	
	debug __PACKAGE__.": sono in hook before";
	
	my $api;
	
	eval {
		$api = API::ART->new (
			 ARTID => $ENV{BOTGWWS_ARTID},
			 USER => $ENV{BOTGWWS_SCRIPT_USER},
			 PASSWORD => $ENV{BOTGWWS_SCRIPT_PASSWORD},
		)
	};
	
	if ($@){
		use Data::Dumper;
		print STDERR Dumper $@;
		error('UUID '.vars->{uuid}.': '.$@);
		status(HTTP_INTERNAL_SERVER_ERROR);
		$ENV{_WSART_UUID} = vars->{uuid};
		halt();
	}

	var api => $api;
};

######## ROUTE /networks ###########

options '/files' => sub { SIRTI::REST::handle_cors_request( METHODS => [ 'POST' ] ); return SIRTI::REST::send_ok(); };
	
sub route_files_post {
	post '/files' => sub {
		
		my $api = vars->{api};

		my $shared_resources = $api->shared_resources();
		SIRTI::REST::send_ko(CODE => HTTP_INTERNAL_SERVER_ERROR, INTERNAL_ERROR_MSG => $api->last_error())
			unless defined $shared_resources;

		my $ids = [];
		for my $f ( upload('attachment') ) {
			
			if($ENV{CLAMAV_DAEMON_PORT}) {
				## virus scan ##
				debug "Scanning file ".$f->tempname." for virus";
				
				unless(chmod 0644, $f->tempname) {
					unlink $f->tempname;
					return SIRTI::REST::send_ko(INTERNAL_ERROR_MSG => "Unable to chmod: ".$!);
				}
				my $av = new File::Scan::ClamAV(port => $ENV{CLAMAV_DAEMON_PORT});
				my ($file, $virus) = $av->scan($f->tempname);
				if(defined $av->errstr()) {
					unlink $f->tempname;
					return SIRTI::REST::send_ko(INTERNAL_ERROR_MSG => "Unable to scan for virus: ".$av->errstr());
				}
				if(defined $virus) {
					unlink $f->tempname;
					return SIRTI::REST::send_ko(CODE => HTTP_BAD_REQUEST, ERROR_MSG => "Found virus ".$virus." on file ".$f->tempname);
				}
			}
			
			my $id = $shared_resources->create(ART => $api, PATH => $f->tempname);
			
			unless (defined $id){
				unlink $f->tempname;
				return SIRTI::REST::send_ko(CODE => HTTP_INTERNAL_SERVER_ERROR, MSG => $api->last_error());
			}
			push @$ids, $id;
			
			unlink $f->tempname;
		}

		my $res = {
			ids => $ids
		};

		return SIRTI::REST::send_ok(CODE => HTTP_CREATED, MSG => $res);
	};
}
route_files_post();

any qr{.*} => sub {
	SIRTI::REST::send_ko(CODE => HTTP_NOT_FOUND);
};

######## ROUTE / ###########

prefix '/';

any qr{.*} => sub {
	SIRTI::REST::send_ko(CODE => HTTP_NOT_FOUND);
};


1;
