set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE OR REPLACE FORCE EDITIONABLE VIEW V_TIM_FIBERCOP_PTE AS 
  select
    dts3.valore "Citta"
    ,dts4.valore "Centrale"
    ,dts2.valore "Armadio"
    ,pt."copperDistributor" "Distributore Rame"
    ,dts.valore PTE
    ,pt."CLLI"
    ,pt.ODS ID_ODS
    ,pt."WBELevel3" "WBE"
    ,pt."address" "Indirizzo"
    ,pt.ID_ATTIVITA
    ,vatt.stato_Corrente "STATO PTE"
    ,vatt.id_Sistema "ID_SISTEMA"
    ,case
        when dts15.valore = 'Team' then (select dts16.valore from core_art.v_Dt_sistemi dts16 where dts16.id_Sistema = Vatt.id_Sistema and dts16.nome in ('privatePermitsLATeamId'))
        when dts15.valore = 'Subcontract' then (select dts17.valore from core_art.v_Dt_sistemi dts17 where dts17.id_Sistema = Vatt.id_Sistema and dts17.nome in ('privatePermitsLASubContractCode'))
        else null
        end "APPALTO PERMESSO PRIV."
    ,dts14.valore "STATO PERMESSO PRIV."
    ,case
        when dts14.valore is not null then (select data_Esecuzione from (
            select max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_PERMESSO_BUILDING', 'CHIUSURA_PERMESSO_BUILDING_OK','CHIUSURA_PERMESSO_BUILDING_KO')
        ))
        else null
        end "DATA ULTIMA VAR. PERM. PRIV."
    ,case
        when dts16.valore = 'Team' then (select dts17.valore from core_art.v_Dt_sistemi dts17 where dts17.id_Sistema = Vatt.id_Sistema and dts17.nome in ('wSurveyLATeamId'))
        when dts16.valore = 'Subcontract' then (select dts18.valore from core_art.v_Dt_sistemi dts18 where dts18.id_Sistema = Vatt.id_Sistema and dts18.nome in ('wSurveyLASubContractCode'))
        else null
        end "APPALTO WALKIN/OUT"
    ,dts5.valore "STATO WALKIN/OUT"
    ,case
        when dts5.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='survey'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. WALKIN/OUT"
    ,case
        when dts20.valore = 'Team' then (select dts21.valore from core_art.v_Dt_sistemi dts21 where dts21.id_Sistema = Vatt.id_Sistema and dts21.nome in ('wInfrastructureLATeamId'))
        when dts20.valore = 'Subcontract' then (select dts22.valore from core_art.v_Dt_sistemi dts22 where dts22.id_Sistema = Vatt.id_Sistema and dts22.nome in ('wInfrastructureLASubContractCode'))
        else null
        end "APPALTO INFRASTRUT."
    ,dts19.valore "STATO INFRASTRUT."
    ,case
        when dts19.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='infrastructure'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. INFRASTRUT."
    ,case
        when dts24.valore = 'Team' then (select dts25.valore from core_art.v_Dt_sistemi dts25 where dts25.id_Sistema = Vatt.id_Sistema and dts25.nome in ('wLayingLATeamId'))
        when dts24.valore = 'Subcontract' then (select dts26.valore from core_art.v_Dt_sistemi dts26 where dts26.id_Sistema = Vatt.id_Sistema and dts26.nome in ('wLayingLASubContractCode'))
        else null
        end "APPALTO POSA"
    ,dts23.valore "STATO POSA"
    ,case
        when dts23.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='laying'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. POSA"
    ,case
        when dts28.valore = 'Team' then (select dts29.valore from core_art.v_Dt_sistemi dts29 where dts29.id_Sistema = Vatt.id_Sistema and dts29.nome in ('wJunctionLATeamId'))
        when dts28.valore = 'Subcontract' then (select dts30.valore from core_art.v_Dt_sistemi dts30 where dts30.id_Sistema = Vatt.id_Sistema and dts30.nome in ('wJunctionLASubContractCode'))
        else null
        end "APPALTO GIUNZIONE"
    ,dts27.valore "STATO GIUNZIONE"
    ,case
        when dts27.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='junction'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. GIUNZIONE"
    ,case
        when dts7.valore = 'Team' then (select dts8.valore from core_art.v_Dt_sistemi dts8 where dts8.id_Sistema = Vatt.id_Sistema and dts8.nome in ('wUpdateDatabaseF1LATeamId'))
        when dts7.valore = 'Subcontract' then (select dts9.valore from core_art.v_Dt_sistemi dts9 where dts9.id_Sistema = Vatt.id_Sistema and dts9.nome in ('wUpdateDatabaseF1LASubContractCode'))
        else null
        end "APPALTO GEN. CLLI"
    ,dts6.valore "STATO GEN. CLLI"
    ,dts46.valore "CAUSALE SOSP. GEN. CLLI"
    ,dts48.valore "MOTIVO SOSPENSIONE GEN. CLLI"
    ,case
        when dts6.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='updateDatabaseF1'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. GEN. CLLI"
    ,case
        when dts11.valore = 'Team' then (select dts12.valore from core_art.v_Dt_sistemi dts12 where dts12.id_Sistema = Vatt.id_Sistema and dts12.nome in ('wUpdateDatabaseF2LATeamId'))
        when dts11.valore = 'Subcontract' then (select dts13.valore from core_art.v_Dt_sistemi dts13 where dts13.id_Sistema = Vatt.id_Sistema and dts13.nome in ('wUpdateDatabaseF2LASubContractCode'))
        else null
        end "APPALTO AGG. CART."
    ,dts10.valore "STATO AGG. CART."
    ,dts47.valore "CAUSALE SOSP. AGG. CART."
    ,dts49.valore "MOTIVO SOSPENSIONE AGG. CART"
    ,case
        when dts10.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='updateDatabaseF2'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. AGG. CART."
    ,case
        when dts32.valore = 'Team' then (select dts33.valore from core_art.v_Dt_sistemi dts33 where dts33.id_Sistema = Vatt.id_Sistema and dts33.nome in ('wTestAppLATeamId'))
        when dts32.valore = 'Subcontract' then (select dts34.valore from core_art.v_Dt_sistemi dts34 where dts34.id_Sistema = Vatt.id_Sistema and dts34.nome in ('wTestAppLASubContractCode'))
        else null
        end "APPALTO COLLAUDO APP"
    ,dts31.valore "STATO COLLAUDO APP"
    ,case
        when dts31.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='testApp'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. COLLAUDO APP"
    ,case
        when dts36.valore = 'Team' then (select dts37.valore from core_art.v_Dt_sistemi dts37 where dts37.id_Sistema = Vatt.id_Sistema and dts37.nome in ('wTestOTDRLATeamId'))
        when dts36.valore = 'Subcontract' then (select dts38.valore from core_art.v_Dt_sistemi dts38 where dts38.id_Sistema = Vatt.id_Sistema and dts38.nome in ('wTestOTDRLASubContractCode'))
        else null
        end "APPALTO COLLAUDO OTDR"
    ,dts35.valore "STATO COLLAUDO OTDR"
    ,case
        when dts35.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='testOTDR'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. COLLAUDO OTDR"
    ,case
        when dts41.valore = 'Team' then (select dts42.valore from core_art.v_Dt_sistemi dts42 where dts42.id_Sistema = Vatt.id_Sistema and dts42.nome in ('wRestorationLATeamId'))
        when dts41.valore = 'Subcontract' then (select dts43.valore from core_art.v_Dt_sistemi dts43 where dts43.id_Sistema = Vatt.id_Sistema and dts43.nome in ('wRestorationLASubContractCode'))
        else null
        end "APPALTO RIPRISTINO"
    ,dts40.valore "STATO RIPRISTINO"
    ,case
        when dts40.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='restoration'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. RIPRISTINO"
    ,case
        when dts45.valore = 'Team' then (select dts46.valore from core_art.v_Dt_sistemi dts46 where dts46.id_Sistema = Vatt.id_Sistema and dts46.nome in ('wPlanningLATeamId'))
        when dts45.valore = 'Subcontract' then (select dts47.valore from core_art.v_Dt_sistemi dts47 where dts47.id_Sistema = Vatt.id_Sistema and dts47.nome in ('wPlanningLASubContractCode'))
        else null
        end "APPALTO PIAN IMPRESA"
    ,dts44.valore "STATO PIAN IMPRESA"
    ,case
        when dts44.valore is not null then (select data_Esecuzione from (
            select dtai.valore, max(vsa.data_Esecuzione) data_Esecuzione
            from core_art.v_storia_Attivita vsa
                join core_art.v_dta dtai on dtai.id_Attivita = Vsa.id_attivita and dtai.data_Esecuzione = vsa.data_esecuzione and dtai.nome ='planning'
            where vsa.id_attivita = vatt.id
            and vsa.azione in ('APERTURA_LAVORO', 'CHIUSURA_LAVORO_OK','CHIUSURA_LAVORO_KO')
            group by dtai.valore
        ))
        else null
        end "DATA ULTIMA VAR. PIAN IMPRESA"
    ,case when ptlc01."cabinetType"='ARLO' then dts4.valore||'-A-'||LPAD( dts2.valore, 4,'0') else null end "Arlo"
    ,pt."clusterJobReport" "Cluster"
    ,pt."penetrationIndex" "Fascia penetrazione"
	,pt."testData001" "Bando"
	,pt."testData002" "Area di Influenza (AI)"
	,pt."testData003" "Data Collaudo"
	,pt."testData004" "Data Connected"
	,pt."testData005" "Splitter Secondario (SS)"
	,pt."testData006" "Splitter Primario (SP)"
	,pt."testData007" "Fatturazione"
	,cast (pt."testData008" as DATE) "Data Test"
	,pt."testData009" "Esito Collaudo"
	,pt."testData010" "Presenza OTDR"
	,pt."testData011" "Presenza Foto"
	,pt."testData012" "Stato RCI"
	,cast (pt."testData013" as DATE) "Data richiesta VUC"
	,pt."testData014" "Id Stato VUC"
	,cast (pt."testData015" as DATE) "Data VUC"
	,pt."testData016" "Network NGNeer"
	,pt."workingGroupCode" "CDL"
    ,pt.UI
    ,dts39.valore "regionId"
from core_art.pt_lc02 pt
	join core_art.v_attivita vatt on vatt.id = pt.id_attivita
	join core_art.v_Dt_sistemi dts on dts.id_Sistema = Vatt.id_Sistema and dts.nome ='id'
	join core_art.a_padre_a_figlio apaf on apaf.figlio = vatt.id
	join core_art.v_attivita vatt2 on vatt2.id = apaf.padre
	join core_art.v_Dt_sistemi dts2 on dts2.id_Sistema = Vatt2.id_Sistema and dts2.nome ='id'
	join core_art.v_Dt_sistemi dts3 on dts3.id_Sistema = Vatt2.id_Sistema and dts3.nome ='city'
	join core_art.v_Dt_sistemi dts4 on dts4.id_Sistema = Vatt2.id_Sistema and dts4.nome ='centralId'
    join core_art.v_Dt_sistemi dts39 on dts39.id_Sistema = Vatt2.id_Sistema and dts39.nome ='regionId'
    join core_art.pt_lc01 ptlc01 on ptlc01.id_Attivita=Vatt2.id
	left join core_art.v_Dt_sistemi dts5 on dts5.id_Sistema = Vatt.id_Sistema and dts5.nome ='wSurveyLAS'
    left join core_art.v_Dt_sistemi dts16 on dts16.id_Sistema = Vatt.id_Sistema and dts16.nome ='wSurveyLAMaker'
	left join core_art.v_Dt_sistemi dts6 on dts6.id_Sistema = Vatt.id_Sistema and dts6.nome ='wUpdateDatabaseF1LAS'
	left join core_art.v_Dt_sistemi dts7 on dts7.id_Sistema = Vatt.id_Sistema and dts7.nome = 'wUpdateDatabaseF1LAMaker'
	left join core_art.v_Dt_sistemi dts10 on dts10.id_Sistema = Vatt.id_Sistema and dts10.nome ='wUpdateDatabaseF2LAS'
	left join core_art.v_Dt_sistemi dts11 on dts11.id_Sistema = Vatt.id_Sistema and dts11.nome = 'wUpdateDatabaseF2LAMaker'
    left join core_art.v_Dt_sistemi dts14 on dts14.id_Sistema = Vatt.id_Sistema and dts14.nome = 'privatePermitsStatus'
	left join core_art.v_Dt_sistemi dts15 on dts15.id_Sistema = Vatt.id_Sistema and dts15.nome = 'privatePermitsLAMaker'
    left join core_art.v_Dt_sistemi dts19 on dts19.id_Sistema = Vatt.id_Sistema and dts19.nome = 'wInfrastructureLAS'
	left join core_art.v_Dt_sistemi dts20 on dts20.id_Sistema = Vatt.id_Sistema and dts20.nome = 'wInfrastructureLAMaker'
    left join core_art.v_Dt_sistemi dts23 on dts23.id_Sistema = Vatt.id_Sistema and dts23.nome = 'wLayingLAS'
	left join core_art.v_Dt_sistemi dts24 on dts24.id_Sistema = Vatt.id_Sistema and dts24.nome = 'wLayingLAMaker'
    left join core_art.v_Dt_sistemi dts27 on dts27.id_Sistema = Vatt.id_Sistema and dts27.nome = 'wJunctionLAS'
	left join core_art.v_Dt_sistemi dts28 on dts28.id_Sistema = Vatt.id_Sistema and dts28.nome = 'wJunctionLAMaker'
    left join core_art.v_Dt_sistemi dts31 on dts31.id_Sistema = Vatt.id_Sistema and dts31.nome = 'wTestAppLAS'
	left join core_art.v_Dt_sistemi dts32 on dts32.id_Sistema = Vatt.id_Sistema and dts32.nome = 'wTestAppLAMaker'
    left join core_art.v_Dt_sistemi dts35 on dts35.id_Sistema = Vatt.id_Sistema and dts35.nome = 'wTestOTDRLAS'
	left join core_art.v_Dt_sistemi dts36 on dts36.id_Sistema = Vatt.id_Sistema and dts36.nome = 'wTestOTDRLAMaker'
    left join core_art.v_Dt_sistemi dts40 on dts40.id_Sistema = Vatt.id_Sistema and dts40.nome = 'wRestorationLAS'
	left join core_art.v_Dt_sistemi dts41 on dts41.id_Sistema = Vatt.id_Sistema and dts41.nome = 'wRestorationLAMaker'
    left join core_art.v_Dt_sistemi dts44 on dts44.id_Sistema = Vatt.id_Sistema and dts44.nome = 'wPlanningLAS'
	left join core_art.v_Dt_sistemi dts45 on dts45.id_Sistema = Vatt.id_Sistema and dts45.nome = 'wPlanningLAMaker'
    left join core_art.v_Dt_sistemi dts46 on dts46.id_Sistema = Vatt.id_Sistema and dts46.nome = 'wUpdateDatabaseF1SuspReason'
    left join core_art.v_Dt_sistemi dts47 on dts47.id_Sistema = Vatt.id_Sistema and dts47.nome = 'wUpdateDatabaseF2SuspReason'
    left join core_art.v_Dt_sistemi dts48 on dts48.id_Sistema = Vatt.id_Sistema and dts48.nome = 'wUpdateDatabaseF1SuspNote'
    left join core_art.v_Dt_sistemi dts49 on dts49.id_Sistema = Vatt.id_Sistema and dts49.nome = 'wUpdateDatabaseF2SuspNote';

drop materialized view MV_TIM_FIBERCOP_PTE;

CREATE MATERIALIZED VIEW MV_TIM_FIBERCOP_PTE
BUILD IMMEDIATE
  USING INDEX
  REFRESH COMPLETE ON DEMAND
AS select *
from V_TIM_FIBERCOP_PTE;

COMMENT ON MATERIALIZED VIEW MV_TIM_FIBERCOP_PTE  IS 'snapshot table for snapshot V_TIM_FIBERCOP_PTE';

GRANT SELECT ON MV_TIM_FIBERCOP_PTE TO CORE_ART;

begin dbms_refresh.add('MV_TIM_FIBERCOP_PTE','MV_TIM_FIBERCOP_PTE');
end;
/


show errors

quit
