set line 1000
set autocommit off
set echo on
set <PERSON>R<PERSON><PERSON><PERSON> on  size 1000000
set <PERSON>R<PERSON><PERSON><PERSON> off

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE OR REPLACE FORCE EDITIONABLE VIEW "AP"."V_R_TEAMS" ("USER<PERSON><PERSON>", "OPI<PERSON>", "EMAIL", "S<PERSON>", "C<PERSON>", "CIDSAP", "DATA_I_VALID", "DATA_F_VALID", "COGNOME", "NOME", "GENERE", "DATA_NASCITA", "COMUNE_NASCITA", "CODICE_CATASTALE", "PV_NASCITA", "CODICE_FISCALE", "TITOLO_STUDIO", "DESCR_TITOLO_STUDIO", "RAGG_TITOLO_STUDIO", "QUALIFICA", "DESCR_QUALIFICA", "LIVELLO", "DESCR_LIVELLO", "RUOLO_PROF", "DESCR_RUOLO_PROF", "<PERSON>OS<PERSON>", "<PERSON>ATA_ASSUNZIONE", "COMUNE_ASSUNZIONE", "DESCR_COMUNE_ASSUNZIONE", "DATA_CESSAZIONE", "UNITA_ORGANIZ", "SIGLA_UNITA_ORGANIZ", "CLASSE_UNITA_ORGANIZ", "DESCR_CLASSE_UNITA_ORGANIZ", "RAGG_CLASSE_UNITA_ORGANIZ", "FLAG_ASS_TEC", "FLAG_SQUADRA", "TIPO_RILEVAZ_PRESTAZ", "SOC_RESP_UNITA_ORGANIZ", "CID_RESP_UNITA_ORGANIZ", "CIDSAP_RESP_UNITA_ORGANIZ", "SOC_DATORE_LAVORO", "CID_DATORE_LAVORO", "CIDSAP_DATORE_LAVORO", "DATA_ORA_VAR", "PARTNER", "ID_TALENTHIA", "REGIME_TRASFERTA", "COMUNE_RESIDENZA", "CAP_RESIDENZA", "INDIRIZZO_RESIDENZA", "PV_RESIDENZA", "DESCR_COMUNE_RESIDENZA", "FRAZIONE_RESIDENZA", "CIVICO_RESIDENZA", "FLAG_GALLERIA", "FLAG_TORRE", "FLAG_FORFETARIO", "FLAG_COLLABORATORE", "CCOSTO_LOGISTICO", "PROFILO_SAP", "CONTRATTO", "DESCR_CONTRATTO", "FLAG_OPERATIVO", "DATA_MITRIC", "DECOR_GRUPPO_OPERATIVO", "SOCRESP_GRUPPO_OPERATIVO", "CIDRESP_GRUPPO_OPERATIVO", "GRUPPO_OPERATIVO", "CODICE_SOGGETTO", "TIPO_CONTRATTO", "DESCR_TIPO_CONTRATTO", "TIPO_PARTTIME", "DESCR_TIPO_PARTTIME", "CODICE_ORGANICO", "DESCR_CODICE_ORGANICO") AS 
select b.userid USERNAME, b.opid opid, b.email, c."SOC",c."CID",c."CIDSAP",c."DATA_I_VALID",c."DATA_F_VALID",c."COGNOME",c."NOME",c."GENERE",c."DATA_NASCITA",c."COMUNE_NASCITA",c."CODICE_CATASTALE",c."PV_NASCITA",c."CODICE_FISCALE",c."TITOLO_STUDIO",c."DESCR_TITOLO_STUDIO",c."RAGG_TITOLO_STUDIO",c."QUALIFICA",c."DESCR_QUALIFICA",c."LIVELLO",c."DESCR_LIVELLO",c."RUOLO_PROF",c."DESCR_RUOLO_PROF",c."CCOSTO",c."DATA_ASSUNZIONE",c."COMUNE_ASSUNZIONE",c."DESCR_COMUNE_ASSUNZIONE",c."DATA_CESSAZIONE",c."UNITA_ORGANIZ",c."SIGLA_UNITA_ORGANIZ",c."CLASSE_UNITA_ORGANIZ",c."DESCR_CLASSE_UNITA_ORGANIZ",c."RAGG_CLASSE_UNITA_ORGANIZ",c."FLAG_ASS_TEC",c."FLAG_SQUADRA",c."TIPO_RILEVAZ_PRESTAZ",c."SOC_RESP_UNITA_ORGANIZ",c."CID_RESP_UNITA_ORGANIZ",c."CIDSAP_RESP_UNITA_ORGANIZ",c."SOC_DATORE_LAVORO",c."CID_DATORE_LAVORO",c."CIDSAP_DATORE_LAVORO",c."DATA_ORA_VAR",c."PARTNER",c."ID_TALENTHIA",c."REGIME_TRASFERTA",c."COMUNE_RESIDENZA",c."CAP_RESIDENZA",c."INDIRIZZO_RESIDENZA",c."PV_RESIDENZA",c."DESCR_COMUNE_RESIDENZA",c."FRAZIONE_RESIDENZA",c."CIVICO_RESIDENZA",c."FLAG_GALLERIA",c."FLAG_TORRE",c."FLAG_FORFETARIO",c."FLAG_COLLABORATORE",c."CCOSTO_LOGISTICO",c."PROFILO_SAP",c."CONTRATTO",c."DESCR_CONTRATTO",c."FLAG_OPERATIVO",c."DATA_MITRIC",c."DECOR_GRUPPO_OPERATIVO",c."SOCRESP_GRUPPO_OPERATIVO",c."CIDRESP_GRUPPO_OPERATIVO",c."GRUPPO_OPERATIVO",c."CODICE_SOGGETTO",c."TIPO_CONTRATTO",c."DESCR_TIPO_CONTRATTO",c."TIPO_PARTTIME",c."DESCR_TIPO_PARTTIME",c."CODICE_ORGANICO",c."DESCR_CODICE_ORGANICO"
from anag.persone@service_anag C
    join anag.UTENTI@service_anag b on  B.SOC=C.SOC AND B.CID=C.CID
WHERE c.DATA_I_VALID <= SYSDATE
    AND c.DATA_F_VALID > SYSDATE
    AND c.FLAG_SQUADRA <> ' '
    and c.PARTNER IS NULL
    AND C.DATA_F_VALID>SYSDATE
    AND (C.DATA_CESSAZIONE is null or C.DATA_CESSAZIONE > sysdate)
;


GRANT SELECT ON "AP"."V_R_TEAMS" TO "AP_ART";
show errors

quit
