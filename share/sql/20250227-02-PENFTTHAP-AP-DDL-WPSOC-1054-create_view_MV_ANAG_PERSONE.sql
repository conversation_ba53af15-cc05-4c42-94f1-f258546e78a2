set line 1000
set autocommit off
set echo on
set <PERSON><PERSON><PERSON><PERSON><PERSON> on  size 1000000
set <PERSON>R<PERSON><PERSON><PERSON> off

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE MATERIALIZED VIEW "AP"."MV_ANAG_PERSONE" ("USERNAME", "OPID", "E<PERSON>IL", "SOC", "C<PERSON>", "<PERSON>ID<PERSON>P", "DATA_I_VALID", "DATA_F_VALID", "COGNOME", "NOME", "GENERE", "DATA_NASCITA", "COMUNE_NASCITA", "CODICE_CATASTALE", "PV_NASCITA", "CODICE_FISCALE", "TITOLO_STUDIO", "DESCR_TITOLO_STUDIO", "RAGG_TITOLO_STUDIO", "QUALIFICA", "DESCR_QUALIFICA", "LIVELLO", "DESCR_LIVELLO", "RUOLO_PROF", "DESCR_RUOLO_PROF", "<PERSON>OS<PERSON>", "<PERSON><PERSON><PERSON>_ASSUNZION<PERSON>", "COMUNE_ASSUNZIONE", "DESCR_COMUNE_ASSUNZIONE", "DATA_CESSAZIONE", "UNITA_ORGANIZ", "SIGLA_UNITA_ORGANIZ", "CLASSE_UNITA_ORGANIZ", "DESCR_CLASSE_UNITA_ORGANIZ", "RAGG_CLASSE_UNITA_ORGANIZ", "FLAG_ASS_TEC", "FLAG_SQUADRA", "TIPO_RILEVAZ_PRESTAZ", "SOC_RESP_UNITA_ORGANIZ", "CID_RESP_UNITA_ORGANIZ", "CIDSAP_RESP_UNITA_ORGANIZ", "SOC_DATORE_LAVORO", "CID_DATORE_LAVORO", "CIDSAP_DATORE_LAVORO", "DATA_ORA_VAR", "PARTNER", "ID_TALENTHIA", "REGIME_TRASFERTA", "COMUNE_RESIDENZA", "CAP_RESIDENZA", "INDIRIZZO_RESIDENZA", "PV_RESIDENZA", "DESCR_COMUNE_RESIDENZA", "FRAZIONE_RESIDENZA", "CIVICO_RESIDENZA", "FLAG_GALLERIA", "FLAG_TORRE", "FLAG_FORFETARIO", "FLAG_COLLABORATORE", "CCOSTO_LOGISTICO", "PROFILO_SAP", "CONTRATTO", "DESCR_CONTRATTO", "FLAG_OPERATIVO", "DATA_MITRIC", "DECOR_GRUPPO_OPERATIVO", "SOCRESP_GRUPPO_OPERATIVO", "CIDRESP_GRUPPO_OPERATIVO", "GRUPPO_OPERATIVO", "CODICE_SOGGETTO", "TIPO_CONTRATTO", "DESCR_TIPO_CONTRATTO", "TIPO_PARTTIME", "DESCR_TIPO_PARTTIME", "CODICE_ORGANICO", "DESCR_CODICE_ORGANICO")
  SEGMENT CREATION IMMEDIATE
  ORGANIZATION HEAP PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TT_DATA" 
  BUILD IMMEDIATE
  USING INDEX 
  REFRESH COMPLETE ON DEMAND
  USING DEFAULT LOCAL ROLLBACK SEGMENT
  USING ENFORCED CONSTRAINTS DISABLE ON QUERY COMPUTATION DISABLE QUERY REWRITE
  AS select *
from v_teams;

COMMENT ON MATERIALIZED VIEW "AP"."MV_ANAG_PERSONE"  IS 'snapshot table for snapshot AP.MV_ANAG_PERSONE';

CREATE UNIQUE INDEX idx_cidsap ON "AP"."MV_ANAG_PERSONE" (CIDSAP);
GRANT SELECT ON "AP"."MV_ANAG_PERSONE" TO "AP_ART";
GRANT SELECT ON "AP"."MV_ANAG_PERSONE" TO "AP_RPT" WITH GRANT OPTION;

show errors

quit
