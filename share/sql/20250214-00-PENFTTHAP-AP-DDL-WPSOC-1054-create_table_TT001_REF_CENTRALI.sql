set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000
set SERVEROUT off

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE TABLE "AP"."TT001_REF_CENTRALI" (
    "AOR" VARCHAR2(100),
    "COD_SETTORE" VARCHAR2(100),
    "SETTORE" VARCHAR2(100),
    "COD_CENTRALE" VARCHAR2(100),
    "CENTRALE" VARCHAR2(100),
    "ACL" VARCHAR2(100)NOT NULL,
    "RO" VARCHAR2(100),
    "REGIONE" VARCHAR2(100),
    "CLLI" VARCHAR2(100),
    "CITTA" VARCHAR2(100),
    "PROVINC<PERSON>" VARCHAR2(100),
    "COD_AOR" VARCHAR2(100),
    PRIMARY KEY ("ACL")
);

GRANT SELECT ON "AP"."TT001_REF_CENTRALI" TO AP_ART;

show errors

quit
