set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

update gruppi set nome = 'AT_CIVILE', descrizione = 'Assistente Tecnico per lavori civili e posa cavi' where nome = 'ASSISTENTE_TECNICO';

update gruppi set nome = 'REFERENTE_PERMESSI_AP', descrizione = 'È responsabile dell''apertura ed avanzamento della permessistica sulle Aree Permesso. Fa capo al Supporto Progettazione' where nome = 'RESPONSABILE_PERMESSI';

update gruppi set nome = 'REFERENTE_PERMESSI_BUILDING', descrizione = 'È responsabile dell''apertura ed avanzamento della permessistica sui Building. In genere è una società esterna' where nome = 'SERVICE';

commit;

quit


