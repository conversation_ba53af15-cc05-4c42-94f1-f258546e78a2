set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE OR REPLACE VIEW V_R_CENTRI_LAVORO AS 
SELECT '49' "companyCode",
	substr(ccosto, 1, 2) || substr(ccosto, length(ccosto) - 3) "id",
        descr "name"
FROM
    anag.ccosto@service_anag
WHERE
    (ccosto like '49%66__' or ccosto like '49%64__' or ccosto like '49%61__' or ccosto like '49%69__')
    and tipo = '4'
;

quit
