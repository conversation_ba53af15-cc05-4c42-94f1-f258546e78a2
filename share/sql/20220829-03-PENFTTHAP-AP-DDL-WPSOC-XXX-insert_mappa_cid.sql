set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever o<PERSON><PERSON>r  exit 2 rollback;

delete AP.MAPPA_CID;
REM INSERTING into AP.MAPPA_CID
SET DEFINE OFF;
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090953','49390953');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090954','49390954');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090955','50590955');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090956','49390956');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090959','50590959');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090963','10090963');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090968','50590968');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090969','10090969');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090974','49390974');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090980','49390980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090981','49390981');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090982','49390982');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090983','49390983');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090984','10090984');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090985','49390985');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090987','49390987');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090992','49390992');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090996','49390996');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090998','49390998');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090999','49390999');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091004','49391004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091006','49391006');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091007','49391007');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091009','49391009');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091014','10091014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091015','49391015');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091018','49391018');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091019','49391019');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091021','49391021');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091022','49391022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091029','49391029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091043','49391043');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091044','49391044');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091045','10091045');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091046','49391046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091048','49391048');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091050','49391050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091051','49391051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091054','49391054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091055','49391055');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091057','10091057');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091060','49391060');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091061','49391061');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091062','50591062');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091063','49391063');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091069','49391069');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091070','49391070');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091071','49391071');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091078','49391078');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091080','49391080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091081','49391081');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091086','49391086');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091093','49391093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091094','49391094');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091095','49391095');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091104','49391104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091108','49391108');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091109','50591109');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091113','49391113');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091114','49391114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091115','49391115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091116','49391116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091117','49391117');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091119','49391119');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091120','49391120');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091121','49391121');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091124','49391124');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091125','49391125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091126','50591126');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091130','49391130');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091140','49391140');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091142','49391142');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091143','49391143');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091144','49391144');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091146','10091146');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091149','10091149');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091154','49391154');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091155','49391155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091156','49391156');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091157','49391157');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091159','49391159');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091163','49391163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091164','49391164');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091168','10091168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091169','49391169');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091170','10091170');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091174','50591174');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091177','49391177');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091179','49391179');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091180','49391180');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091184','50591184');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091185','49391185');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091186','49391186');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091187','49391187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091188','49391188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091190','49391190');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091191','49391191');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091192','49391192');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091193','49391193');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091194','49391194');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091195','49391195');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091199','49391199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091200','49391200');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091201','50591201');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091202','49391202');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091206','50591206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091207','49391207');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091208','49391208');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091210','10091210');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091212','50591212');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091215','49391215');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091218','49391218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091225','49391225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091227','49391227');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091229','50591229');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091233','49391233');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091234','49391234');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091235','49391235');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091237','49391237');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091239','49391239');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091241','49391241');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091242','49391242');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091244','49391244');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091245','49391245');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091247','49391247');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091248','49391248');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091249','49391249');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091251','49391251');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091252','49391252');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091255','10091255');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091261','49391261');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091264','49391264');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091265','49391265');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091266','49391266');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091268','49391268');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091271','49391271');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091272','49391272');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091277','10091277');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091279','49391279');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091284','49391284');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091287','49391287');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091288','49391288');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091291','50591291');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091293','50591293');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091309','50591309');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091312','49391312');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091313','50591313');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091315','49391315');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091316','49391316');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091317','49391317');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091318','49391318');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091324','49391324');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091332','49391332');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091335','49391335');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091337','49391337');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091339','49391339');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091340','49391340');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091345','49391345');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091346','49391346');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091355','49391355');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091356','49391356');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091358','49391358');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091360','49391360');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091361','50591361');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091363','49391363');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091367','49391367');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091368','49391368');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091369','49391369');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091370','49391370');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091371','49391371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091374','10091374');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091375','10091375');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091384','49391384');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091386','50591386');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091387','49391387');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091388','49391388');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091389','49391389');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091390','50591390');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091392','49391392');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091393','49391393');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091394','49391394');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091395','49391395');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091396','49391396');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091397','49391397');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091400','50591400');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091402','49391402');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091407','49391407');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091408','49391408');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091409','49391409');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091410','49391410');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091413','49391413');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091414','49391414');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091415','49391415');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091419','49391419');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091421','50591421');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091426','49391426');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091427','49391427');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091428','49391428');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091430','49391430');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091433','49391433');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091434','49391434');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091437','49391437');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091438','49391438');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091439','49391439');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091442','49391442');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091444','49391444');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091446','49391446');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091447','49391447');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091449','49391449');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091450','49391450');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091452','49391452');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091453','49391453');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091458','49391458');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091462','10091462');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091463','50591463');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091464','50591464');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091472','49391472');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091474','49391474');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091475','49391475');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091476','49391476');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091478','50591478');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091483','49391483');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091486','49391486');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091487','49391487');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091490','50591490');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091491','49391491');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091493','49391493');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091494','49391494');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091495','49391495');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091500','49391500');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091501','49391501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091502','10091502');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091505','49391505');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091507','49391507');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091508','49391508');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091509','49391509');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091511','49391511');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091512','49391512');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091513','49391513');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091514','10091514');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091515','49391515');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091517','49391517');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091520','50591520');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091524','49391524');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091525','49391525');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091528','49391528');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091530','49391530');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091531','49391531');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091533','49391533');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091534','49391534');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091535','49391535');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091536','49391536');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091537','49391537');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091538','49391538');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091540','49391540');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091541','49391541');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091542','49391542');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091543','49391543');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091545','49391545');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091546','49391546');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091548','49391548');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091553','50591553');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091554','49391554');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091557','49391557');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091558','49391558');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091559','49391559');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091561','49391561');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091564','49391564');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091566','49391566');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091567','49391567');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091569','49391569');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091570','49391570');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091572','10091572');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091573','49391573');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091575','49391575');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091576','49391576');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091577','49391577');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091578','49391578');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091580','49391580');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091582','49391582');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091585','49391585');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091589','10091589');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091591','49391591');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091595','49391595');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091596','49391596');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091600','49391600');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091602','49391602');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091603','49391603');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091604','49391604');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091605','49391605');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091606','49391606');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091607','49391607');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091609','50591609');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091612','49391612');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091613','50591613');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091614','10091614');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091615','50591615');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091616','49391616');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091620','50591620');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091623','50591623');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091625','49391625');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091626','49391626');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091627','49391627');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091628','49391628');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091631','49391631');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091634','50591634');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091636','49391636');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091637','50591637');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091638','49391638');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091639','49391639');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091641','49391641');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091645','49391645');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091646','49391646');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091649','49391649');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091652','50591652');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091654','49391654');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091657','49391657');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091667','49391667');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091670','49391670');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091672','49391672');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091673','10091673');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091674','49391674');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091676','49391676');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091677','49391677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091681','49391681');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091682','49391682');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091703','49391703');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091704','49391704');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091705','49391705');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091706','49391706');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091714','49391714');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091719','50591719');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091721','49391721');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091722','49391722');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091728','49391728');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091734','50591734');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091738','49391738');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091739','49391739');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091740','49391740');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091741','49391741');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091742','10091742');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091744','49391744');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091748','10091748');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091756','10091756');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091759','50591759');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091760','10091760');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091762','49391762');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091766','49391766');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091767','49391767');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091768','49391768');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091769','49391769');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091772','49391772');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091773','10091773');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091774','49391774');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091775','49391775');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091777','49391777');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091778','49391778');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091779','49391779');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091782','49391782');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091787','49391787');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091788','50591788');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091790','49391790');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091801','49391801');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091802','49391802');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091806','49391806');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091807','49391807');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091809','49391809');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091812','10091812');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091817','49391817');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091818','49391818');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091823','49391823');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091824','49391824');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091825','49391825');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091827','49391827');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091828','49391828');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091829','49391829');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091831','10091831');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091832','49391832');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091833','49391833');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091834','49391834');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091836','49391836');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091837','10091837');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091838','10091838');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091839','49391839');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091840','49391840');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091841','49391841');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091843','49391843');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091844','50591844');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091845','50591845');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091855','50591855');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091856','10091856');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091864','49391864');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091868','49391868');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091869','10091869');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091872','49391872');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091882','49391882');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091884','49391884');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091885','49391885');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091887','49391887');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091889','49391889');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091890','49391890');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091892','49391892');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091893','49391893');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091894','49391894');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091895','49391895');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091896','50591896');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091898','49391898');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091900','50591900');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091902','49391902');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091904','50591904');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091907','49391907');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091912','49391912');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091914','49391914');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091915','49391915');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091920','49391920');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091921','49391921');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091922','49391922');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091923','49391923');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091924','50591924');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091928','50591928');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091929','49391929');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091931','49391931');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091934','49391934');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091935','49391935');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091936','49391936');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091937','49391937');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091939','49391939');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091940','49391940');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091941','49391941');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091942','49391942');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091943','49391943');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091944','49391944');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091945','49391945');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091946','49391946');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091948','49391948');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091950','50591950');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091951','49391951');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091952','49391952');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091953','49391953');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091954','49391954');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091956','49391956');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091957','49391957');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091958','50591958');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091962','49391962');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091963','49391963');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091964','49391964');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091968','49391968');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091969','49391969');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091973','49391973');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091975','50591975');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091976','49391976');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091978','50591978');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091980','50591980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091981','50591981');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091983','50591983');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091984','50591984');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091985','50591985');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091986','50591986');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091988','50591988');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091991','50591991');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091993','50591993');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091996','49391996');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091997','49391997');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091998','49391998');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10091999','49391999');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092000','49392000');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092002','49392002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092003','49392003');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092004','49392004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092005','49392005');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092008','49392008');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092009','49392009');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092014','49392014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092018','10092018');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092019','49392019');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092022','50592022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092023','50592023');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092026','50592026');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092027','50592027');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092028','50592028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092032','49392032');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092036','49392036');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092037','49392037');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092041','50592041');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092042','49392042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092046','50592046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092048','50592048');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092049','50592049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092050','50592050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092054','49392054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092055','49392055');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092056','49392056');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092057','49392057');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092059','10092059');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092060','10092060');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092062','49392062');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092064','50592064');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092065','49392065');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092066','49392066');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092067','50592067');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092080','50592080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092094','49392094');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092099','49392099');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092100','49392100');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092101','49392101');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092103','49392103');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092104','49392104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092105','49392105');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092107','49392107');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092108','49392108');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092110','50592110');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092112','49392112');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092114','49392114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092115','49392115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092116','49392116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092118','49392118');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092120','49392120');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092121','49392121');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092122','49392122');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092124','49392124');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092125','49392125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092128','49392128');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092129','49392129');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092130','49392130');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092133','49392133');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092135','49392135');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092136','49392136');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092139','49392139');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092146','49392146');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092148','49392148');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092151','49392151');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092155','49392155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092156','49392156');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092158','49392158');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092159','49392159');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092162','49392162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092163','49392163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092164','49392164');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092165','49392165');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092168','49392168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092171','49392171');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092172','49392172');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092173','49392173');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092174','49392174');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092177','49392177');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092182','49392182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092185','49392185');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092187','49392187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092188','49392188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092189','49392189');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092190','49392190');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092191','49392191');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092192','49392192');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092195','49392195');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092196','10092196');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092198','50592198');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092206','10092206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092208','49392208');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092210','10092210');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092218','50592218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092220','10092220');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092221','50592221');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092229','49392229');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092231','49392231');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092233','50592233');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092234','50592234');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092235','50592235');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092238','49392238');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092241','10092241');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092246','50592246');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092247','50592247');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092248','50592248');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092249','49392249');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092250','49392250');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092252','49392252');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092253','49392253');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092256','49392256');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092257','49392257');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092258','49392258');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092260','49392260');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092261','49392261');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092262','49392262');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092279','49392279');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092280','49392280');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092284','49392284');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092285','49392285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092287','49392287');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092293','50592293');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092344','49392344');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092350','49392350');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092351','49392351');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092356','49392356');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092357','49392357');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092360','49392360');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092362','10092362');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092369','10092369');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092392','50592392');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092393','50592393');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092394','49392394');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092409','50592409');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092410','50592410');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092411','50592411');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092412','50592412');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092413','50592413');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092416','49392416');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092417','49392417');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092419','49392419');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092420','49392420');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092421','49392421');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092422','49392422');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092423','49392423');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092425','50592425');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092426','49392426');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092428','49392428');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092433','49392433');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092435','49392435');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092444','49392444');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092462','49392462');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092467','49392467');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092471','49392471');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092475','49392475');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092476','49392476');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092478','49392478');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092479','49392479');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092480','10092480');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092481','49392481');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092484','49392484');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092487','49392487');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092489','49392489');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092491','49392491');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092493','49392493');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092494','49392494');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092496','49392496');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092497','49392497');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092499','49392499');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092500','49392500');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092501','49392501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092502','49392502');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092504','49392504');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092506','10092506');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092510','49392510');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092513','49392513');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092515','49392515');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092526','49392526');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092527','50592527');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092528','49392528');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092529','49392529');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092530','49392530');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092532','49392532');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092534','49392534');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092535','49392535');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092536','49392536');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092537','49392537');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092540','49392540');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092541','49392541');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092543','49392543');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092544','49392544');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092547','49392547');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092550','49392550');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092557','49392557');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092558','49392558');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092560','49392560');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092564','49392564');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092565','49392565');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092577','49392577');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092579','49392579');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092581','49392581');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092582','49392582');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092584','49392584');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092585','49392585');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092586','49392586');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092602','49392602');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092609','49392609');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092610','49392610');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092614','49392614');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092621','49392621');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092622','49392622');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092644','49392644');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092645','50592645');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092649','49392649');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092651','49392651');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092653','49392653');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092656','49392656');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092657','49392657');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092658','49392658');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092659','49392659');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092661','49392661');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092662','10092662');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092664','49392664');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092685','49392685');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092686','49392686');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092690','49392690');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092691','49392691');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092693','49392693');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092694','10092694');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092698','49392698');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092700','49392700');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092704','49392704');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092706','49392706');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092708','49392708');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092709','49392709');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092710','49392710');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092711','49392711');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092712','49392712');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092714','49392714');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092717','49392717');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092719','49392719');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092720','10092720');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092723','49392723');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092727','49392727');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092731','49392731');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092732','49392732');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092733','49392733');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092734','49392734');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092735','49392735');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092736','49392736');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092737','49392737');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092740','49392740');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092743','49392743');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092744','49392744');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092745','49392745');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092751','49392751');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092752','49392752');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092754','49392754');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092757','49392757');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092758','50592758');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092763','49392763');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092766','50592766');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092773','50592773');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092774','49392774');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092775','49392775');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092777','49392777');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092781','49392781');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092782','49392782');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092785','49392785');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092787','49392787');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092788','49392788');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092789','49392789');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092790','49392790');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092793','49392793');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092794','10092794');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092795','49392795');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092809','49392809');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092812','49392812');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092813','49392813');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092815','49392815');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092816','49392816');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092821','49392821');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092822','50592822');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092828','49392828');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092830','49392830');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092833','49392833');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092834','49392834');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092839','49392839');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092855','10092855');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092860','10092860');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092867','49392867');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092869','49392869');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092877','49392877');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092878','49392878');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092887','10092887');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092888','49392888');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092889','49392889');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092890','49392890');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092891','49392891');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092892','10092892');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092894','49392894');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092902','10092902');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092912','50592912');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092913','49392913');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092914','49392914');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092915','49392915');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092916','49392916');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092917','49392917');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092919','49392919');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092926','10092926');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092932','50592932');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092935','49392935');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092936','49392936');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092937','49392937');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092944','10092944');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092948','50592948');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092949','50592949');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092957','50592957');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092959','10092959');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092960','49392960');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092963','50592963');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092966','50592966');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092971','10092971');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092974','50592974');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092975','10092975');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092976','49392976');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092980','49392980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092982','49392982');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092983','49392983');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092986','49392986');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092988','49392988');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092989','49392989');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092990','49392990');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092995','50592995');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10092999','50592999');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093000','49393000');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093001','49393001');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093002','49393002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093003','49393003');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093004','49393004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093007','10093007');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093009','49393009');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093010','49393010');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093011','49393011');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093012','49393012');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093013','49393013');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093014','49393014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093015','50593015');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093017','50593017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093020','50593020');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093022','50593022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093023','50593023');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093024','10093024');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093025','50593025');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093027','10093027');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093028','50593028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093029','10093029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093032','50593032');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093033','50593033');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093035','50593035');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093036','49393036');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093037','49393037');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093042','50593042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093043','50593043');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093045','49393045');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093048','49393048');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093049','49393049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093051','49393051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093052','49393052');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093053','50593053');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093054','49393054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093057','49393057');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093060','49393060');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093063','50593063');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093064','49393064');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093065','49393065');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093066','10093066');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093067','49393067');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093069','49393069');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093070','49393070');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093072','10093072');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093073','50593073');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093074','50593074');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093075','50593075');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093077','50593077');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093078','10093078');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093080','49393080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093081','49393081');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093082','50593082');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093083','49393083');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093084','49393084');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093085','49393085');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093086','49393086');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093087','10093087');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093088','10093088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093089','50593089');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093092','50593092');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093093','49393093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093094','50593094');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093095','50593095');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093096','50593096');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093097','49393097');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093098','49393098');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093099','49393099');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093100','49393100');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093101','49393101');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093102','49393102');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093103','49393103');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093104','49393104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093105','49393105');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093106','49393106');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093107','49393107');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093108','49393108');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093109','49393109');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093110','49393110');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093111','49393111');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093112','49393112');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093113','49393113');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093114','49393114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093115','50593115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093116','50593116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093117','49393117');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093120','49393120');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093121','49393121');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093122','49393122');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093123','50593123');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093125','49393125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093131','49393131');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093132','49393132');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093133','50593133');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093136','49393136');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093141','49393141');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093143','49393143');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093145','50593145');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093150','49393150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093154','49393154');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093155','49393155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093157','49393157');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093158','49393158');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093159','49393159');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093161','49393161');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093162','49393162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093163','49393163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093164','49393164');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093166','50593166');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093167','49393167');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093169','10093169');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093170','49393170');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093171','49393171');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093173','49393173');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093174','49393174');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093175','50593175');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093176','50593176');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093177','50593177');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093178','50593178');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093180','49393180');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093181','49393181');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093182','49393182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093184','49393184');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093185','49393185');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093186','49393186');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093187','49393187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093188','49393188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093189','49393189');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093190','49393190');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093191','49393191');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093192','49393192');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093193','10093193');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093194','49393194');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093195','50593195');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093196','49393196');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093197','49393197');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093198','10093198');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093199','49393199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093200','49393200');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093201','49393201');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093202','49393202');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093203','49393203');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093204','49393204');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093205','49393205');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093206','49393206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093207','49393207');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093210','49393210');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093211','49393211');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093217','50593217');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093218','49393218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093219','49393219');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093220','49393220');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093223','49393223');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093224','49393224');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093225','49393225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093226','49393226');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093227','49393227');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093228','49393228');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093230','49393230');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093231','49393231');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093232','49393232');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093235','49393235');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093237','49393237');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093238','49393238');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093239','49393239');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093241','49393241');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093242','49393242');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093247','49393247');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093248','50593248');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093249','49393249');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093250','49393250');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093251','49393251');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093252','49393252');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093253','50593253');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093254','49393254');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093256','50593256');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093258','50593258');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093259','50593259');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093260','50593260');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093261','49393261');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093262','49393262');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093263','49393263');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093264','49393264');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093267','50593267');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093268','49393268');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093269','49393269');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093270','49393270');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093273','49393273');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093276','49393276');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093280','50593280');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093281','50593281');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093282','50593282');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093283','50593283');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093284','49393284');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093285','49393285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093286','49393286');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093287','50593287');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093288','49393288');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093290','49393290');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093293','49393293');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093294','49393294');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093297','49393297');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093298','49393298');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093299','49393299');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093300','49393300');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093301','49393301');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093302','50593302');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093303','49393303');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093304','50593304');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093306','49393306');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093307','50593307');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093308','49393308');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093310','49393310');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093311','49393311');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093313','49393313');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093314','49393314');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093315','50593315');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093316','49393316');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093317','49393317');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093319','49393319');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093320','49393320');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093321','49393321');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093322','49393322');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093324','49393324');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093325','49393325');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093326','49393326');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093328','49393328');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093331','49393331');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093332','49393332');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093333','49393333');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093335','49393335');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093336','50593336');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093337','50593337');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093338','50593338');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093343','49393343');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093346','49393346');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093347','49393347');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093348','49393348');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093349','49393349');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093350','49393350');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093351','49393351');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093352','49393352');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093353','49393353');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093354','49393354');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093356','49393356');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093357','49393357');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093358','49393358');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093359','49393359');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093360','50593360');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093362','49393362');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093363','49393363');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093364','49393364');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093366','49393366');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093367','49393367');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093368','49393368');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093369','49393369');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093370','49393370');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093371','49393371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093373','49393373');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093374','49393374');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093375','49393375');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093376','49393376');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093377','49393377');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093378','49393378');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093379','49393379');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093381','49393381');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093382','49393382');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093384','49393384');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093385','49393385');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093386','49393386');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093387','49393387');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093388','49393388');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093389','49393389');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093390','49393390');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093391','49393391');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093392','49393392');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093393','49393393');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093395','49393395');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093396','49393396');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093397','49393397');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093398','49393398');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093399','49393399');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093400','49393400');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093401','50593401');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093402','49393402');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093403','49393403');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093404','49393404');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093405','49393405');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093406','49393406');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093408','10093408');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093409','49393409');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093410','49393410');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093411','49393411');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093412','49393412');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093414','49393414');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093415','49393415');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093416','49393416');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093417','49393417');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093418','50593418');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093419','49393419');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093420','49393420');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093421','49393421');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093422','49393422');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093423','49393423');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093424','49393424');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093425','49393425');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093426','49393426');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093427','49393427');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093428','49393428');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093429','49393429');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093430','49393430');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093431','49393431');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093432','10093432');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093433','49393433');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093434','49393434');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093435','49393435');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093436','49393436');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093437','49393437');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093438','49393438');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093439','49393439');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093440','49393440');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093441','49393441');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093442','49393442');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093443','50593443');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093444','49393444');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093445','50593445');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093446','49393446');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093447','49393447');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093449','49393449');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093450','49393450');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093451','49393451');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093453','49393453');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093454','49393454');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093455','50593455');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093456','49393456');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093457','49393457');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093458','49393458');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093459','49393459');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093460','49393460');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093461','49393461');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093462','49393462');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093463','49393463');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093464','49393464');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093465','49393465');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093466','49393466');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093467','49393467');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093468','49393468');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093469','50593469');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093470','50593470');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093471','10093471');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093472','50593472');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093473','50593473');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093474','49393474');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093475','49393475');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093476','50593476');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093477','49393477');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093479','50593479');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093480','50593480');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093481','50593481');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093482','50593482');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093483','50593483');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093484','50593484');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093485','50593485');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093486','50593486');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093487','49393487');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093488','50593488');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093489','50593489');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093490','49393490');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093491','49393491');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093492','49393492');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093493','49393493');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093494','50593494');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093495','50593495');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093496','49393496');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093497','49393497');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093498','50593498');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093499','49393499');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093500','49393500');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093501','49393501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093502','49393502');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093503','10093503');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093504','50593504');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093505','49393505');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093507','49393507');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093508','49393508');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093509','50593509');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093510','49393510');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10093511','50593511');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200971','49200971');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200972','49200972');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200973','49200973');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200974','49200974');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200975','49200975');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200976','49200976');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200977','49200977');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200978','49200978');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200979','49200979');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200980','49200980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200981','49200981');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200982','49200982');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200983','49200983');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200984','49200984');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200985','49200985');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200986','49200986');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10200987','49200987');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10248000','49248000');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480001','49480001');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480002','49480002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480003','49480003');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480004','49480004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480006','49480006');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480007','49480007');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480008','49480008');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480009','49480009');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480010','49480010');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480012','49480012');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480013','49480013');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480014','49480014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480015','49480015');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480016','49480016');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480017','49480017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480018','49480018');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480020','49480020');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480021','49480021');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480023','49480023');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480024','49480024');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480025','49480025');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480026','49480026');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480027','49480027');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480028','49480028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480029','49480029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480030','49480030');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480032','49480032');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480033','49480033');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480034','49480034');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480035','49480035');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480037','49480037');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480039','49480039');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480040','49480040');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480042','49480042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480045','49480045');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480046','49480046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480047','49480047');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480049','49480049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480050','49480050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480051','49480051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480052','49480052');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480053','49480053');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10480055','49480055');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901672','10901672');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901683','10901683');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901692','10901692');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901695','10901695');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901699','10901699');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901700','10901700');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10901701','10901701');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10910000','10910000');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991051','50991051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991058','49991058');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991065','49991065');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991066','49991066');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991070','49991070');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991072','49991072');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991074','49991074');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991081','49991081');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991082','49991082');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991083','49991083');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991085','49991085');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991086','49991086');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991087','49991087');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991092','49991092');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991093','49991093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991099','49991099');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991100','49991100');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991114','49991114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991115','49991115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991116','49991116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991117','49991117');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991118','49991118');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991119','49991119');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991120','49991120');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991123','49991123');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991124','49991124');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991125','49991125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991127','49991127');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991129','49991129');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991134','49991134');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991138','49991138');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991139','49991139');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991140','49991140');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991141','49991141');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991142','49991142');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991143','49991143');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991144','49991144');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991145','49991145');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991146','49991146');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991147','49991147');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991148','49991148');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991150','49991150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991151','49991151');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991152','49991152');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991160','49991160');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991161','49991161');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991162','49991162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991166','49991166');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991167','49991167');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991168','49991168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991169','49991169');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991170','49991170');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991171','49991171');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991172','49991172');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991173','49991173');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991174','49991174');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991175','49991175');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991176','49991176');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991177','49991177');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991179','49991179');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991180','49991180');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991181','49991181');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991182','49991182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991183','49991183');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991187','49991187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991188','49991188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991189','49991189');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991190','49991190');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991191','49991191');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991192','49991192');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991193','49991193');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991194','49991194');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991195','49991195');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991196','49991196');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991197','49991197');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991198','49991198');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991199','49991199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991200','49991200');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991201','49991201');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991202','49991202');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991206','49991206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991207','49991207');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991212','49991212');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991214','49991214');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991215','49991215');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991216','49991216');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10991217','49991217');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999441','49999441');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999446','49999446');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999453','49999453');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999456','49999456');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999458','49999458');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999459','49999459');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999460','49999460');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999461','49999461');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999462','49999462');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999518','49999518');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999585','49999585');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999586','49999586');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999587','49999587');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999610','49999610');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999652','49999652');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999658','49999658');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999659','49999659');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999669','49999669');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999701','49999701');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999864','49999864');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999967','49999967');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10999999','49999999');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000207','49300207');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000212','10000212');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000231','49300231');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000285','10000285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000303','49300303');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000312','10000312');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000332','50500332');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000336','10000336');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000338','10000338');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000339','10000339');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000344','10000344');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000345','49300345');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10000346','49300346');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10005293','50505293');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10007674','10007674');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041495','49341495');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041501','49341501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041504','49341504');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041506','49341506');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041508','49341508');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041536','49341536');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041544','49341544');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041559','49341559');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041631','49341631');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041641','49341641');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041653','10041653');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041663','10041663');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041665','49341665');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041676','10041676');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041677','49341677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041678','49341678');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041686','49341686');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041694','49341694');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041705','10041705');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041720','10041720');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041741','49341741');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041743','49341743');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041750','49341750');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041754','49341754');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041759','49341759');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041767','49341767');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041775','49341775');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041776','49341776');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041788','49341788');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041909','10041909');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041934','49341934');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041949','10041949');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10041961','10041961');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10043150','10043150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044353','49344353');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044368','49344368');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044396','49344396');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044408','10044408');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044419','49344419');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044442','49344442');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044460','49344460');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044465','49344465');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044466','49344466');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044507','49344507');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044510','10044510');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044539','49344539');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044540','49344540');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044561','49344561');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044564','49344564');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044592','49344592');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044596','49344596');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044598','49344598');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044614','49344614');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044615','49344615');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044616','49344616');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044624','49344624');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044629','49344629');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044652','10044652');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044669','49344669');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044671','50544671');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044726','49344726');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044728','49344728');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044741','49344741');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044763','49344763');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044770','49344770');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044784','10044784');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044785','49344785');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044788','49344788');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044802','49344802');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044812','49344812');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044826','49344826');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044834','49344834');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044836','49344836');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044844','49344844');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044848','49344848');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044849','49344849');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044857','49344857');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044864','49344864');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044869','49344869');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044875','49344875');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044895','10044895');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044897','49344897');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044901','49344901');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044904','49344904');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044911','49344911');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044955','49344955');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044956','49344956');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044958','49344958');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10044982','49344982');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045004','49345004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045005','49345005');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045011','49345011');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045013','49345013');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045014','49345014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045034','49345034');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045037','49345037');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045038','49345038');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045041','49345041');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045049','49345049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045050','49345050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045070','49345070');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045071','49345071');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045074','49345074');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045076','49345076');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045082','49345082');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045083','49345083');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045087','49345087');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045091','49345091');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045093','49345093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045099','49345099');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045103','49345103');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045104','49345104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045105','49345105');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045112','49345112');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045124','50545124');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045128','49345128');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045133','49345133');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045136','49345136');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045138','49345138');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045139','49345139');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045158','49345158');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045162','49345162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045168','49345168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045183','49345183');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045190','49345190');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045202','49345202');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045206','49345206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045222','49345222');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045240','49345240');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045245','49345245');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045257','10045257');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045258','49345258');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045261','49345261');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045267','49345267');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045269','10045269');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045274','49345274');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045281','49345281');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045288','49345288');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045290','49345290');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045309','49345309');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045319','49345319');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045320','49345320');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045321','49345321');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045322','49345322');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045324','49345324');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045325','49345325');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045326','49345326');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045329','49345329');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045343','49345343');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045345','49345345');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045346','49345346');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045365','49345365');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045391','49345391');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045392','49345392');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045404','49345404');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045416','10045416');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045433','49345433');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045439','10045439');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045441','49345441');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045442','49345442');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045443','49345443');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045446','49345446');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045447','49345447');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045448','49345448');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045453','49345453');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045457','50545457');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045458','49345458');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045464','49345464');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045466','49345466');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045486','49345486');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045502','49345502');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045503','49345503');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045504','49345504');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045505','49345505');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045509','49345509');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045512','49345512');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045514','49345514');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045541','49345541');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045542','49345542');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045563','49345563');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045565','49345565');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045566','49345566');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045568','10045568');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045569','49345569');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045590','49345590');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045599','49345599');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045603','49345603');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045611','49345611');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045613','49345613');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045622','49345622');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045624','49345624');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045625','49345625');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045644','49345644');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045649','49345649');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045651','49345651');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045654','49345654');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045657','49345657');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045659','49345659');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045662','49345662');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045664','49345664');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045665','50545665');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045666','10045666');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045669','50545669');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045671','50545671');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045674','49345674');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045676','49345676');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045677','49345677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045692','50545692');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045718','49345718');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045725','49345725');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045726','49345726');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045727','49345727');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045729','49345729');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045730','49345730');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045731','49345731');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045734','49345734');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045736','49345736');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045737','49345737');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045738','49345738');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045739','49345739');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045741','49345741');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045743','49345743');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045744','49345744');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045746','49345746');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045747','49345747');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045748','49345748');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045749','49345749');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045751','49345751');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045752','49345752');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045755','49345755');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045765','49345765');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045766','49345766');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045785','49345785');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045787','49345787');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045792','49345792');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045793','49345793');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045796','49345796');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045797','49345797');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045820','49345820');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045831','49345831');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045833','49345833');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045835','49345835');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045836','49345836');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045845','50545845');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045847','49345847');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045852','49345852');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045853','49345853');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045856','49345856');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045859','49345859');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045907','49345907');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045915','10045915');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045917','49345917');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045918','49345918');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045919','49345919');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045920','49345920');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045935','49345935');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045941','49345941');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045942','49345942');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045952','49345952');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045953','49345953');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045954','49345954');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045955','49345955');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045956','49345956');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045957','49345957');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045958','49345958');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045967','49345967');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045969','49345969');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045974','49345974');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10045993','49345993');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046021','49346021');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046022','49346022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046023','49346023');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046043','49346043');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046049','10046049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046050','49346050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046051','49346051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046052','49346052');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046053','49346053');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046054','49346054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046088','49346088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046101','49346101');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046102','49346102');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046103','49346103');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046116','49346116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046126','10046126');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046142','49346142');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046143','49346143');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046145','10046145');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046148','10046148');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046161','49346161');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046168','49346168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046208','49346208');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046213','10046213');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046214','49346214');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046240','49346240');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046291','49346291');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046316','49346316');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046371','49346371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046397','49346397');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046414','49346414');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046415','49346415');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046418','49346418');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046461','49346461');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046471','49346471');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046497','49346497');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046501','49346501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046521','49346521');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046544','49346544');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046563','49346563');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10046571','49346571');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10051834','49351834');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10052123','49352123');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10052216','49352216');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10052313','49352313');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10052318','49352318');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061003','49361003');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061004','49361004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061005','49361005');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061010','49361010');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061011','49361011');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061012','50561012');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061014','49361014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061015','49361015');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061016','10061016');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061017','49361017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061018','49361018');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061021','10061021');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061022','10061022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061026','49361026');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061027','49361027');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061028','49361028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061030','49361030');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061031','49361031');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061032','49361032');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061035','49361035');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061037','49361037');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061041','49361041');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061044','49361044');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061045','49361045');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061046','49361046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061047','49361047');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061048','49361048');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061049','49361049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061050','49361050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061051','49361051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061060','49361060');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061063','50561063');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061068','49361068');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061069','10061069');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061070','50561070');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061074','10061074');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061082','49361082');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061084','49361084');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061085','49361085');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061086','49361086');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061088','49361088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061092','49361092');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061095','50561095');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061099','49361099');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061100','49361100');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061104','49361104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061106','49361106');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061107','49361107');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061108','49361108');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061109','49361109');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061113','49361113');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061114','50561114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061115','49361115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061116','10061116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061122','49361122');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061124','49361124');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061132','49361132');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061137','49361137');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061138','49361138');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061139','49361139');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061154','49361154');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061155','49361155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061156','49361156');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061157','49361157');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061162','49361162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061163','49361163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061164','49361164');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061166','49361166');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061169','49361169');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061171','49361171');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061172','49361172');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061173','49361173');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061178','49361178');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061181','50561181');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061182','49361182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061183','49361183');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061185','49361185');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061188','10061188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061197','49361197');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061199','49361199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061202','50561202');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061204','49361204');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061205','49361205');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061206','49361206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061209','49361209');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061211','49361211');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061219','49361219');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061234','49361234');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061236','10061236');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061237','49361237');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061259','49361259');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061272','49361272');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061273','49361273');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061284','49361284');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061285','10061285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10061286','49361286');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062000','50562000');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062001','50562001');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062002','50562002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062004','50562004');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062005','50562005');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062006','49362006');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062008','50562008');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062009','50562009');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062012','49362012');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062014','49362014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062017','50562017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062018','10062018');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062020','50562020');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062021','50562021');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062022','50562022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062023','50562023');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062028','49362028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062030','50562030');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062031','10062031');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062032','50562032');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062033','50562033');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062034','50562034');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062036','50562036');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062038','50562038');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062039','50562039');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062040','10062040');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062041','50562041');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062042','10062042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062043','10062043');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062046','50562046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062049','50562049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062051','50562051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062053','50562053');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062054','49362054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062055','50562055');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062056','49362056');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062057','49362057');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062060','50562060');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062061','50562061');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062063','50562063');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062064','50562064');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062065','50562065');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062066','50562066');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062067','50562067');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062068','10062068');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062072','50562072');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062074','10062074');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062079','49362079');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062080','50562080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062081','10062081');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062083','50562083');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062084','10062084');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062086','50562086');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062088','50562088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062091','50562091');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062092','50562092');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062093','50562093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062095','50562095');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062096','50562096');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062097','10062097');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062100','50562100');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062101','50562101');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062102','50562102');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062104','50562104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062105','50562105');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062106','50562106');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062107','49362107');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062108','10062108');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062109','50562109');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062110','50562110');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062113','10062113');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062114','50562114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062115','50562115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062117','49362117');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062118','50562118');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062120','50562120');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062123','49362123');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062125','50562125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062126','50562126');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062127','50562127');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062128','50562128');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062129','50562129');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062132','10062132');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062133','50562133');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062134','50562134');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062135','10062135');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062136','50562136');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062137','50562137');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062138','50562138');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062139','50562139');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062141','50562141');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062142','50562142');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062143','50562143');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062144','10062144');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062146','50562146');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062147','50562147');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062149','49362149');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062150','10062150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062151','50562151');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062152','10062152');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062154','10062154');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062155','50562155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062156','10062156');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062157','50562157');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062158','50562158');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062160','49362160');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062161','10062161');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062162','10062162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062163','50562163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062168','50562168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062169','50562169');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062170','49362170');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062171','50562171');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062173','49362173');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062174','50562174');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062176','50562176');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062179','50562179');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062180','50562180');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062181','50562181');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062182','50562182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062183','50562183');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062185','49362185');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062186','50562186');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062187','50562187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062188','50562188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062190','50562190');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062191','10062191');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062193','50562193');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062194','50562194');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062195','10062195');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062199','50562199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062200','50562200');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062201','50562201');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062202','50562202');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062203','10062203');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062206','50562206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062207','50562207');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062210','49362210');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062213','10062213');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062214','50562214');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062216','50562216');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062217','50562217');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062218','50562218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062220','50562220');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062221','50562221');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062222','50562222');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062224','50562224');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062225','50562225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062227','50562227');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062229','50562229');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062231','50562231');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062232','50562232');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062233','10062233');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062235','50562235');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062239','50562239');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062242','49362242');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062243','50562243');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062244','50562244');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062247','50562247');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062250','10062250');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062252','50562252');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062253','50562253');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062254','49362254');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10062256','50562256');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10077287','10077287');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078047','49378047');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078142','10078142');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078149','49378149');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078223','49378223');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078271','49378271');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078414','49378414');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078459','49378459');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078488','10078488');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078552','49378552');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078663','49378663');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078669','50578669');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078670','49378670');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078841','10078841');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078863','49378863');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078906','49378906');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078907','49378907');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078908','49378908');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078914','10078914');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078922','49378922');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078939','49378939');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078940','49378940');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078950','49378950');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078968','49378968');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10078987','49378987');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079002','49379002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079013','50579013');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079015','49379015');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079017','50579017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079019','49379019');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079037','49379037');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079038','49379038');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079043','49379043');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079218','10079218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079226','49379226');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079242','49379242');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079269','49379269');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079282','49379282');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079309','49379309');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079313','49379313');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079340','49379340');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079360','49379360');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079362','49379362');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079364','49379364');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079365','49379365');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079371','49379371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079479','49379479');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079492','49379492');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079500','10079500');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079510','49379510');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079518','50579518');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079525','49379525');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079571','49379571');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079598','49379598');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079627','49379627');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079632','49379632');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079633','49379633');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079637','49379637');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079715','49379715');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079720','10079720');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079742','10079742');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079752','49379752');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079765','49379765');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079791','49379791');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079807','49379807');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079869','49379869');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079871','49379871');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079897','49379897');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079915','49379915');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079916','49379916');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079942','10079942');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079964','49379964');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10079973','49379973');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080005','10080005');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080016','49380016');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080082','49380082');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080111','49380111');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080119','49380119');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080150','49380150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080154','49380154');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080167','49380167');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080171','49380171');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080181','49380181');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080198','49380198');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080199','49380199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080201','49380201');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080219','49380219');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080226','49380226');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080229','50580229');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080237','10080237');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080239','49380239');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080270','49380270');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080271','49380271');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080279','10080279');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080282','10080282');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080296','49380296');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080331','10080331');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080332','49380332');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080342','49380342');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080357','49380357');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080358','49380358');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080368','49380368');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080387','49380387');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080398','49380398');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080429','49380429');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080439','49380439');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080453','49380453');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080455','49380455');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080480','50580480');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080481','49380481');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080513','49380513');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080539','49380539');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080547','49380547');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080602','49380602');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080613','49380613');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080624','49380624');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080651','49380651');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080653','49380653');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080669','49380669');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080681','10080681');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080683','50580683');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080685','49380685');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080730','49380730');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080764','50580764');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080772','49380772');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080781','49380781');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080789','49380789');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080795','49380795');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080809','49380809');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080815','49380815');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080834','10080834');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080836','49380836');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080844','49380844');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080845','49380845');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080851','10080851');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080858','49380858');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080862','49380862');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080866','49380866');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080880','49380880');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080905','49380905');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080908','49380908');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080923','10080923');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080925','49380925');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080938','49380938');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080963','10080963');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080965','50580965');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080979','49380979');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080980','49380980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080983','49380983');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10080984','49380984');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081003','49381003');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081010','49381010');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081018','10081018');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081027','49381027');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081042','49381042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081054','49381054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081072','49381072');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081128','10081128');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081135','49381135');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081137','49381137');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081152','49381152');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081188','49381188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081203','49381203');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081205','10081205');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081221','49381221');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081270','49381270');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081286','49381286');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081297','49381297');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081298','49381298');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081303','49381303');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081316','49381316');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081371','49381371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081390','49381390');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081391','10081391');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081405','10081405');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081406','10081406');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081425','49381425');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081430','49381430');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081431','49381431');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081439','49381439');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081441','49381441');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081451','49381451');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081457','49381457');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081459','49381459');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081460','49381460');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081469','49381469');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081470','49381470');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081471','49381471');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081475','49381475');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081525','10081525');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081541','49381541');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081549','49381549');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081558','49381558');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081593','49381593');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081599','49381599');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081600','49381600');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081608','49381608');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081609','49381609');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081679','49381679');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081685','49381685');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081692','49381692');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081700','49381700');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081710','49381710');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081720','49381720');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081742','10081742');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081744','10081744');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081752','49381752');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081760','49381760');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081782','49381782');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081791','49381791');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081795','10081795');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081821','49381821');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081824','49381824');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081832','49381832');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081881','10081881');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081890','49381890');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081892','49381892');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081905','49381905');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081906','10081906');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081912','50581912');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081913','49381913');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081924','49381924');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081933','49381933');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10081948','49381948');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082029','10082029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082030','49382030');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082031','49382031');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082035','49382035');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082038','10082038');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082086','10082086');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082096','10082096');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082103','49382103');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082251','49382251');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082263','49382263');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082269','49382269');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082360','10082360');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082365','10082365');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082371','10082371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082410','49382410');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082421','49382421');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082422','49382422');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082433','50582433');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082436','49382436');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082444','49382444');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082452','49382452');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082463','49382463');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082466','49382466');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082495','49382495');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082499','49382499');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082506','49382506');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082508','49382508');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082512','50582512');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082523','49382523');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082524','49382524');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082527','49382527');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082535','10082535');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082559','49382559');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082564','49382564');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082568','49382568');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082569','49382569');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082572','49382572');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082576','49382576');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082578','49382578');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082589','49382589');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082603','10082603');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082604','49382604');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082621','49382621');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082622','49382622');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082647','49382647');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082649','49382649');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082650','49382650');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082660','49382660');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082671','49382671');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082677','49382677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082679','49382679');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082686','10082686');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082687','49382687');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082689','10082689');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082715','49382715');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082723','49382723');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082749','49382749');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082754','10082754');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082767','49382767');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082768','49382768');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082777','49382777');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082778','49382778');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082779','49382779');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082783','49382783');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082784','49382784');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082804','49382804');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082805','49382805');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082831','49382831');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082836','49382836');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082840','49382840');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082843','49382843');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082848','49382848');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082851','49382851');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082852','49382852');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082857','49382857');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082858','49382858');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082867','49382867');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082873','49382873');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082886','49382886');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082887','49382887');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082888','49382888');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082889','49382889');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082892','49382892');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082893','49382893');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082895','49382895');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082896','49382896');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082897','49382897');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082915','49382915');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082917','49382917');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082932','49382932');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082933','49382933');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082934','49382934');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082959','49382959');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082962','49382962');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10082994','49382994');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083017','49383017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083020','49383020');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083025','49383025');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083050','49383050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083057','49383057');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083058','49383058');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083075','49383075');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083076','49383076');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083079','49383079');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083080','49383080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083084','49383084');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083088','49383088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083093','49383093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083094','49383094');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083099','10083099');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083109','50583109');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083112','49383112');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083119','49383119');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083122','49383122');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083124','49383124');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083141','49383141');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083156','49383156');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083163','49383163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083176','49383176');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083192','49383192');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083204','49383204');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083206','50583206');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083215','49383215');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083218','49383218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083220','49383220');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083221','49383221');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083225','49383225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083243','49383243');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083250','49383250');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083255','49383255');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083277','49383277');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083285','49383285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083299','49383299');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083300','49383300');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083310','49383310');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083311','49383311');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083313','49383313');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083323','49383323');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083325','49383325');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083343','49383343');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083345','49383345');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083364','49383364');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083369','49383369');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083375','49383375');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083378','49383378');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083393','10083393');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083402','50583402');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083406','49383406');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083407','49383407');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083409','49383409');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083415','49383415');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083417','49383417');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083418','49383418');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083428','49383428');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083436','49383436');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083454','49383454');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083480','49383480');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083506','49383506');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083604','49383604');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083670','49383670');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083676','49383676');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083677','49383677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083683','10083683');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083684','50583684');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083706','49383706');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083732','50583732');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083735','49383735');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083756','49383756');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083758','49383758');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083760','49383760');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083761','49383761');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083763','49383763');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083768','49383768');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083770','49383770');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083772','49383772');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083773','49383773');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083774','49383774');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083781','49383781');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083786','49383786');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083789','49383789');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083820','10083820');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083830','49383830');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083852','10083852');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083856','49383856');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083858','49383858');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083859','49383859');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083861','10083861');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083875','49383875');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083881','49383881');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083902','49383902');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083909','49383909');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083912','49383912');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083915','49383915');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083923','49383923');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083930','49383930');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083946','49383946');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083947','49383947');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083948','49383948');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083954','49383954');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083957','49383957');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083966','49383966');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083974','10083974');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083977','49383977');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083979','49383979');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083980','49383980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083988','49383988');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10083997','49383997');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084007','49384007');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084008','50584008');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084012','49384012');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084014','49384014');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084015','10084015');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084017','50584017');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084022','49384022');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084024','49384024');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084026','49384026');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084028','49384028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084029','49384029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084030','49384030');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084035','49384035');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084038','49384038');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084040','49384040');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084043','49384043');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084044','49384044');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084046','49384046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084047','49384047');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084049','49384049');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084050','49384050');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084051','49384051');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084052','49384052');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084053','49384053');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084055','49384055');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084056','49384056');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084063','49384063');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084064','49384064');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084066','49384066');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084067','49384067');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084075','49384075');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084077','49384077');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084079','49384079');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084090','49384090');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084096','49384096');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084098','49384098');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084107','49384107');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084112','49384112');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084125','49384125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084126','49384126');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084156','49384156');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084170','49384170');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084175','49384175');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084187','49384187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084191','49384191');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084192','49384192');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084194','49384194');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084200','49384200');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084201','49384201');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084209','49384209');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084257','49384257');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084260','49384260');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084263','10084263');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084269','49384269');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084270','49384270');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084276','50584276');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084277','49384277');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084282','49384282');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084283','49384283');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084285','49384285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084291','49384291');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084292','49384292');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084300','49384300');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084301','50584301');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084302','49384302');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084304','49384304');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084309','49384309');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084311','49384311');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084314','49384314');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084322','49384322');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084325','49384325');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084326','49384326');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084331','49384331');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084332','49384332');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084334','49384334');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084339','10084339');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084356','10084356');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084376','49384376');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084378','49384378');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084420','49384420');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084422','49384422');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084428','49384428');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084429','49384429');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084434','10084434');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084455','10084455');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084463','10084463');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084465','49384465');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084469','49384469');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084471','49384471');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084475','49384475');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084476','49384476');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084477','10084477');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084483','49384483');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084492','49384492');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084499','49384499');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084500','49384500');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084501','49384501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084503','49384503');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084505','49384505');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084523','49384523');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084527','49384527');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084528','49384528');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084531','49384531');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084532','49384532');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084541','49384541');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084543','49384543');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084554','49384554');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084560','10084560');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084566','49384566');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084571','10084571');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084584','49384584');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084592','49384592');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084593','49384593');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084594','49384594');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084597','50584597');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084605','10084605');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084609','49384609');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084613','49384613');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084616','49384616');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084619','49384619');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084622','49384622');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084633','49384633');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084635','10084635');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084637','49384637');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084639','10084639');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084643','49384643');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084644','49384644');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084645','49384645');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084647','49384647');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084665','49384665');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084691','49384691');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084703','49384703');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084706','49384706');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084714','49384714');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084717','49384717');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084724','50584724');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084725','49384725');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084728','49384728');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084734','49384734');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084751','49384751');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084769','49384769');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084770','49384770');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084772','49384772');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084773','49384773');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084782','49384782');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084785','49384785');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084787','49384787');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084795','49384795');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084797','49384797');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084801','49384801');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084803','49384803');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084804','49384804');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084805','49384805');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084806','49384806');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084809','49384809');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084811','49384811');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084814','49384814');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084818','49384818');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084821','49384821');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084823','49384823');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084839','49384839');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084841','10084841');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084843','49384843');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084845','49384845');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084846','10084846');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084848','49384848');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084852','10084852');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084858','49384858');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084861','10084861');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084868','49384868');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084874','49384874');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084876','49384876');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084882','49384882');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084883','49384883');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084900','49384900');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084907','49384907');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084910','49384910');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084912','49384912');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084916','49384916');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084918','49384918');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084923','49384923');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084925','49384925');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084928','50584928');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084929','49384929');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084933','49384933');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084936','49384936');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084948','49384948');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084949','49384949');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084951','49384951');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084964','49384964');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084980','49384980');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084985','49384985');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10084986','49384986');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085002','49385002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085023','49385023');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085027','49385027');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085028','49385028');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085029','49385029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085039','49385039');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085040','49385040');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085042','49385042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085046','49385046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085048','10085048');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085057','49385057');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085058','49385058');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085060','49385060');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085062','49385062');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085064','49385064');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085068','49385068');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085069','49385069');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085074','50585074');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085080','49385080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085085','49385085');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085087','49385087');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085088','49385088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085093','49385093');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085098','49385098');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085104','49385104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085108','49385108');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085132','49385132');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085133','49385133');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085138','49385138');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085139','49385139');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085143','49385143');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085146','10085146');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085147','50585147');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085148','49385148');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085150','49385150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085165','49385165');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085169','49385169');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085179','49385179');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085188','49385188');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085199','49385199');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085209','10085209');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085210','49385210');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085212','49385212');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085213','49385213');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085216','49385216');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085221','49385221');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085228','49385228');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085230','49385230');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085233','10085233');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085235','49385235');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085238','10085238');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085243','49385243');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085244','49385244');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085245','49385245');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085246','49385246');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085249','49385249');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085270','49385270');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085277','49385277');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085278','49385278');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085291','49385291');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085292','49385292');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085309','49385309');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085320','49385320');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085323','10085323');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085325','49385325');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085330','49385330');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085337','49385337');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085341','49385341');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085342','49385342');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085343','49385343');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085357','49385357');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085359','49385359');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085377','49385377');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085385','49385385');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085388','49385388');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085390','49385390');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085394','49385394');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085395','49385395');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085397','49385397');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085406','10085406');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085419','49385419');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085430','49385430');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085450','49385450');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085452','49385452');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085464','49385464');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085474','49385474');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085475','49385475');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085490','49385490');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085496','49385496');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085497','49385497');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085498','49385498');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085499','10085499');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085504','49385504');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085512','49385512');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085520','49385520');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085521','49385521');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085522','49385522');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085523','49385523');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085524','49385524');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085525','49385525');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085528','49385528');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085529','49385529');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085532','49385532');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085534','49385534');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085544','49385544');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085558','49385558');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085574','49385574');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085575','49385575');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085577','49385577');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085600','10085600');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085601','49385601');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085604','49385604');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085605','49385605');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085609','49385609');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085616','49385616');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085619','49385619');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085620','49385620');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085653','49385653');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085656','49385656');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085661','49385661');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085664','49385664');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085679','49385679');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085684','10085684');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085688','49385688');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085694','49385694');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085712','49385712');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085714','49385714');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085732','49385732');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085733','49385733');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085735','49385735');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085736','49385736');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085745','49385745');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085758','49385758');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085762','49385762');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085763','49385763');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085774','49385774');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085778','49385778');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085781','10085781');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085782','49385782');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085797','49385797');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085821','49385821');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085828','49385828');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085833','49385833');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085834','49385834');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085844','49385844');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085881','49385881');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085886','10085886');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085889','10085889');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085898','49385898');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085905','49385905');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085906','49385906');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085907','49385907');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085908','49385908');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085919','49385919');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085923','49385923');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085925','49385925');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085930','49385930');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085934','49385934');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085947','49385947');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085976','49385976');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085981','49385981');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085988','49385988');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085989','49385989');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085991','49385991');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085992','49385992');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085994','49385994');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10085995','49385995');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086010','49386010');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086040','10086040');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086046','49386046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086080','49386080');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086083','49386083');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086085','49386085');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086113','49386113');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086125','49386125');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086126','10086126');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086135','49386135');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086145','49386145');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086147','10086147');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086150','49386150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086152','49386152');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086155','49386155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086162','10086162');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086163','10086163');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086168','49386168');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086179','49386179');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086195','49386195');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086196','49386196');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086198','49386198');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086208','49386208');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086225','49386225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086228','10086228');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086231','49386231');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086239','49386239');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086246','49386246');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086247','49386247');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086265','49386265');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086298','49386298');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086299','49386299');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086305','49386305');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086312','49386312');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086325','49386325');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086339','49386339');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086340','49386340');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086342','49386342');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086351','49386351');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086353','49386353');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086359','49386359');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086361','49386361');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086362','49386362');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086364','49386364');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086370','49386370');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086371','49386371');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086378','49386378');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086400','49386400');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086403','49386403');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086414','49386414');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086418','49386418');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086419','49386419');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086427','49386427');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086430','49386430');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086431','49386431');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086441','10086441');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086452','49386452');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086455','49386455');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086456','49386456');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086457','49386457');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086480','49386480');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086482','49386482');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086485','49386485');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086487','49386487');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086488','10086488');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086510','50586510');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086540','49386540');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086554','49386554');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086560','49386560');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086561','49386561');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086563','49386563');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086564','49386564');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086579','50586579');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086615','49386615');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086627','49386627');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086653','49386653');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086666','49386666');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086667','49386667');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086669','49386669');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086671','49386671');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086675','49386675');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086676','49386676');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086677','49386677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086678','49386678');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086680','49386680');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086682','49386682');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086683','49386683');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086684','49386684');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086686','49386686');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086689','49386689');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086690','49386690');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086693','49386693');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086695','49386695');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086700','49386700');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086701','49386701');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086710','49386710');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086734','49386734');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086752','10086752');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086758','49386758');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086850','49386850');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086867','49386867');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086871','50586871');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086916','49386916');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086981','10086981');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10086990','49386990');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087032','49387032');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087033','49387033');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087059','10087059');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087123','50587123');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087351','49387351');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087401','49387401');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087583','10087583');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087600','49387600');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087646','10087646');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087797','49387797');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087861','49387861');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087875','49387875');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087885','49387885');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10087979','49387979');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088002','49388002');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088054','49388054');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088114','10088114');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088115','10088115');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088150','10088150');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088221','49388221');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088225','49388225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088234','49388234');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088249','10088249');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088257','49388257');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088259','49388259');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088270','49388270');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088282','49388282');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088343','49388343');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088359','50588359');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088373','49388373');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088416','49388416');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088512','49388512');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088517','49388517');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088520','49388520');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088526','49388526');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088529','50588529');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088530','49388530');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088542','49388542');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088545','10088545');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088556','49388556');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088560','10088560');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088567','49388567');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088590','49388590');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088620','49388620');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088683','49388683');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088766','49388766');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088779','49388779');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088828','49388828');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088889','49388889');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088890','49388890');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088891','49388891');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10088978','10088978');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089036','49389036');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089038','49389038');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089039','49389039');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089040','49389040');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089087','10089087');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089129','10089129');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089155','49389155');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089182','49389182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089205','49389205');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089230','49389230');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089286','49389286');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089327','49389327');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089328','49389328');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089330','49389330');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089331','49389331');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089332','49389332');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089341','49389341');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089342','49389342');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089344','49389344');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089349','49389349');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089364','49389364');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089372','49389372');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089374','49389374');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089376','49389376');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089377','49389377');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089379','49389379');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089380','49389380');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089382','49389382');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089390','49389390');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089410','10089410');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089461','49389461');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089488','49389488');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089505','49389505');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089544','49389544');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089557','10089557');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089630','49389630');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089642','49389642');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089643','49389643');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089661','49389661');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089676','49389676');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089696','49389696');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089702','10089702');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089709','49389709');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089711','49389711');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089724','10089724');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089733','49389733');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089786','10089786');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089823','10089823');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089878','49389878');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089897','49389897');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089917','49389917');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089929','49389929');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089937','49389937');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089945','49389945');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089948','49389948');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089949','49389949');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089955','10089955');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089960','50589960');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089968','49389968');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089972','49389972');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089974','49389974');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089975','49389975');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089977','50589977');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089989','49389989');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089990','49389990');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089993','10089993');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089997','49389997');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10089999','49389999');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090007','49390007');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090026','49390026');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090029','49390029');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090036','10090036');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090042','49390042');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090044','49390044');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090045','49390045');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090046','49390046');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090053','49390053');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090072','49390072');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090078','49390078');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090088','50590088');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090100','49390100');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090101','49390101');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090104','49390104');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090107','10090107');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090110','49390110');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090112','49390112');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090116','10090116');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090126','49390126');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090144','49390144');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090172','50590172');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090175','49390175');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090181','49390181');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090182','49390182');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090183','49390183');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090187','49390187');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090189','49390189');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090193','49390193');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090196','10090196');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090210','49390210');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090214','49390214');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090218','49390218');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090219','49390219');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090222','49390222');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090223','49390223');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090225','49390225');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090227','49390227');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090228','49390228');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090233','49390233');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090235','49390235');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090244','49390244');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090246','49390246');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090259','49390259');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090260','49390260');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090267','49390267');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090272','49390272');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090276','10090276');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090284','49390284');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090285','49390285');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090286','49390286');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090287','10090287');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090300','49390300');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090310','49390310');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090312','49390312');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090314','49390314');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090315','10090315');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090322','49390322');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090328','49390328');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090329','49390329');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090330','49390330');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090331','49390331');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090335','49390335');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090336','49390336');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090337','49390337');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090342','49390342');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090346','49390346');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090347','49390347');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090357','49390357');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090363','10090363');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090364','49390364');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090367','49390367');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090376','10090376');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090380','49390380');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090381','49390381');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090382','49390382');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090384','10090384');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090385','50590385');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090386','49390386');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090391','50590391');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090394','49390394');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090405','10090405');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090412','49390412');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090418','49390418');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090428','49390428');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090432','10090432');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090435','49390435');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090436','50590436');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090438','49390438');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090439','49390439');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090442','49390442');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090445','49390445');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090462','49390462');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090465','49390465');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090466','49390466');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090469','49390469');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090478','49390478');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090479','49390479');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090501','49390501');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090502','49390502');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090508','49390508');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090510','49390510');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090511','49390511');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090512','49390512');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090529','50590529');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090551','10090551');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090562','49390562');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090585','49390585');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090588','49390588');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090589','49390589');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090593','49390593');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090609','49390609');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090618','49390618');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090621','49390621');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090624','49390624');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090627','49390627');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090631','49390631');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090633','49390633');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090634','49390634');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090635','49390635');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090643','49390643');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090648','49390648');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090649','49390649');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090650','50590650');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090652','49390652');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090656','49390656');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090665','10090665');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090667','49390667');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090671','49390671');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090672','49390672');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090677','49390677');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090678','49390678');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090694','49390694');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090716','10090716');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090718','49390718');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090729','50590729');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090730','49390730');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090732','49390732');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090733','49390733');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090734','49390734');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090735','49390735');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090736','49390736');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090740','10090740');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090741','49390741');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090746','49390746');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090749','10090749');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090751','50590751');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090753','49390753');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090757','49390757');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090761','49390761');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090791','49390791');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090799','50590799');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090815','49390815');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090816','49390816');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090817','49390817');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090819','10090819');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090825','49390825');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090826','49390826');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090842','10090842');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090861','49390861');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090863','49390863');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090864','49390864');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090865','49390865');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090867','49390867');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090868','49390868');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090874','49390874');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090875','49390875');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090876','50590876');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090877','49390877');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090881','49390881');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090882','49390882');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090886','49390886');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090889','49390889');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090890','50590890');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090897','49390897');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090900','10090900');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090901','50590901');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090903','49390903');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090904','49390904');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090905','50590905');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090912','49390912');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090922','49390922');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090923','49390923');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090928','49390928');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090937','49390937');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090938','49390938');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090939','49390939');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090940','49390940');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090941','49390941');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090942','49390942');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090943','49390943');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090944','49390944');
Insert into AP.MAPPA_CID (OLD_CID,NEW_CID) values ('10090949','50590949');

show errors

commit;

quit
