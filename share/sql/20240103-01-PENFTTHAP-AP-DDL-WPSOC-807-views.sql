set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE OR REPLACE VIEW V_AP_PERMESSI_BUILDING AS 
select p.id_sistema id_building,
	   p.id id_permesso,
       pt."privatePermitCategory" categoria,
       pt."privatePermitType" tipo,
	   p.stato_corrente stato,
       p.data_ult_varstat DATA_ULTIMA_VARIAZIONE,
       to_char(pt."requestDate", 'yyyy-mm-dd') data_inoltro_permesso,
       to_char(pt."authorizationDate", 'yyyy-mm-dd') data_permesso_ottenuto,
       dts.valore "customerId",
       dts2.valore "contractId",
       pt."projectId" "projectId",
       pt."maker",
       pt."teamId",
       pt."teamName",
       pt."subContractCode",
       pt."subContractName"
from  ap_art.v_attivita p
    join ap_art.pt_permesso_building pt on pt.id_attivita = p.id
    join ap_art.v_dt_sistemi dts on dts.id_Sistema = p.id_Sistema and dts.nome = 'customerId'
    join ap_art.v_dt_sistemi dts2 on dts2.id_Sistema = p.id_Sistema and dts2.nome = 'contractId'
where p.Nome_Tipo_Attivita = 'PERMESSO_BUILDING';

GRANT SELECT ON V_AP_PERMESSI_BUILDING TO AP_RPT with grant option;
GRANT SELECT ON V_AP_PERMESSI_BUILDING TO CLIENT_CORE_RPT with grant option;
GRANT SELECT ON V_AP_PERMESSI_BUILDING TO CLIENT_WORKS_RPT;

show errors

quit
