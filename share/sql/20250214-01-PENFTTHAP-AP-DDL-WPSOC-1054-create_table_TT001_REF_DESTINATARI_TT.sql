set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000
set SERVEROUT off

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE TABLE "AP"."TT001_REF_DESTINATARI_TT" (	
    "RO" VARCHAR2(100) NOT NULL ENABLE, 
    "AOR" VARCHAR2(100) NOT NULL ENABLE, 
	"Applicativo" VARCHAR2(100) NOT NULL ENABLE, 
	"TO" VARCHAR2(100), 
	"CC" VARCHAR2(100), 
	 PRIMARY KEY ("RO", "AOR", "Applicativo")
);
GRANT SELECT ON "AP"."TT001_REF_DESTINATARI_TT" TO AP_ART;
show errors

quit
