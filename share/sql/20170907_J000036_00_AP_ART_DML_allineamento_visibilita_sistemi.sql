set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

insert into gruppi
select seq_gruppi.nextval
  , es.descrizione
  , es.amministratore
  , es.root
  , es.morto
  , es.nome
  , es.privato
  , es.autogenerato
from (
select --seq_gruppi.nextval
  --, 
  distinct 'PROJECT4RPB_'||replace(v.descrizione,'-','_') DESCRIZIONE
  , null AMMINISTRATORE
  , null ROOT
  , null MORTO
  ,'PROJECT4RPB_'||replace(v.descrizione,'-','_') NOME
  , null PRIVATO
  , 'Y' AUTOGENERATO
From v_sistemi v
where v.tipo = 'PROJECT'
and not exists (
  select 1
  from gruppi g
  where g.nome = 'PROJECT4RPB_'||replace(v.descrizione,'-','_')
)
) es
;

-- assegna i gruppi PROJECT al relativo sistema
insert into permission_sistemi
select distinct s.id, (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT4RPB_'||replace(s.descrizione,'-','_')
  )
from v_sistemi s
where s.tipo = 'PROJECT'
and not exists(
  select 1
  from permission_sistemi ps
  where ps.id_sistema = s.id
  and PS.ID_GRUPPO_ABILITATO = (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT4RPB_'||replace(s.descrizione,'-','_')
  )
);

-- assegna i gruppi PROJECT al sistema BUILDING
insert into permission_sistemi
select distinct s.id, (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT4RPB_'||replace((select sa.descrizione From sistemi_relazione sr join V_sistemi sa on sa.id = sr.a where sr.b = s.id),'-','_')
  )
from v_sistemi s
where s.tipo = 'BUILDING'
and not exists(
  select 1
  from permission_sistemi ps
  where ps.id_sistema = s.id
  and PS.ID_GRUPPO_ABILITATO = (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT4RPB_'||replace((select sa.descrizione From sistemi_relazione sr join V_sistemi sa on sa.id = sr.a where sr.b = s.id),'-','_')
  )
);

commit;

quit
