set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000
set SERVEROUT off

whenever oserror exit 1 rollback;
whenever sqlerror exit 2 rollback;

create or replace view V_TIM_5G_SITES as
select v.descrizione, v.stato_corrente, v.data_creazione, v.data_ult_varstat,pt.*
from ap_art.pt_lc06 pt
    join ap_art.v_attivita v on v.id = pt.id_attivita
where pt."customerId" = 'TIM';

grant select on V_TIM_5G_SITES to CLIENT_LEGACY ;

show errors

quit
