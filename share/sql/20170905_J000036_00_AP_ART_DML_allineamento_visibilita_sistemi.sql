set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

insert into gruppi
select seq_gruppi.nextval
  , es.descrizione
  , es.amministratore
  , es.root
  , es.morto
  , es.nome
  , es.privato
  , es.autogenerato
from (
select --seq_gruppi.nextval
  --, 
  distinct 'PROJECT_'||replace(v.descrizione,'-','_') DESCRIZIONE
  , null AMMINISTRATORE
  , null ROOT
  , null MORTO
  ,'PROJECT_'||replace(v.descrizione,'-','_') NOME
  , null PRIVATO
  , 'Y' AUTOGENERATO
From v_sistemi v
where v.tipo = 'PROJECT'
and not exists (
  select 1
  from gruppi g
  where g.nome = 'PROJECT_'||replace(v.descrizione,'-','_')
)
) es
;

-- assegna i gruppi PROJECT al relativo sistema
insert into permission_sistemi
select distinct s.id, (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT_'||replace(s.descrizione,'-','_')
  )
from v_sistemi s
where s.tipo = 'PROJECT'
and not exists(
  select 1
  from permission_sistemi ps
  where ps.id_sistema = s.id
  and PS.ID_GRUPPO_ABILITATO = (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT_'||replace(s.descrizione,'-','_')
  )
);

-- rimuove il gruppo USER ai sistemi PROJECT
delete permission_sistemi
where (ID_SISTEMA, id_gruppo_abilitato) in (
select ps.ID_SISTEMA, ps.id_gruppo_abilitato
from v_sistemi s
  join permission_sistemi ps on ps.id_sistema = s.id
  join gruppi g on g.id_gruppo = ps.id_gruppo_abilitato
where s.tipo = 'PROJECT'
  and g.nome = 'USER'
  );

-- assegna i gruppi PROJECT al sistema AP
insert into permission_sistemi
select distinct s.id, (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT_'||replace((select sa.descrizione From sistemi_relazione sr join V_sistemi sa on sa.id = sr.a where sr.b = s.id),'-','_')
  )
from v_sistemi s
where s.tipo = 'AP'
and not exists(
  select 1
  from permission_sistemi ps
  where ps.id_sistema = s.id
  and PS.ID_GRUPPO_ABILITATO = (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT_'||replace((select sa.descrizione From sistemi_relazione sr join V_sistemi sa on sa.id = sr.a where sr.b = s.id),'-','_')
  )
);

-- rimuove il gruppo USER ai sistemi AP
delete permission_sistemi
where (ID_SISTEMA, id_gruppo_abilitato) in (
select ps.ID_SISTEMA, ps.id_gruppo_abilitato
from v_sistemi s
  join permission_sistemi ps on ps.id_sistema = s.id
  join gruppi g on g.id_gruppo = ps.id_gruppo_abilitato
where s.tipo = 'AP'
  and g.nome = 'USER'
  );

-- assegna i gruppi PROJECT al sistema BUILDING
insert into permission_sistemi
select distinct s.id, (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT_'||replace((select sa.descrizione From sistemi_relazione sr join V_sistemi sa on sa.id = sr.a where sr.b = s.id),'-','_')
  )
from v_sistemi s
where s.tipo = 'BUILDING'
and not exists(
  select 1
  from permission_sistemi ps
  where ps.id_sistema = s.id
  and PS.ID_GRUPPO_ABILITATO = (
    select g.id_gruppo
    from gruppi g 
    where g.nome = 'PROJECT_'||replace((select sa.descrizione From sistemi_relazione sr join V_sistemi sa on sa.id = sr.a where sr.b = s.id),'-','_')
  )
);

-- rimuove il gruppo USER ai sistemi BUILDING
delete permission_sistemi
where (ID_SISTEMA, id_gruppo_abilitato) in (
select ps.ID_SISTEMA, ps.id_gruppo_abilitato
from v_sistemi s
  join permission_sistemi ps on ps.id_sistema = s.id
  join gruppi g on g.id_gruppo = ps.id_gruppo_abilitato
where s.tipo = 'BUILDING'
  and g.nome = 'USER'
  );

commit;

quit
