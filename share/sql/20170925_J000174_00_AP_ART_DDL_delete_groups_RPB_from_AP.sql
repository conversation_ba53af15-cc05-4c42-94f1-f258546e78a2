
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

delete permission_sistemi
where (id_sistema, id_gruppo_abilitato) in (
  select s.id, (select g.id_gruppo from gruppi g where g.nome = 'PROJECT4RPB_'||dts.valore||'_'||dts1.valore||'_'||dts2.valore)
  from v_sistemi s
    join v_dt_sistemi dts on dts.id_sistema = s.id
    join v_dt_sistemi dts1 on dts1.id_sistema = s.id
    join v_dt_sistemi dts2 on dts2.id_sistema = s.id
  where s.tipo = 'AP'
  and dts.nome = 'customerId'
  and dts1.nome = 'contractId'
  and dts2.nome = 'projectId'
);

commit;

quit
