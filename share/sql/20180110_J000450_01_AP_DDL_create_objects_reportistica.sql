set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_AP AS 
  select a.id,
	   ap_art.property_value(a.id, 'projectId') projectId,
	   ap_art.property_value(a.id, 'permitsAreaId') id_area_permesso,
	   ap_art.property_value(a.id, 'name') nome_ap,
	   a.stato_corrente stato,
	   Sa.Login_Operatore nome_creatore,
	   sa.data_esecuzione data_creazione,
	   A.Data_Ult_Varstat data_ultima_modifica,
     pt."forecastStartDate" DATA_INIZIO_FORECASTING,
     pt."forecastEndDate" DATA_FINE_FORECASTING,
     (
      select min(ptl."expectedAuthorizationDate")
      from ap_art.a_padre_a_figlio apaf
        join ap_art.pt_permesso_lavori ptl on ptl.id_attivita = apaf.figlio
      where apaf.padre = a.id
     ) MIN_DATA_INIZIO_PERMESSO_RICH,
     (
      select max(ptl."expectedEndAuthorizationDate")
      from ap_art.a_padre_a_figlio apaf
        join ap_art.pt_permesso_lavori ptl on ptl.id_attivita = apaf.figlio
      where apaf.padre = a.id
     ) MAX_DATA_FINE_PERMESSO_RICH,
     (
      select min(ptl."authorizationDate")
      from ap_art.a_padre_a_figlio apaf
        join ap_art.pt_permesso_lavori ptl on ptl.id_attivita = apaf.figlio
      where apaf.padre = a.id
     ) MIN_DATA_INIZIO_PERMESSO_OTT,
     (
      select max(ptl."endAuthorizationDate")
      from ap_art.a_padre_a_figlio apaf
        join ap_art.pt_permesso_lavori ptl on ptl.id_attivita = apaf.figlio
      where apaf.padre = a.id
     ) MAX_DATA_FINE_PERMESSO_OTT
from  ap_art.v_attivita a
	  inner join  ap_art.V_Storia_Attivita sa on sa.id_attivita = a.id
    inner join ap_Art.pt_ap_lc pt on pt.id_attivita = a.id
where A.Nome_Tipo_Attivita = 'AP_LC'
  and Sa.Azione = 'APERTURA';

quit
