set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

CREATE TABLE "AP"."MAPPA_CDL" 
   (	"OLD_CDL" VARCHAR2(6 CHAR) NOT NULL ENABLE, 
	"NEW_CDL" VARCHAR2(6 CHAR) NOT NULL ENABLE, 
	 PRIMARY KEY ("OLD_CDL", "NEW_CDL")
   );

grant select on MAPPA_CDL to AP_ART;

CREATE TABLE "AP"."MAPPA_CID"
   (    "OLD_CID" VARCHAR2(8 CHAR) NOT NULL ENABLE,
    "NEW_CID" VARCHAR2(8 CHAR) NOT NULL ENABLE,
    PRIMARY KEY ("OLD_CID", "NEW_CID")
   );

grant select on MAPPA_CID to AP_ART;

show errors

quit
