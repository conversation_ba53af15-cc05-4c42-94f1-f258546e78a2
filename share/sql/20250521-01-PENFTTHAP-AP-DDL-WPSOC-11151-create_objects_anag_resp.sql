set line 1000
set autocommit off
set echo on
set SE<PERSON><PERSON><PERSON><PERSON> on  size 1000000
set SE<PERSON><PERSON><PERSON>UT off

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE OR REPLACE VIEW V_R_ANAG_PERSONE_RESP AS
SELECT
    C.SOC RESP_SOC,
    A.CIDSAP_RESP_UNITA_ORGANIZ RESP_CIDSAP,
    C.cognome RESP_COGNOME,
    C.nome RESP_NOME,
    a.soc USER_SOC,
    a.cidsap USER_CIDSAP,
    a.cognome USER_COGNOME,
    a.nome USER_NOME
FROM ANAG.PERSONE@service_anag A
    LEFT JOIN ANAG.PERSONE@service_anag C ON C.SOC = A.SOC_RESP_UNITA_ORGANIZ AND C.CID = A.CID_RESP_UNITA_ORGANIZ AND SYSDATE BETWEEN C.DATA_I_VALID AND C.DATA_F_VALID and c.data_cessazione is null
WHERE 1=1
     AND SYSDATE BETWEEN A.DATA_I_VALID AND A.DATA_F_VALID
     and a.data_cessazione is null
;  

create or replace view V_ANAG_PERSONE_RESP as
select *
from V_R_ANAG_PERSONE_RESP;

CREATE MATERIALIZED VIEW MV_ANAG_PERSONE_RESP
  BUILD IMMEDIATE
  USING INDEX
  REFRESH COMPLETE ON DEMAND as
select * from V_ANAG_PERSONE_RESP;

GRANT select on MV_ANAG_PERSONE_RESP to ap_art;
CREATE UNIQUE INDEX MV_ANAG_PERSONE_RESP_IDX01 ON MV_ANAG_PERSONE_RESP (USER_CIDSAP) tablespace TT_IDX;

COMMENT ON MATERIALIZED VIEW MV_ANAG_PERSONE_RESP  IS 'materializzazione da vista V_ANAG_PERSONE_RESP';

begin dbms_refresh.make('MV_ANAG_PERSONE_RESP','MV_ANAG_PERSONE_RESP',NULL, NULL); end;
/

BEGIN
  dbms_scheduler.create_job(
    JOB_NAME=>'JOB_REFRESH_MV_ANAG_PERSONE_RESP',
    JOB_TYPE=>'PLSQL_BLOCK',
    AUTO_DROP=>FALSE,
    JOB_ACTION=>'DBMS_SNAPSHOT.REFRESH(''"AP"."MV_ANAG_PERSONE_RESP"'',''C'');',
    START_DATE=> systimestamp,
    REPEAT_INTERVAL=>'freq=daily;byhour=1; byminute=0;bysecond=0',
    ENABLED=>TRUE,
    COMMENTS=>'Job refresh MV MV_ANAG_PERSONE_RESP'
);
END;
/

show errors

quit
