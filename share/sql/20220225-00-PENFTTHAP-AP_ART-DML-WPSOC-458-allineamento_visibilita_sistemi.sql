set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

-- aggiunge i gruppi SERVICE ai sistemi assegnati al relativo fornitore e causa un trigger di aggiornamento su ELK
create table tmp_permission_sistemi (
    id_sistema number(10,0),
    id_gruppo_abilitato number(10,0)
);

insert into tmp_permission_sistemi
select distinct
        a.id_sistema,
        g.id_gruppo id_gruppo_abilitato
from    v_dta dta
join    v_attivita a
on      a.id = dta.id_attivita
join    gruppi g
on      g.nome = 'SERVICE_' || dta.valore
where   dta.nome = 'subContractCode'
and not exists (
    select  1
    from    permission_sistemi ps
    where   ps.id_sistema = a.id_sistema
    and     ps.id_gruppo_abilitato = g.id_gruppo
);


insert into permission_sistemi
select distinct id_sistema, id_gruppo_abilitato
from tmp_permission_sistemi;

update attivita
set descrizione = descrizione
where id_sistema in ( select distinct id_sistema from tmp_permission_sistemi );

drop table tmp_permission_sistemi;

commit;

show errors

quit
