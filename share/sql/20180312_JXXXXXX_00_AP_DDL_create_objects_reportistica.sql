set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_BUILDINGS AS 
  with buildings as (
  select  id id_building,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'projectId'
		  ) projectId,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'permitsAreaId'
		  ) permitsAreaId,
		  (
		  select dts.valore
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'completeAddress'
		  ) completeAddress,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'privateUI'
		  ) privateUI,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'smallBusinessUI'
		  ) smallBusinessUI,
		  (
		  select dts.valore
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'buildingSinfoId'
		  ) buildingSinfoId,
		  (
		  select dts.valore
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'verticalConstruction'
		  ) verticalConstruction
  from  ap_art.v_sistemi s
  where s.tipo = 'BUILDING'
  and s.data_sospensione is null
  and s.data_dismissione is null
),
att as (
select a.id,
	   ap_art.property_value(a.id, 'name') nome_ap,
	   ap_art.property_value(a.id, 'permitsAreaId') id_area_permesso
from  ap_art.v_attivita a
where a.Nome_Tipo_Attivita = 'AP_LC'
)
select b.projectid,
	   a.id_area_permesso,
	   a.nome_ap,
	   b.id_building id_building,
	   b.buildingSinfoId id_building_sinfo,
	   b.completeAddress indirizzo,
	   nvl(b.smallBusinessUI, 0) + nvl(b.privateUI, 0) tot_ui,
	   b.verticalConstruction stato_del_verticale
from buildings b
	  left join att a on a.id_area_permesso = b.permitsAreaId
;

quit
