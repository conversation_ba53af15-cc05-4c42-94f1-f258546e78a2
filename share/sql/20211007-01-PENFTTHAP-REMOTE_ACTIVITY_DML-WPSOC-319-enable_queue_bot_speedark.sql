
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

INSERT
INTO
  RA_SERVICE
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'BOT',
    'Gestione BOT',
    1
  );

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'SPEEDARK',
    'Gestione Speedark',
    1
  );

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'CUSTOMER_PROJECT',
    'Gestione Customer Project',
    1
  );

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'CUSTOMER_PROJECT',
    'REQUEST_DOCUMENTATION::NE',
    'Network: richiesta esecuzione BOT per NordEst',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'CUSTOMER_PROJECT'
        and     EVENT = 'REQUEST_DOCUMENTATION::NE'
        and     ENABLED = 1
    ),
    'BOT',
    'SPEEDARK',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'CUSTOMER_PROJECT',
    'REQUEST_DOCUMENTATION::NO',
    'Network: richiesta esecuzione BOT per NordOvest',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'CUSTOMER_PROJECT'
        and     EVENT = 'REQUEST_DOCUMENTATION::NO'
        and     ENABLED = 1
    ),
    'BOT',
    'SPEEDARK',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'CUSTOMER_PROJECT',
    'REQUEST_DOCUMENTATION::CE',
    'Network: richiesta esecuzione BOT per Centro',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'CUSTOMER_PROJECT'
        and     EVENT = 'REQUEST_DOCUMENTATION::CE'
        and     ENABLED = 1
    ),
    'BOT',
    'SPEEDARK',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'CUSTOMER_PROJECT',
    'REQUEST_DOCUMENTATION::SU',
    'Network: richiesta esecuzione BOT per Sud',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'CUSTOMER_PROJECT'
        and     EVENT = 'REQUEST_DOCUMENTATION::SU'
        and     ENABLED = 1
    ),
    'BOT',
    'SPEEDARK',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    '___LOCALHOST___',
    'CUSTOMER_PROJECT',
    'TIMEOUT_REQUEST_DOCUMENTATION',
    'Customer project: gestione timeout documentazione',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = '___LOCALHOST___'
        and     SOURCE_CONTEXT = 'CUSTOMER_PROJECT'
        and     EVENT = 'TIMEOUT_REQUEST_DOCUMENTATION'
        and     ENABLED = 1
    ),
    '___LOCALHOST___',
    'CUSTOMER_PROJECT',
    1
);

commit;

quit
