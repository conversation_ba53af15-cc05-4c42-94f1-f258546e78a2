set line 1000
set autocommit off
set echo on
set SERVEROUT off

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

-- Migrazione non condizionale (PTE e Manutenzioni)
update dati_Tecnici dt
set dt.descrizione = nvl((select m.new_cdl from ap.mappa_cdl m where m.old_cdl = dt.descrizione), dt.descrizione)
where dt.id_Sistema in (
    select  a.id_sistema
    from    v_attivita a
    join    v_dta rt
    on      rt.id_attivita = a.id
    and     rt.nome = 'targetAsset'
    where   a.nome_tipo_attivita = 'PERMESSO_BUILDING'
    and     rt.valore = 'PTE'
) and dt.id_tipo_Dato_Tecnico = (select id_tipo_Dato_Tecnico from tipi_Dati_Tecnici where nome = 'workingGroupCode')
;

-- Migrazione condizionale (Network)
update dati_Tecnici dt
set dt.descrizione = nvl((select m.new_cdl from ap.mappa_cdl m where m.old_cdl = dt.descrizione), dt.descrizione)
where dt.id_Sistema in (
    select  a.id_sistema
    from    v_attivita a
    join    v_dta rt
    on      rt.id_attivita = a.id
    and     rt.nome = 'targetAsset'
    join    v_dta nid
    on      nid.id_attivita = a.id
    and     nid.nome = 'projectId'
    where   a.nome_tipo_attivita = 'PERMESSO_BUILDING'
    and     rt.valore = 'Network'
    and     exists ( select 1 from ap.network_da_migrare where "networkId" = nid.valore )
) and dt.id_tipo_Dato_Tecnico = (select id_tipo_Dato_Tecnico from tipi_Dati_Tecnici where nome = 'workingGroupCode')
;

update dati_Tecnici dt
set dt.descrizione = nvl((select m.new_cdl from ap.mappa_cdl m where m.old_cdl = dt.descrizione), dt.descrizione)
where dt.id_Sistema in (
    select  a.id_sistema
    from    v_attivita a
    join    v_dta rt
    on      rt.id_attivita = a.id
    and     rt.nome = 'targetAsset'
    join    v_dta nid
    on      nid.id_attivita = a.id
    and     nid.nome = 'projectId'
    where   a.nome_tipo_attivita = 'PERMESSO_LAVORI'
    and     rt.valore in ( 'Network', 'NetworkFibercop' )
    and     exists ( select 1 from ap.network_da_migrare where "networkId" = nid.valore )
) and dt.id_tipo_Dato_Tecnico = (select id_tipo_Dato_Tecnico from tipi_Dati_Tecnici where nome = 'workingGroupCode')
;

show errors

commit;

quit
