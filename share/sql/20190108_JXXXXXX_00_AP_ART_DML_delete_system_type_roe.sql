
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

delete tipi_sistema_tipi_Dati_Tecnici
where id_tipo_sistema = (select id_tipo_sistema from tipi_sistema where nome_Tipo_sistema = 'ROE');

delete tipi_sistema
where  nome_Tipo_sistema = 'ROE';

delete tipi_Dati_tecnici
where nome = 'roeId';

commit;

quit
