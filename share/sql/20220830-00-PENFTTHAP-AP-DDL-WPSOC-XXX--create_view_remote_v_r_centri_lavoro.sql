set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_r_centri_lavoro as
SELECT '49' "companyCode",
	substr(ccosto, 1, 2) || substr(ccosto, length(ccosto) - 3) "id",
        descr "name"
FROM
    anag.ccosto@service_anag
WHERE
    (ccosto like '49%66__' or ccosto like '49%64__')
    and tipo = '4'
;

grant select on v_r_centri_lavoro to ap_art;

quit
