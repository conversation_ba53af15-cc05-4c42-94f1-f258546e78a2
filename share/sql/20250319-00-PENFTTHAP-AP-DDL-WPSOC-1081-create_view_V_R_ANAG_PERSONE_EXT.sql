set line 1000
set autocommit off
set echo on
set SERVEROUT on  size 1000000
set SERVEROUT off

whenever oser<PERSON>r exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE OR REPLACE VIEW V_R_ANAG_PERSONE_EXT AS 
select u.userid, p.partner, p.ragione_sociale, pe.email
from anag.persone_esterne@service_anag pe
    join anag.utenti@service_anag u on u.cid = pe.cid
    join anag.partners@service_anag p on p.partner = pe.partner
where pe.data_f_valid > sysdate;

show errors

quit
