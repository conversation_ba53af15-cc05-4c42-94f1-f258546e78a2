set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

declare
     sq_grants varchar(32000) := '';
     c_id_tipo_attivita ap_art.tipi_attivita.id_tipo_attivita%type;
     type varray_varchar is varying array(400) of ap_art.tipi_attivita.nome_tipo_attivita%type;
     var_varray_varchar varray_varchar;
     
 begin
 
    var_varray_varchar  := varray_varchar(
        'PERMESSO_LAVORI',
        'PERMESSO_BUILDING'
    );

    for ta in 1 .. var_varray_varchar.count loop

        --MS
        select id_tipo_attivita into c_id_tipo_attivita from ap_art.tipi_attivita where nome_tipo_attivita = var_varray_varchar(ta);
        
        sq_grants := 'grant select on tipi_attivita to ap_rpt with grant option';
        
        execute immediate sq_grants;

        sq_grants := 'grant select on v_xt_'||c_id_tipo_attivita||' to ap_rpt with grant option';
        
        execute immediate sq_grants;

    end loop;

    sq_grants := 'grant select on v_dt_sistemi to ap_rpt with grant option';

    execute immediate sq_grants;

 end;
/

show errors

quit
