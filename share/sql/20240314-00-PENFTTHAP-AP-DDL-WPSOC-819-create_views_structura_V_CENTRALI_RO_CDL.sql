set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_R_CENTRALI_RO_CDL AS
select distinct w.tipinterv tipo_intervento, c.liv1 RO, c.centrale "centralId", w.cdl
from amelia.centrali@service_wfm c
    join amelia.config_cdl@service_wfm w on w.centrale = c.centrale
where w.tipinterv in ('YR','YS','YA','YB');

create or replace view V_CENTRALI_RO_CDL as
select *
from V_R_CENTRALI_RO_CDL;

drop materialized view MV_CENTRALI_RO_CDL;

create materialized view MV_CENTRALI_RO_CDL refresh complete as
select *
from V_CENTRALI_RO_CDL;

grant select on MV_CENTRALI_RO_CDL to AP_ART;

BEGIN
  dbms_scheduler.create_job(
    JOB_NAME=>'JOB_REFRESH_MV_CENTRALI_RO_CDL',
    JOB_TYPE=>'PLSQL_BLOCK',
    AUTO_DROP=>FALSE,
    JOB_ACTION=>'DBMS_SNAPSHOT.REFRESH(''"AP"."MV_CENTRALI_RO_CDL"'',''C'');',
    START_DATE=> systimestamp,
    REPEAT_INTERVAL=>'freq=daily;byhour=1; byminute=30;bysecond=0',
    ENABLED=>TRUE,
    COMMENTS=>'Job refresh MV MV_CENTRALI_RO_CDL'
);
END;
/

show errors;

quit

SQL*Plus: Release ********.0 - Production on Gio Mar 14 10:57:15 2024
Version *********.0

Copyright (c) 1982, 2021, Oracle.  All rights reserved.

Ora ultimo login riuscito: Gio Mar 14 2024 10:36:45 +01:00

Connesso a:
Oracle Database 19c Enterprise Edition Release ********.0 - Production
Version *********.0

SQL> set SERVEROUT on
SQL> 
SQL> whenever oserror exit 1 rollback;
SQL> whenever sqlerror exit 1 rollback;
SQL> 
SQL> CREATE OR REPLACE VIEW V_R_CENTRALI_RO_CDL AS
  2  select distinct w.tipinterv tipo_intervento, c.liv1 RO, c.centrale "centralId", w.cdl
  3  from amelia.centrali@service_wfm c
  4  	 join amelia.config_cdl@service_wfm w on w.centrale = c.centrale
  5  where w.tipinterv in ('YR','YS','YA','YB');

Vista creata.

SQL> 
SQL> accept obj_centrali_ro_cdl default 'V_R_CENTRALI_RO_CDL' prompt 'dammi nome oggetto per i circuiti (default V_R_CENTRALI_RO_CDL) '
dammi nome oggetto per i circuiti (default V_R_CENTRALI_RO_CDL) 

SQL*Plus: Release ********.0 - Production on Gio Mar 14 10:58:17 2024
Version *********.0

Copyright (c) 1982, 2021, Oracle.  All rights reserved.

Ora ultimo login riuscito: Gio Mar 14 2024 10:57:15 +01:00

Connesso a:
Oracle Database 19c Enterprise Edition Release ********.0 - Production
Version *********.0

SQL> set SERVEROUT on
SQL> 
SQL> whenever oserror exit 1 rollback;
SQL> whenever sqlerror exit 1 rollback;
SQL> 
SQL> CREATE OR REPLACE VIEW V_R_CENTRALI_RO_CDL AS
  2  select distinct w.tipinterv tipo_intervento, c.liv1 RO, c.centrale "centralId", w.cdl
  3  from amelia.centrali@service_wfm c
  4  	 join amelia.config_cdl@service_wfm w on w.centrale = c.centrale
  5  where w.tipinterv in ('YR','YS','YA','YB');

Vista creata.

SQL> 
SQL> create or replace view V_CENTRALI_RO_CDL as
  2  select *
  3  from V_R_CENTRALI_RO_CDL;

Vista creata.

SQL> 
SQL> drop materialized view MV_CENTRALI_RO_CDL;

Vista materializzata eliminata.

SQL> 
SQL> create materialized view MV_CENTRALI_RO_CDL refresh complete as
  2  select *
  3  from V_CENTRALI_RO_CDL;

Creata vista materializzata.

SQL> 
SQL> grant select on MV_CENTRALI_RO_CDL to AP_ART;

Concessione riuscita.

SQL> 
SQL> BEGIN
  2    dbms_scheduler.create_job(
  3  	 JOB_NAME=>'JOB_REFRESH_MV_CENTRALI_RO_CDL',
  4  	 JOB_TYPE=>'PLSQL_BLOCK',
  5  	 AUTO_DROP=>FALSE,
  6  	 JOB_ACTION=>'DBMS_SNAPSHOT.REFRESH(''"AP"."MV_CENTRALI_RO_CDL"'',''C'');',
  7  	 START_DATE=> systimestamp,
  8  	 REPEAT_INTERVAL=>'freq=daily;byhour=1; byminute=30;bysecond=0',
  9  	 ENABLED=>TRUE,
 10  	 COMMENTS=>'Job refresh MV MV_CENTRALI_RO_CDL'
 11  );
 12  END;
 13  /

Procedura PL/SQL completata correttamente.

SQL> 
SQL> show errors;
Nessun errore.
SQL> 
SQL> quit
Disconnesso da Oracle Database 19c Enterprise Edition Release ********.0 - Production
Version *********.0
