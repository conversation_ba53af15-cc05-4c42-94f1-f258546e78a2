set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

insert into allegati_azione_eventi (sistema_esterno, ts_notifica, ts_lavorazione, id_Attivita, id_allegato, tipo_Evento) 
select '0',
    systimestamp,
    null,
    id_attivita,
    id_allegato,
    'UPSERT'
from allegati_Azione
where data_cancellazione is null;

commit;

show errors

quit
