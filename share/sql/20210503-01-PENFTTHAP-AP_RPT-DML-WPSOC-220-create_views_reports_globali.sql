set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oser<PERSON>r  exit 2 rollback;

declare
     view_name varchar(40) := '';
     sq_view varchar(32000) := '';
     c_id_tipo_attivita ap_art.tipi_attivita.id_tipo_attivita%type;
     type varray_varchar is varying array(400) of ap_art.tipi_attivita.nome_tipo_attivita%type;
     var_varray_varchar varray_varchar;
     
 begin
 
    var_varray_varchar  := varray_varchar(
        'PERMESSO_LAVORI',
        'PERMESSO_BUILDING'
    );

    for ta in 1 .. var_varray_varchar.count loop

        --MS
        select id_tipo_attivita into c_id_tipo_attivita from ap_art.tipi_attivita where nome_tipo_attivita = var_varray_varchar(ta);
        
        view_name := 'v_tim_'||var_varray_varchar(ta);

        if (var_varray_varchar(ta) = 'PERMESSO_BUILDING') then 
            sq_view := 'create or replace view '||view_name||' as select xt.*, (select dt.valore from ap_art.v_dt_sistemi dt where dt.id_Sistema = xt.system_id and dt.nome = ''assetId'' and rownum<2) "assetId" from ap_art.v_xt_'||c_id_tipo_attivita||' xt';
        else 
            sq_view := 'create or replace view '||view_name||' as select * from ap_art.v_xt_'||c_id_tipo_attivita;
        end if;

        execute immediate sq_view;

    end loop;

 end;
/

show errors

quit
