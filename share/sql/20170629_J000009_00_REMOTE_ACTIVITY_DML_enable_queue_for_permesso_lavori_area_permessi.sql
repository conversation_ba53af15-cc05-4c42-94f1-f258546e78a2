
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'PERMESSO_LAVORI',
    'Gestione Permesso Lavori',
    1
  );

INSERT
INTO
  RA_CONTEXT
  (
    ID,
    DESCRIPTION,
    ENABLED
  )
  VALUES
  (
    'AREA_PERMESSI',
    'Gestione Area Permessi',
    1
  );


insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    '___LOCALHOST___',
    'PERMESSO_LAVORI',
    'UPDATE_STATO_AREA_PERMESSI',
    'Gestione aggiornamento area permessi a seguito movimentazione permesso lavori',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = '___LOCALHOST___'
	and 	SOURCE_CONTEXT = 'PERMESSO_LAVORI'
        and     EVENT = 'UPDATE_STATO_AREA_PERMESSI'
        and     ENABLED = 1
    ),
    '___LOCALHOST___',
    'AREA_PERMESSI',
    1
);

commit;

quit
