
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

insert into RA_CONTEXT (
    ID,
    DESCRIPTION,
    ENABLED
) values (
    'PERMESSO_BUILD',
    'Gestione Permesso Building',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'PERMESSO_BUILD',
    'NOTIFY_OPEN',
    'Comunicazioni apertura permesso_building',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'PERMESSO_BUILD'
        and     EVENT = 'NOTIFY_OPEN'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'PERMESSO_BUILD',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'PERMESSO_BUILD',
    'NOTIFY_CLOSE_KO',
    'Comunicazioni chiusura negativa permesso_building',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'PERMESSO_BUILD'
        and     EVENT = 'NOTIFY_CLOSE_KO'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'PERMESSO_BUILD',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'PERMESSO_BUILD',
    'NOTIFY_CLOSE_OK',
    'Comunicazioni chiusura positiva permesso_building',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'PERMESSO_BUILD'
        and     EVENT = 'NOTIFY_CLOSE_OK'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'PERMESSO_BUILD',
    1
);

commit;

quit
