set line 1000
set autocommit off
set echo on
set SER<PERSON><PERSON>UT on  size 1000000
set SER<PERSON><PERSON>UT off

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 2 rollback;

CREATE MATERIALIZED VIEW "AP"."MV_ANAG_PERSONE_EXT" ("USERID", "PARTNE<PERSON>", "RA<PERSON><PERSON><PERSON>_SOCIALE","<PERSON><PERSON><PERSON>")
  SEGMENT CREATION IMMEDIATE
  ORGANIZATION HEAP PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "TT_DATA" 
  BUILD IMMEDIATE
  USING INDEX 
  REFRESH COMPLETE ON DEMAND
  USING DEFAULT LOCAL ROLL<PERSON>CK SEGMENT
  USING ENFORCED CONSTRAINTS DISABLE ON QUERY COMPUTATION DISABLE QUERY REWRITE
  AS select *
from V_ANAG_PERSONE_EXT;

COMMENT ON MATERIALIZED VIEW "AP"."MV_ANAG_PERSONE_EXT"  IS 'snapshot table for snapshot AP.MV_ANAG_PERSONE_EXT';


GRANT SELECT ON "AP"."MV_ANAG_PERSONE_EXT" TO "AP_ART";

show errors

quit
