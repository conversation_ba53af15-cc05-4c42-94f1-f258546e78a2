set line 1000
set autocommit off
set echo on
set SERVEROUT on  size    1000000

whenever sqlerror exit 1 rollback;
whenever oserror  exit 2 rollback;

create or replace view v_tim_permessi_building_last as
select *
from ap.v_ap_permessi_building
where ("projectId", id_permesso) in (
    select "projectId", max(id_permesso) ID_PERMESSO
    from ap.v_ap_permessi_building
    where "projectId" is not null
    and "customerId" = 'TIM'
    group by "projectId"
);

GRANT SELECT ON v_tim_permessi_building_last TO CLIENT_CORE_RPT;

create or replace view v_tim_permessi_lavori_last as
select pti.id_Attivita, vi.stato_Corrente stato, vi.data_ult_varstat data_ultima_variazione, pti."projectId", pti."maker", pti."teamId", pti."teamName", pti."subContractCode", pti."subContractName"
from ap_art.pt_permesso_lavori pti
    join ap_art.v_Attivita vi on vi.id = pti.id_Attivita
where (pti."projectId", pti.id_Attivita) in (
    select "projectId", max(a.id) ID_PERMESSO
    from  ap_art.v_attivita a
          --inner join ap_art.a_padre_a_figlio apaf on Apaf.Padre = a.id
          --inner join ap_art.v_attivita p on p.id = apaf.figlio
          join ap_art.pt_permesso_lavori pt on pt.id_Attivita = a.id
          join ap_art.v_dt_Sistemi dts on dts.id_sistema = a.id_Sistema and dts.nome = 'customerId'
    where A.Nome_Tipo_Attivita = 'PERMESSO_LAVORI'
    and dts.valore = 'TIM'
    group by "projectId"
);

GRANT SELECT ON v_tim_permessi_lavori_last TO CLIENT_CORE_RPT;

show errors

quit
