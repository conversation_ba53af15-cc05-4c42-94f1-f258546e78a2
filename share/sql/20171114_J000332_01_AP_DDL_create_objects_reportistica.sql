set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_ap as
select a.id, 
	   ap_art.property_value(a.id, 'projectId') projectId,
	   ap_art.property_value(a.id, 'permitsAreaId') id_area_permesso,
	   ap_art.property_value(a.id, 'name') nome_ap,
	   a.stato_corrente stato,
	   Sa.Login_Operatore nome_creatore,
	   sa.data_esecuzione data_creazione,
	   A.Data_Ult_Varstat data_ultima_modifica
from  ap_art.v_attivita a
	  inner join  ap_art.V_Storia_Attivita sa on sa.id_attivita = a.id
where A.Nome_Tipo_Attivita = 'AP_LC'
  and Sa.Azione = 'APERTURA';

create or replace view v_ap_permessi_pubblici as 
select a.id, 
	   ap_art.property_value(a.id, 'projectId') projectId,
	   ap_art.property_value(a.id, 'permitsAreaId') id_area_permesso,
	   ap_art.property_value(a.id, 'name') nome_ap,
	   p.id id_permesso,
	   p.descrizione descrizione_permesso,
	   ap_art.property_value(p.id, 'publicPermitCategory') categoria,
	   ap_art.property_value(p.id, 'publicPermitType') tipo,
	   ap_art.property_value(p.id, 'authority') ente,
	   p.stato_corrente stato,
	   ap_art.property_value(p.id, 'protocol') protocollo,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'requestDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd') data_inoltro_permesso,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'expectedAuthorizationDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd') data_inizio_attesa,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'expectedEndAuthorizationDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd')  data_fine_attesa,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'authorizationDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd')  data_inizio_ottenuta,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'endAuthorizationDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd')  data_fine_ottenuta,
	   to_number(ap_art.property_value(p.id, 'amount')) euro
from  ap_art.v_attivita a
	  inner join ap_art.a_padre_a_figlio apaf on Apaf.Padre = a.id
	  inner join ap_art.v_attivita p on p.id = apaf.figlio
where A.Nome_Tipo_Attivita = 'AP_LC';

create or replace view v_buildings as 
with buildings as (
  select  id id_building,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'projectId'
		  ) projectId,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'permitsAreaId'
		  ) permitsAreaId,
		  (
		  select dts.valore
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'completeAddress'
		  ) completeAddress,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'privateUI'
		  ) privateUI,
		  (
		  select to_number(dts.valore)
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'smallBusinessUI'
		  ) smallBusinessUI,
		  (
		  select dts.valore
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'buildingSinfoId'
		  ) buildingSinfoId,
		  (
		  select dts.valore
		  from ap_art.V_Dt_Sistemi dts
		  where Dts.Id_Sistema = s.id
			and dts.nome = 'verticalConstruction'
		  ) verticalConstruction
  from  ap_art.v_sistemi s
  where s.tipo = 'BUILDING'
),
att as (
select a.id,
	   ap_art.property_value(a.id, 'name') nome_ap,
	   ap_art.property_value(a.id, 'permitsAreaId') id_area_permesso
from  ap_art.v_attivita a 
where a.Nome_Tipo_Attivita = 'AP_LC'
)
select b.projectid,
	   a.id_area_permesso,
	   a.nome_ap,
	   b.id_building id_building,
	   b.buildingSinfoId id_building_sinfo,
	   b.completeAddress indirizzo,
	   nvl(b.smallBusinessUI, 0) + nvl(b.privateUI, 0) tot_ui,
	   b.verticalConstruction stato_del_verticale
from buildings b 
	  left join att a on a.id_area_permesso = b.permitsAreaId
;

create or replace view v_ap_permessi_building as 
select p.id_sistema id_building,
	   p.id id_permesso,
	   ap_art.property_value(p.id, 'privatePermitCategory') categoria,
	   ap_art.property_value(p.id, 'privatePermitType') tipo,
	   p.stato_corrente stato,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'requestDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd') data_inoltro_permesso,
	   to_char(to_timestamp_tz(ap_art.property_value(p.id, 'authorizationDate'), 'YYYY-MM-DD"T"hh24:mi:ss.fftzh:tzm') at time zone 'Europe/Rome', 'yyyy-mm-dd') data_permesso_ottenuto
from  ap_art.v_attivita p
where p.Nome_Tipo_Attivita = 'PERMESSO_BUILDING';
	
grant select on v_ap to ap_rpt;
grant select on v_ap_permessi_pubblici to ap_rpt;
grant select on v_buildings to ap_rpt;
grant select on v_ap_permessi_building to ap_rpt;
grant select on v_ap to client_core_rpt;
grant select on v_ap_permessi_pubblici to client_core_rpt;
grant select on v_buildings to client_core_rpt;
grant select on v_ap_permessi_building to client_core_rpt;
grant select on v_ap to client_works_rpt;
grant select on v_ap_permessi_pubblici to client_works_rpt;
grant select on v_buildings to client_works_rpt;
grant select on v_ap_permessi_building to client_works_rpt;

quit