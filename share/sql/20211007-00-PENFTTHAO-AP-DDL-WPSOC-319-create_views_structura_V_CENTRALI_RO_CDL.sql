set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON><PERSON> exit 1 rollback;
whenever sqlerror exit 1 rollback;

CREATE OR REPLACE VIEW V_R_CENTRALI_RO_CDL AS
select distinct c.liv1 RO, c.centrale "centralId", w.cdl
from amelia.centrali@service_wfm c
    join amelia.config_cdl@service_wfm w on w.centrale = c.centrale;

accept obj_centrali_ro_cdl default 'V_R_CENTRALI_RO_CDL' prompt 'dammi nome oggetto per i circuiti (default V_R_CENTRALI_RO_CDL) '

create or replace view V_CENTRALI_RO_CDL as
select *
from &obj_centrali_ro_cdl;

create materialized view MV_CENTRALI_RO_CDL refresh complete as
select *
from V_CENTRALI_RO_CDL;

alter materialized view MV_CENTRALI_RO_CDL refresh start with sysdate+1/48 next sysdate+1/24;

grant select on MV_CENTRALI_RO_CDL to AP_ART;

quit