set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever o<PERSON><PERSON>r exit 1 rollback;
whenever sqlerror exit 1 rollback;

create or replace view v_redi_allegati as
select es.*,am.id KEY_ID
        ,am.ts KEY_TIMESTAMP
        ,am.key "KEY"
        ,am.value "VALUE"
from (
  select   (
            select valore
            from   config_art
            where chiave = 'ART.APPLICATION_NAME'
                and rownum < 2
         ) OWNER_ID
        ,to_char(aa.id_allegato) OWNER_REF_ID -- generalizzazione
        ,aa.ts_allegato FILE_TIMESTAMP
        ,aa.nome_file_client FILENAME
        ,aa.dimensione "SIZE"
        ,null "MD5"
        ,cast (o.cognome_operatore
            || case when o.cognome_operatore is null or o.nome_operatore is null then '' else ', ' end
            || o.nome_operatore as varchar2(4000)) "USER_NAME"
        ,aa.titolo TITLE
        ,aa.descrizione "DESCRIPTION"
        ,aa.revisione REV
        ,aa.data_riferimento REF_DATE
        ,(select tata.id_tipo_documento from tipi_attivita_tipi_allegati tata where tata.id = aa.id_tipo_allegato_tipo_doc) DOC_TYPE
        , (
            select valore
            from   config_art
            where chiave = 'ART.REDI_INDEX'
                and rownum < 2
         ) REDI_INDEX
        , (
            select valore
            from   config_art
            where chiave = 'ART.REDI_TYPE_CONTEXT'
                and rownum < 2
         ) REDI_TYPE_CONTEXT
        ,aa.id_attivita REDI_TYPE_ID
        ,to_char(aa.id_action, 'yyyymmddhh24miss') REDI_TYPE_TRANSITION_ID
        , (ROW_NUMBER() OVER(PARTITION BY aa.id_attivita, aa.id_action
       ORDER BY aa.id_attivita, aa.id_action, aa.nome_file_servizio))-1 AS REDI_TYPE_SEQUENCE
from allegati_azione aa
    inner join storia_attivita sa on sa.id_attivita = aa.id_attivita and sa.data_esecuzione = aa.id_action
    inner join operatori o on o.id_operatore = sa.id_operatore
where aa.ts_allegato > to_timestamp('2020-10-02 11:25:40,686000000', 'yyyy-mm-dd hh24:mi:ss,ff') -- HEADUP: necessario in seguito al crash di Connect del 2020-10-02
)
es
    inner join allegati_meta am on es.OWNER_REF_ID = to_char(am.id_allegato)
;


quit
