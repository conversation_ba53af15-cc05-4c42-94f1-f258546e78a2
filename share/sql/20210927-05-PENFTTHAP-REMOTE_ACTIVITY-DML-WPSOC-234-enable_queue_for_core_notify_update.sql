
/* inserimento voci per abilitare la lettura delle remote activity    */
/* IMPORTANTE: questo script va eseguito come remote_activity */


set line 1000
set autocommit off
set echo on
set SERVEROUT on

whenever oserror exit 1 rollback;
whenever sqlerror exit 1 rollback;

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'PERMESSO_BUILD',
    'ROE_NOTIFY_UPDATE',
    'Comunicazioni aggiornamento permesso',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'PERMESSO_BUILD'
        and     EVENT = 'ROE_NOTIFY_UPDATE'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'PERMESSO_BUILD',
    1
);

insert into RA_EVENT (
    ID,
    SOURCE_SERVICE,
    SOURCE_CONTEXT,
    EVENT,
    DESCRIPTION,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_EVENT ),1),
    'ENFTTH_AP',
    'PERMESSO_LAVORI',
    'ROE_NOTIFY_UPDATE',
    'Comunicazioni aggiornamento permesso',
    1
);

insert into RA_QUEUE (
    ID,
    EVENT_ID,
    TARGET_SERVICE,
    TARGET_CONTEXT,
    ENABLED
) values (
    nvl(( select 1+max(ID) from RA_queue ),1),
    (   select  ID
        from    RA_EVENT
        where   SOURCE_SERVICE = 'ENFTTH_AP'
        and     SOURCE_CONTEXT = 'PERMESSO_LAVORI'
        and     EVENT = 'ROE_NOTIFY_UPDATE'
        and     ENABLED = 1
    ),
    'ENFTTH_CORE',
    'PERMESSO_LAVORI',
    1
);

commit;

quit
