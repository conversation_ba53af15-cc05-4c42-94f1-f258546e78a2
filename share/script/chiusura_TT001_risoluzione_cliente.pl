#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use Log::Log4perl qw(get_logger :levels);
use Try::Tiny;

use API::ART;

use API::ART::Collection::Activity;
$API::ART::Collection::Activity::DEFAULT_CLASS_TO_CREATE = 'API::ART::Activity::Factory';

my $logger_name  = 'WPSOAP::BIN::chiusura_TT001_risoluzione_cliente';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;

# ENUMerazione degli errori
use enum qw (:ERROR_=0
    OK=0
    SHOW_USAGE=0
    WRONG_OPTIONS
    UNABLE_TO_LOAD_LOG_CONFIG
    CANNOT_INIT_API_ART
    CANNOT_INIT_API_ART_Collection_Activity
    CANNOT_INIT_WPSOAP_Collection_Activity_TT001
);

sub usage( $ ) {
    my $rc = shift;
    my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
    print $file "
    $0  [<opzioni>]

    Effettua chiusura dei TT001 ferme nello stato RISOLUZIONE_LAVORAZIONE_CLIENTE da più di 3 gg

    <opzioni>:
        -a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOAP_ARTID)
        -u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOAP_SCRIPT_USER)
        -p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOAP_SCRIPT_PASSWORD)
        -t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
            altrimenti esegue un rollback
        -l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
            variabile d'ambiente LOG4PERL_CONF
        -d, --debug - attiva messaggi di debug per le API::ART
        -g, --giorni-timeout - giorni di fermo da attendere prima di forzare la chiusura (DEFAULT 15)
        -h, --help - questo aiuto

";
    exit $rc;
}

my ( $help, $commit, $debug );

my $artid               = $ENV{WPSOAP_ARTID};
my $api_script_user     = $ENV{WPSOAP_SCRIPT_USER};
my $api_script_password = $ENV{WPSOAP_SCRIPT_PASSWORD};
my $log_config          = $ENV{LOG4PERL_CONF};
my $timeout             = 3;
my $stato_attesa        = 'RISOLUZIONE_LAVORAZIONE_CLIENTE';
my $azione              = 'VALIDAZIONE_OK';

GetOptions (
    'help|h'=>              \$help,
    'artid|a=s' =>          \$artid,
    'api-user|u=s' =>       \$api_script_user,
    'api-password|p=s' =>   \$api_script_password,
    'transaction-mode|t=s'=> sub {
        if ( $_[1] eq 'r' ) {
            $commit = 0;
        } elsif ( $_[1] eq 'c' ) {
            $commit = 1;
        } else {
            die( "--transaction-mode puo' valere 'r' o 'c'\n" );
        }
    },
    'log-config|l=s'=>  \$log_config,
    'debug|d' =>        \$debug,
    'giorni-timeout|g=i' => \$timeout
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );


# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

try {
    Log::Log4perl->init( $log_config );
} catch {
    print STDERR "Impossibile inizializzare il sistema di logging: $_";
    exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
};

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );

$logger->debug( "Inizializzo API::ART" );
my $art = try {
    API::ART->new(
        ARTID    => $artid
        ,USER     => $api_script_user
        ,PASSWORD => $api_script_password
        ,AUTOSAVE => 0
        ,DEBUG    => $debug
    );
} catch {
    $logger->fatal( "Impossibile inizializzare API::ART: $_" );
    exit ERROR_CANNOT_INIT_API_ART;
};

$logger->debug( "Inizializzo API::ART::Collection::Activity" );
my $collActivity = try {
    API::ART::Collection::Activity->new(ART => $art);
} catch {
    $logger->fatal( "Impossibile inizializzare WPSOAP::API::ART::Collection::Activity: $_" );
    exit ERROR_CANNOT_INIT_API_ART_Collection_Activity;
};

my $db = $art->_dbh;
my $q_date_before = sprintf q{
    select  to_char( trunc(sysdate) - %d, %s )
    from    dual
}, $timeout, $db->quote( $art->get_default_date_format );
my $p_date_before = $db->create_prepare($q_date_before);
my $date_before = $p_date_before->fetchscalar;

my $descrizione = sprintf q{Chiusura per fermo maggiore di %d giorni nello stato %s}, $timeout, $stato_attesa;

my $tt001s = $collActivity->find_object(
    ACTIVITY_TYPE_NAME => [ 'TT001' ],
    STATUS_IN          => [ $stato_attesa ],
    LAST_VAR_DATE_LE   => $date_before,
);

my %step_params = (
    ACTION => $azione,
    DESCRIPTION => $descrizione,
);

#%step_params = (
#    VIRTUAL => 1,
#    DEST_STATUS => 'CHIUSA',
#    ACTION => '__ALLINEAMENTO__',
#    DESCRIPTION => $descrizione,
#);

LOOP_N: for my $tt001 ( @{$tt001s} ) {

    $db->do('savepoint tt001_closing');

    if ( $tt001->step( %step_params ) ) {
        $logger->info( sprintf q{La tt001 %d è stata chiusa}, $tt001->id );
    } else {
        $db->do('rollback to savepoint tt001_closing');
        $logger->error( sprintf q{Impossibile effettuare lo step %s sul tt001 %d: %s}, $azione, $tt001->id, $art->last_error );
        next LOOP_N;
    }

    if ($commit) {
        $art->save;
    } else  {
        $db->do('rollback to savepoint tt001_closing');
        $art->cancel;
    }
}

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;

