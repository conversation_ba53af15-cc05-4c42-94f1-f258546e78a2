#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Text::CSV_XS;
use Text::CSV::Separator qw(get_separator);
use API::ART;
use WPSOAP::Collection::System::NETWORK;
use Array::Utils qw(:all);

my $logger_name  = 'WPSOAP::BIN::societarizzazione_cliente';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_LOAD_LOG_CONFIG
	MISSING_MANDATORY_PARAM
	CANNOT_DETERMINE_SEPARATOR
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOAP_Collection_System_NETWORK
	CANNOT_OPEN_INPUT_FILE
	CANNOT_OPEN_OUTPUT_FILE
	OUTPUT_FILE_EXISTS
	DB
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>]

	Effettua societarizzazione cliente

	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOAP_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOAP_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOAP_SCRIPT_PASSWORD)
		-f, --filename=<FILENAME> - csv con il tracciato previsto per la migrazione
		-x, --customerId - identificativo cliente (default 'TIM')
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $commit, $log_config, $debug, $file ,$customerId);

$artid               = $ENV{WPSOAP_ARTID};
$api_script_user     = $ENV{WPSOAP_SCRIPT_USER};
$api_script_password = $ENV{WPSOAP_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'filename|f=s' =>		\$file,,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'log-config|l=s'=>	\$log_config,
	'customerId|x=s'=>	\$customerId,
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

unless (defined $file){
	print STDERR "--filename obbligatorio";
	usage( ERROR_WRONG_OPTIONS );
}
( my $out_file = $file ) =~ s{(\.csv)?$}{_OUTPUT.csv};

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

$customerId = 'TIM' unless defined $customerId;

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

$logger->debug( "Inizializzo API::ART" );
my $api_art;

eval {
	$api_art = API::ART->new(
		ARTID    => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $fh;
unless(open $fh, "<:encoding(utf8)", $file){
	$logger->fatal( "Impossibile aprire in lettura il file $file: $!" );
	exit ERROR_CANNOT_OPEN_INPUT_FILE;
};
if ( -e $out_file ) {
    $logger->fatal(sprintf q{Il file di output '%s' esiste già}, $out_file);
    exit ERROR_OUTPUT_FILE_EXISTS
}

my $out;
unless(open $out, ">:encoding(utf8)", $out_file){
	$logger->fatal( "Impossibile aprire in scrittura il file $out_file: $!" );
	exit ERROR_CANNOT_OPEN_OUTPUT_FILE;
};

my @char_list = eval {get_separator(path    => $file)};

unless (scalar @char_list){
	$logger->fatal( "Impossibile dedurre carattere separatore file in input $file" );
	exit ERROR_CANNOT_DETERMINE_SEPARATOR;
}

my $csv = Text::CSV_XS->new(
	{
		binary        => 1
		,auto_diag    => 1 
		,sep_char     => $char_list[0]
		,quote_char   => '"'
        ,eol          => $/
        ,always_quote => 1
	}
);

my $coll;
eval {
	$coll = WPSOAP::Collection::System::NETWORK->new(ART => $api_art);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOAP::Collection::System::NETWORK: $@" );
	exit ERROR_CANNOT_INIT_WPSOAP_Collection_System_NETWORK;
}

my $csv_mandatory_fields = [
	'networkId',
	'networkIdOld',
	'workOrderId',
	'workOrderIdOld',
	'customerWBE',
	'customerWBEOld',
	'buyer',
	'buyerOld',
	'buyerDesc',
	'buyerDescOld',
];

my $header = $csv->getline($fh);

for my $h ( @{$csv_mandatory_fields}){
	unless(grep {$_ eq $h} @{$header}){
		$logger->fatal( "Campo obbligatorio mancante: ".$h );
		exit ERROR_MISSING_MANDATORY_PARAM;
	}
}

$csv->print($out, [ @{$header}, 'CAUSALE ERRORE' ]);
my $i = 0;
CSV_LOOP: while (<$fh>){
	$i++;
	$logger->info( "Elaboro riga $i" );
	my %row;
	my %rowFinal;
	$csv->parse($_);
	my @fields = $csv->fields();

	# read data
	@row{@{$header}} = @fields;
	
	for my $k (keys %row){
		$rowFinal{$k} = $row{$k};
	}

	# normalizzo la network
	for ('networkId', 'networkIdOld'){
		$rowFinal{$_} = sprintf '%012d', $rowFinal{$_};
	}

	# aggancio la network
	my $networks = $coll->cerca(
		customerId	=> $customerId,
		networkId	=> $rowFinal{'networkIdOld'},
	);
	unless (defined $networks){
		my $message = "Riga $i => KO: ".$api_art->last_error;
		$csv->print($out, [ @fields, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}

	unless (scalar @{$networks}){
		my $message = "Riga $i => KO: network ".$rowFinal{'networkIdOld'}." non trovata";
		$csv->print($out, [ @fields, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}

	my $network = $networks->[0];

	unless ($network->set_description($rowFinal{'networkId'})){
		my $message = "Riga $i => KO: network ".$rowFinal{'networkIdOld'}." ".$api_art->last_error;
		$csv->print($out, [ @fields, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}

	unless ($network->set_property(
		PROPERTIES => {
			networkId		=> $rowFinal{'networkId'},
			networkIdOld	=> $rowFinal{'networkIdOld'},
		}
	)){
		my $message = "Riga $i => KO: network ".$rowFinal{'networkIdOld'}." ".$api_art->last_error;
		$csv->print($out, [ @fields, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}

	$logger->info("OK: Network ".$rowFinal{'networkIdOld'}." (ID_SISTEMA ".$network->id().") aggiornato correttamente");

	if ($commit){
		$api_art->save();
	} else	{
		$api_art->cancel();
	}
}

close $fh;
close $out or warn "Error - $out_file: $!";

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;


