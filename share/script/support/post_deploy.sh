#!/bin/bash

chmod 755 \
  $ETC/profile.sh \
  $ROOT/share/script/crea_nuovi_buildings.pl \
  $ROOT/share/script/manage_workingGroupCode.pl \
  $ROOT/share/script/chiusura_TT001_risoluzione_cliente.pl

cd $ROOT/bin && \
  rm -f \
    crea_nuovi_buildings \
    manage_workingGroupCode \
    chiusura_TT001_risoluzione_cliente \
    api-art-activity-stream-lc06 \
    api-art-activity-stream-lc-customer-project \
    api-art-activity-stream-permesso-lavori \
    api-art-activity-stream-permesso-building \
    api-art-activity-stream-tt001 \
    && \
    ln -s ../share/script/crea_nuovi_buildings.pl crea_nuovi_buildings \
    && \
    ln -s ../share/script/manage_workingGroupCode.pl manage_workingGroupCode \
    && \
    ln -s ../share/script/chiusura_TT001_risoluzione_cliente.pl chiusura_TT001_risoluzione_cliente \
    &&
    ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-lc06 \
    &&
    ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-lc-customer-project \
    &&
    ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-permesso-lavori \
    &&
    ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-permesso-building \
    &&
    ln -s ../../COM/share/script/elk-ext/api-art-activity-stream.pl api-art-activity-stream-tt001
