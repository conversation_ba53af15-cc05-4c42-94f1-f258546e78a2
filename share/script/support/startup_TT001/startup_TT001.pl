#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Text::CSV_XS;
use Text::CSV::Separator qw(get_separator);
use API::ART;
use WPSOAP::Collection::Activity::TT001;
use Array::Utils qw(:all);

my $logger_name  = 'WPSOAP::BIN::startup_TT001';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	UNABLE_TO_LOAD_LOG_CONFIG
	MISSING_MANDATORY_PARAM
	CANNOT_DETERMINE_SEPARATOR
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOAP_Collection_Activity_TT001
	CANNOT_OPEN_INPUT_FILE
	CANNOT_OPEN_OUTPUT_FILE
	OUTPUT_FILE_EXISTS
	DB
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>]
	
	Effettua importazione massiva siti lc06 

	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOAP_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOAP_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOAP_SCRIPT_PASSWORD)
		-f, --filename=<FILENAME> - csv con il tracciato previsto per l'import
		-x, --customerSystem - nome Sistema Cliente
		-y, --contractId - identificativo contratto
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $commit, $log_config, $debug, $file ,$customerSystem, $contractId);

$artid               = $ENV{WPSOAP_ARTID};
$api_script_user     = $ENV{WPSOAP_SCRIPT_USER};
$api_script_password = $ENV{WPSOAP_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'filename|f=s' =>		\$file,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'log-config|l=s'=>	\$log_config,
	'customerSystem|x=s'=>	\$customerSystem,
	'contractId|y=s'=>	\$contractId,
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

unless (defined $file){
	print STDERR "--filename obbligatorio";
	usage( ERROR_WRONG_OPTIONS );
}
( my $out_file = $file ) =~ s{(\.csv)?$}{_OUTPUT.csv};

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

$logger->debug( "Inizializzo API::ART" );
my $api_art;

eval {
	$api_art = API::ART->new(
		ARTID    => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $fh;
unless(open $fh, "<:encoding(utf8)", $file){
	$logger->fatal( "Impossibile aprire in lettura il file $file: $!" );
	exit ERROR_CANNOT_OPEN_INPUT_FILE;
};
if ( -e $out_file ) {
    $logger->fatal(sprintf q{Il file di output '%s' esiste già}, $out_file);
    exit ERROR_OUTPUT_FILE_EXISTS
}

my $out;
unless(open $out, ">:encoding(utf8)", $out_file){
	$logger->fatal( "Impossibile aprire in scrittura il file $out_file: $!" );
	exit ERROR_CANNOT_OPEN_OUTPUT_FILE;
};

my @char_list = eval {get_separator(path => $file)};

unless (scalar @char_list){
	$logger->fatal( "Impossibile dedurre carattere separatore file in input $file" );
	exit ERROR_CANNOT_DETERMINE_SEPARATOR;
}

my $csv = Text::CSV_XS->new(
	{
		binary        => 1
		,auto_diag    => 1 
		,sep_char     => $char_list[0]
		,quote_char   => '"'
        	,eol          => $/
        	,always_quote => 1
	}
);

my $collTT001;
eval {
	$collTT001 = WPSOAP::Collection::Activity::TT001->new(ART => $api_art);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOAP::Collection::Activity::TT001: $@" );
	exit ERROR_CANNOT_INIT_WPSOAP_Collection_Activity_TT001;
}

my $customerId = 'TIM';
my $csv_mandatory_fields;
my %field_mapping = (
    'Network'                    => 'networkId',
    'Sistema cliente'            => 'customerSystem',
    'Email Cliente TO'           => 'customerContactsTO',
    'Email Cliente CC'           => 'customerContactsCC',
    'Nome WO'                    => 'nameWO',
    'Specializzazione WO'        => 'specialisationWO',
    'Tipo WO'                    => 'typeWO',
    'Ambito WO'                  => 'scopeWO',
    'Ambito'                     => 'scope',
    'Q.ta PTE Bloccati'          => 'qtyPTELocked',
    'Utente Segnalante'          => 'reportingUser',
    'OL Fittizio'                => 'FictitiousOL',
    'Errore STD'                 => 'errorSTD',
    'Step Operazione Eseguita'   => 'stepOperationPerformed',
    'Descrizione Problema'       => 'problemDescription',
    'Recapito Telefonico'        => 'telephoneContact',
    'ID Ticket Cliente'          => 'customerTicketId',
    'Data Apertura'              => 'creationDateEx',
    'Codice Fornitore'           => 'supplierCode',
    'ID esterno'                 => 'tt001externalId',
    'Data Invio Richiesta'       => 'dateRequestTT',
    'Data Esito'                 => 'dateOutcome',
    'Nome Job'                   => 'nameJOB',
    'Specializzazione JOB'       => 'specialisationJOB',
    'Tipo Job'                   => 'typeJOB',
    'Nome OL'                    => 'nameOL',
    'Anomalia Riscontrata'       => 'anomalyFound',
);

if($customerSystem eq "NGNEER"){

	$csv_mandatory_fields = [
		'Network',
		'Sistema cliente',
		'Ambito',
		'Nome WO',
		'Specializzazione WO',
		'Tipo WO',
		'Ambito WO',
		'Q.ta PTE Bloccati',
		'Utente Segnalante',
		'OL Fittizio',
		'Errore STD',
		'Step Operazione Eseguita',
		'Descrizione Problema',
		'Email Cliente TO',
		'Email Cliente CC',
		'Recapito Telefonico',
		'ID Ticket Cliente',
		'Stato TT',
		'Data Apertura',
		'Codice Fornitore',
		'ID esterno', 
		'Data Invio Richiesta',
		'Data Esito',
	];
	

}elsif($customerSystem eq "DAPHNE"){
	$csv_mandatory_fields = [
		'Network',
		'Sistema cliente',
		'Nome Job',
		'Specializzazione JOB',
		'Tipo Job',
		'Q.ta PTE Bloccati',
		'Utente Segnalante',
		'Step Operazione Eseguita',
		'Descrizione Problema',
		'Email Cliente TO',
		'Email Cliente CC',
		'Recapito Telefonico',
		'ID Ticket Cliente',
		'Stato TT',
		'Data Apertura',
		'Codice Fornitore',
		'ID esterno',
		'Data Invio Richiesta',
		'Data Esito'
	];

}elsif($customerSystem eq "UNICARA"){
	$csv_mandatory_fields = [
		'Network',
		'Sistema cliente',
		'Nome OL',
		'Anomalia Riscontrata',
		'Utente Segnalante',
		'Descrizione Problema',
		'Email Cliente TO',
		'Email Cliente CC',
		'Step Operazione Eseguita',
		'Recapito Telefonico',
		'ID Ticket Cliente',
		'Stato TT',
		'Data Apertura',
		'Codice Fornitore',
		'ID esterno',
		'Data Invio Richiesta',
		'Data Esito'
	];

}else{
	$logger->fatal("customerSystem non presente: ".$customerSystem);
}
my $stato_dettaglio_anomalia = 'DETTAGLI_ANOMALIA_'.$customerSystem;
my $header = $csv->getline($fh);

for my $h ( @{$csv_mandatory_fields}){
	unless(grep {$_ eq $h} @{$header}){
		$logger->fatal( "Campo obbligatorio mancante:".$h );
		exit ERROR_MISSING_MANDATORY_PARAM;

	}
}
use Data::Dumper;#fixme
$csv->print($out, [ @{$header}, 'CAUSALE ERRORE' ]);
my $i = 0;
CSV_LOOP: while (my $fields = $csv->getline ($fh)){
	$i++;
	$logger->info( "Elaboro riga $i" );
	my %row;
	my %rowFinal;

	# read data
	@row{@{$header}} = @{$fields};
	
	for my $k (keys %row){
		$rowFinal{$k} = $row{$k} if defined $row{$k} && $row{$k} ne '';
	}
	# Forza padding a 12 cifre solo per Network
	if (exists $rowFinal{'Network'}) {
		$rowFinal{'Network'} = sprintf("%012s", $rowFinal{'Network'});
	}
	if (exists $rowFinal{'Codice Fornitore'} && $rowFinal{'Codice Fornitore'} ne ''){
		$rowFinal{'Codice Fornitore'} = sprintf("%010d",$rowFinal{'Codice Fornitore'});
	}
	my %mapped_row;
	for my $csv_key (keys %rowFinal) {
		if (exists $field_mapping{$csv_key}) {
			my $new_key = $field_mapping{$csv_key};
			$mapped_row{$new_key} = $rowFinal{$csv_key};
		}
	}
	#print STDERR Dumper(\%mapped_row);

	my %crea_TT001_params = (
		STARTUP => 1,
		customerSystem	=> $customerSystem,
		networkId => $rowFinal{'Network'},
		customerId		=> $customerId,
	);

	$api_art->_dbh()->do("savepoint \"".$rowFinal{'Network'}."\"");
	my $tt001 = $collTT001->crea(%crea_TT001_params);
	unless(defined $tt001) {
		$api_art->_dbh()->do("rollback to savepoint \"".$rowFinal{'Network'}."\"");
		my $message = "Riga $i => KO: ".$api_art->last_error;
		$csv->print($out, [ @{$fields}, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}

	my	$step_params = {
		ACTION => $stato_dettaglio_anomalia,
		PROPERTIES => \%mapped_row,
		DESCRIPTION => 'Caricamento iniziale TT001'
	};

	# potrebbe essere cambiato a causa della societarizzazione
	$mapped_row{networkId} = $tt001->activity_property('networkId');

	# eseguo lo step per portare il ticket nello stato ANALISI
	unless ($tt001->step_filtered(%$step_params)){
		$api_art->_dbh()->do("rollback to savepoint \"".$rowFinal{'Network'}."\"");
		my $message = "Riga $i => KO: ".$api_art->last_error;
		$csv->print($out, [ @{$fields}, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}
	# eseguo lo step per portare il ticket nello stato specificato nel csv
	my $destStatus = $rowFinal{'Stato TT'}; 
	$destStatus =~ s/^\s+//;  
	$destStatus =~ s/\s+$//;  
	$destStatus =~ s/\s+/_/g;

	for my $mm (keys %mapped_row){
		my $x = {
			TYPE => $api_art->_lookup()->tipo_ui_tdta_nome($mm),
			KEY => $mm,
			VALUE => $mapped_row{$mm}
		};
		if ($api_art->_lookup()->tipo_ui_tdta_nome($mm) eq 'POPUP'){
			$x->{VALUES} = $api_art->_lookup()->valori_tdta_nome($mm) ;
		}
		unless($api_art->check_value(%$x)){
			$api_art->_dbh()->do("rollback to savepoint \"".$rowFinal{'Network'}."\"");
			my $message = "Riga $i => KO: ".$api_art->last_error;
			$csv->print($out, [ @{$fields}, $message ]);
			$logger->error($message);
			next CSV_LOOP;
		}
	}

	unless ($tt001->step(
		ACTION => '__ALLINEAMENTO__',
		VIRTUAL => 1,
		DEST_STATUS => $destStatus,
		PROPERTIES => \%mapped_row,
		DESCRIPTION => 'Caricamento Massivo iniziale TT001'
	)){
		$api_art->_dbh()->do("rollback to savepoint \"".$rowFinal{'Network'}."\"");
		my $message = "Riga $i => KO: ".$api_art->last_error;
		$csv->print($out, [ @{$fields}, $message ]);
		$logger->error($message);
		next CSV_LOOP;
	}

	if (exists $rowFinal{'Codice Fornitore'} && $rowFinal{'Codice Fornitore'} ne ''){
		unless($tt001->system()->set_groups('SERVICE_'.$rowFinal{'Codice Fornitore'})){
			$api_art->_dbh()->do("rollback to savepoint \"".$rowFinal{'Network'}."\"");
			my $message = "Riga $i => KO: ".$api_art->last_error;
			$csv->print($out, [ @{$fields}, $message ]);
			$logger->error($message);
			next CSV_LOOP;
		};
	}
	
	$logger->info("OK: Creata attivita ".ref($tt001)." ".$tt001->id." (".$tt001->get_current_status_name().")");

	if ($commit){
		$api_art->save();
	} else	{
		$api_art->cancel();
	}
}

close $fh;
close $out or warn "Error - $out_file: $!";

$logger->info( '===== END ======================================================================' );

exit ERROR_OK;
