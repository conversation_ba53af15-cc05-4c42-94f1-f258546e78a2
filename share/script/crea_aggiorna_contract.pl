#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);

use API::ART;
use WPSOAP;

my $logger_name  = 'WPSOAP::BIN::crea_aggiorna_contract';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	MISSING_CUSTOMER_ID
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOAP
	CANNOT_UPDATE_CONTRACT
	CANNOT_CREATE_CONTRACT
);

# tabella che associa a ciascun nome di colore la corrispondente sequenza di escape ANSI per la selezione
my %colore	= (
	rosso			=> "\033[31;1m",
	rosso_scuro		=> "\033[31m",
	verde			=> "\033[32;1m",
	verde_scuro		=> "\033[32m",
	giallo			=> "\033[33;1m",
	giallo_scuro	=> "\033[33m",
	blu				=> "\033[34;1m",
	blu_scuro		=> "\033[34m",
	celeste			=> "\033[36;1m",
	celeste_scuro	=> "\033[36m",
	underline		=> "\033[4m",
	default			=> "\033[0m",
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0 [parametri] <customerId>
		
	<opzioni>:
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOAP_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOAP_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOAP_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOAP_TRANSACTION_MODE o se non impostata 'r' (rollback)
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto
	
	Se il contratto è già attivo allinea al visiblità a quella del customer e aggiorna i dati tecnici, altrimenti creo il contratto con la stessa
	visibilità del customer
	
";
	exit $rc;
}

usage( 1 ) if scalar(@ARGV) < 1;

sub _conferma
{	
	my $risposta = "";	# input dell'utente
	my $SI		= \1;	# valore di ritorno per operazione confermata
	my $NO		= \0;	# valore di ritorno per operazioni NON confermata
	my $QUIT	= \2;	# valore di ritorno per USCITA dalla procedura
	
	while ($risposta eq "")  
	{
		my $msg  = "$colore{'celeste_scuro'}$colore{'underline'}(SI/NO/QUIT)$colore{'default'}\n";  
		print $msg;
				
		$risposta = <STDIN>;
		chomp ($risposta);  
		
		if ($risposta eq "SI") 
		{  						
			return $$SI;
		}
		elsif ($risposta eq "NO")
		{
			return $$NO;
		}
		elsif ($risposta eq "QUIT")
		{
			return $$QUIT;
		}
		elsif ($risposta eq "")
		{
			$msg  ="$colore{'rosso_scuro'}ATTENZIONE! Scelta obbligatoria!$colore{'default'}\n";
			print $msg;
		}
		else
		{
			$msg  = "$colore{'rosso'}ATTENZIONE! Opzione NON valida!$colore{'default'}\n" ;
			print $msg;
			
			$risposta = "";
		}
	}
}

my ( $help,$customerId,$artid,$api_script_user,$api_script_password,$commit,$log_config,$debug,$daemon,$waitsec );

$artid               = $ENV{WPSOAP_ARTID};
$api_script_user     = $ENV{WPSOAP_SCRIPT_USER};
$api_script_password = $ENV{WPSOAP_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};

if ( defined $ENV{WPSOAP_TRANSACTION_MODE} && $ENV{WPSOAP_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>				\$help,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" ); 
		}
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

$customerId = $ARGV[0];
usage( ERROR_MISSING_CUSTOMER_ID )
	if (!defined $customerId);

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

my $keep_going = 1;
$SIG{HUP}  = sub {
	$logger->info("Intercettato SIGHUP: chiudo i log di log4perl");
	Log::Log4perl->init( $log_config );
	$logger = get_logger( $logger_name );
	$logger->info("Riapro i log di log4perl");
	$logger->info( $command_line );
};
$SIG{INT}  = sub { $logger->warn("Intercettato SIGINT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{QUIT} = sub { $logger->warn("Intercettato SIGQUIT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{TERM} = sub { $logger->warn("Intercettato SIGTERM: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID     => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}

my $wpsoap;
eval {
	$wpsoap = WPSOAP->new(
		ART => $api_art
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOAP: $@" );
	exit ERROR_CANNOT_INIT_WPSOAP;
}

my %search_customer_params = (
	customerId => $customerId
);

my $customer_system  = $wpsoap->customers()->cerca(%search_customer_params);

if(scalar @{$customer_system}) {
	$logger->info("customer '$customerId' OK!");
} else {
	goto NO_CUSTOMER_FOUND;
}

my $contractId			= "";
my $contractName		= "";
my $operationalContext	= "";
my $contractClaim		= "";
my $requiredBlocks		= [];
my $backgroundURL		= "";
my $logoURL				= "";
my $externalWorkTypeId	= "";
my $companyAbbreviation	= "";
my $companyCode			= "";
my $companyName			= "";

OUTER:
	 
while ($contractId eq "")	{
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'contractId' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$contractId = <STDIN>;
	chomp ($contractId);
	
	if ($contractId =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{												
		if ($contractId eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}
while ($contractName eq "")	{		
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'contractName' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$contractName = <STDIN>;
	chomp ($contractName);
	
	if ($contractName =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{
		if ($contractName eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}
while ($operationalContext eq "")	{		
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'operationalContext' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$operationalContext = <STDIN>;
	chomp ($operationalContext);
	
	if ($operationalContext =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{
		if ($operationalContext eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}

while ($externalWorkTypeId eq "")	{
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'externalWorkTypeId' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$externalWorkTypeId = <STDIN>;
	chomp ($externalWorkTypeId);
	
	if ($externalWorkTypeId =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{												
		if ($externalWorkTypeId eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}

while ($companyAbbreviation eq "")	{		
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'companyAbbreviation' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$companyAbbreviation = <STDIN>;
	chomp ($companyAbbreviation);
	
	if ($companyAbbreviation =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{
		if ($companyAbbreviation eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}

while ($companyCode eq "")	{		
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'companyCode' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$companyCode = <STDIN>;
	chomp ($companyCode);
	
	if ($companyCode =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{
		if ($companyCode eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}

while ($companyName eq "")	{		
	
	printf "$colore{'celeste_scuro'}Inserire un valore per il campo OBBLIGATORIO 'companyName' - ('QUIT' per uscire dalla procedura):$colore{'default'}\n"; 
	
	$companyName = <STDIN>;
	chomp ($companyName);
	
	if ($companyName =~ m/^QUIT$/i )	{
		goto USER_EXIT  
	} else 	{
		if ($companyName eq "")	{
			print "$colore{'rosso_scuro'}Parametro obbligatorio$colore{'default'}\n";
			redo;
		}
	} 
}

my %contract_params = (
	contractId				=> $contractId
	,contractName			=> $contractName
	,operationalContext		=> $operationalContext
	,externalWorkTypeId		=> $externalWorkTypeId
	,companyAbbreviation	=> $companyAbbreviation
	,companyCode			=> $companyCode
	,companyName			=> $companyName
);

REQUIRED_BLOCK: while (1){
	printf "$colore{'celeste'}Inserire un valore per il campo opzionale array 'requiredBlocks' - ('__END_BLOCKS__' per terminare l'inserimento di questo campo - 'QUIT' per uscire dalla procedura):$colore{'default'}\n";
	
	my $tmpRequiredBlock = <STDIN>;
	chomp ($tmpRequiredBlock);
	
	if ($tmpRequiredBlock =~ m/^QUIT$/i )	{
		goto USER_EXIT;
	} elsif ($tmpRequiredBlock =~ m/^__END_BLOCKS__/i )	{
		last REQUIRED_BLOCK;
	} else 	{
		if ($tmpRequiredBlock eq "")	{
			print "$colore{'rosso_scuro'}Parametro non passato$colore{'default'}\n";
			redo REQUIRED_BLOCK;
		}
		push @{$requiredBlocks}, $tmpRequiredBlock;
	} 
}

printf "$colore{'celeste'}Inserire un valore per il campo opzionale 'contractClaim' - ('INVIO' per lasciarlo vuoto - 'QUIT' per uscire dalla procedura):$colore{'default'}\n"; 

$contractClaim = <STDIN>;
chomp ($contractClaim);

goto USER_EXIT if $backgroundURL =~ m/^QUIT$/i;

printf "$colore{'celeste'}Inserire un valore per il campo opzionale 'backgroundURL' - ('INVIO' per lasciarlo vuoto - 'QUIT' per uscire dalla procedura):$colore{'default'}\n"; 

$backgroundURL = <STDIN>;
chomp ($backgroundURL);

goto USER_EXIT if $backgroundURL =~ m/^QUIT$/i;  

printf "$colore{'celeste'}Inserire un valore per il campo opzionale 'logoURL' - ('INVIO' per lasciarlo vuoto - 'QUIT' per uscire dalla procedura):$colore{'default'}\n"; 

$logoURL = <STDIN>;
chomp ($logoURL);

goto USER_EXIT if $logoURL =~ m/^QUIT$/i;  

SUMMARY:
$logger->info("$colore{'underline'}Sono stati inseriti i seguenti dati tecnici:$colore{'default'}");
$logger->info("contractId (* obbligatorio): ". $contractId );
$logger->info("contractName (* obbligatorio): ". $contractName );
$logger->info("operationalContext (* obbligatorio): ". $operationalContext );
$logger->info("externalWorkTypeId (* obbligatorio): ". $externalWorkTypeId );
$logger->info("requiredBlocks [opzionale]: ". Dumper ($requiredBlocks) ) if scalar @{$requiredBlocks};
$logger->info("contractClaim [opzionale]: ". $contractClaim ) if $contractClaim;
$logger->info("backgroundURL [opzionale]: ". $backgroundURL ) if $backgroundURL;
$logger->info("logoURL [opzionale]: ". $logoURL ) if $logoURL;

print "$colore{'celeste_scuro'}$colore{'underline'}SI VUOLE CONFERMARE?$colore{'default'}\n";

my $conferma = _conferma ();

if ( $conferma == 0)  {
	$contractId = "";
	$contractName = "";
	$operationalContext = "";
	$requiredBlocks = [] if scalar @{$requiredBlocks};
	$contractClaim = "" if $contractClaim;
	$backgroundURL = "" if $backgroundURL;			
	$logoURL = "" if $logoURL;		
	goto OUTER;
} elsif ($conferma == 2) {
	goto USER_EXIT;
} else { # vale 1, ovvero SI
	# controllo che se e' gia' presente a sistema
	my %search_contract_params = (
		contractId => $contractId
	);
	
	my $collContracts = $wpsoap->contracts(customerId => $customerId );
		
	my $contract_system  = $collContracts->cerca(%search_contract_params);
	
	# aggiungo parametri opzionali
	$contract_params{requiredBlocks} = $requiredBlocks if scalar @{$requiredBlocks};
	$contract_params{contractClaim} = $contractClaim if $contractClaim;
	$contract_params{backgroundURL} = $backgroundURL if $backgroundURL;
	$contract_params{logoURL} = $logoURL if $logoURL;
	
	if(scalar @{$contract_system}) { # se esiste allino i gruppi a quelli del padre
		delete $contract_params{contractId};
		
		my $up = $contract_system->[0]->update_contract(
			PROPERTIES => \%contract_params
		);
		
		if(defined $up) {
			$logger->info("contractId '$contractId' aggiornato con successo (ID ".$contract_system->[0]->id().")!");
		} else {
			$logger->fatal("Impossibile aggiornare il sistema '$contractId': ".$api_art->last_error());
			exit ERROR_CANNOT_CREATE_CONTRACT;
		}
	} else {
		my $contract = $collContracts->crea(%contract_params);
	
		if(defined $contract) {
			$logger->info("contractId '$contractId' creato con successo (ID ".$contract->id().")!");
		} else {
			$logger->fatal("Impossibile creare il sistema '$contractId': ".$api_art->last_error());
			exit ERROR_CANNOT_CREATE_CONTRACT;
		}
	}
} 

if( $commit ) {
	$logger->debug("Eseguo la commit delle modifiche fatte sul db");
	$api_art->save();
	goto LAST;
} else {
	$logger->warn("Non effettuo la commit in quanto sono in modalita' test");
	goto LAST;
}

NO_CUSTOMER_FOUND: 
$logger->error("customer '$customerId' non esiste!");
goto LAST;

USER_EXIT:
$logger->warn("$colore{'giallo_scuro'}Si è scelto di uscire dalla procedura$colore{'default'}"); 
$contractId = "" if	$contractId;
$contractName = "" if 	$contractName;
$backgroundURL = "" if 	$backgroundURL;
$logoURL = "" if 	$logoURL;
goto LAST;

LAST:
$logger->info( '===== END ======================================================================' );

exit ERROR_OK;
