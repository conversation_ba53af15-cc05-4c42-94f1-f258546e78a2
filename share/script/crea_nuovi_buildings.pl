#!/usr/bin/perl

use strict;
use warnings;
use Carp qw( verbose croak );
use Data::Dumper;
use Getopt::Long;
Getopt::Long::Configure (qw(bundling no_ignore_case no_auto_abbrev));
use File::Basename;
use Log::Log4perl qw(get_logger :levels);
use Date::Calc qw(:all);

use API::ART;
use SIRTI::ART::Lookup::ART;
use WPSOAP;
use WPSOAP::Controller::BUILDING;

my $logger_name  = 'WPSOAP::BIN::crea_nuovi_buildings';
# copio i dati di @ARGV che verra' svuotato da Getopt
my @ARGV_BACKUP = @ARGV;
my $command_line = basename($0).' '.join(" ", map { /\s/ ? "'$_'" : $_ } @ARGV);

# ENUMerazione degli errori
use enum qw	(:ERROR_=0
	OK=0
	SHOW_USAGE=0
	WRONG_OPTIONS
	MISSING_ID_CLIENTE_OR_ID_CONTRATTO
	UNABLE_TO_LOAD_LOG_CONFIG
	CANNOT_INIT_API_ART
	CANNOT_INIT_WPSOAP
	CANNOT_INIT_WPSOAP_BUILDINGS
	UNABLE_TO_CREATE_NEW_BUILDINGS
);

sub usage( $ ) {
	my $rc = shift;
	my $file = grep( /^$rc$/, 0, 0 ) ? *STDOUT : *STDERR;
	print $file "
	$0  [<opzioni>] <customerId> <contractId>
	
	Questo script si collega a SiNFO per ottenere i buildings per ciascun progetto locale e li crea o modifica di conseguenza

	<opzioni>:
		--project-id=<projectId> - Cerca i building solo sul progetto specificato
		-a, --artid=<ARTID> - ARTID delle API::ART a cui connettersi (DEFAULT variabile di sistema WPSOAP_ARTID)
		-u, --api-user=<USER> - USER name dell'utente ART (DEFAULT variabile di sistema WPSOAP_SCRIPT_USER)
		-p, --api-password=<PASSWORD> - PASSWORD dello USER (DEFAULT variabile di sistema WPSOAP_SCRIPT_PASSWORD)
		-t, --transaction-mode=<r|c> - se impostato a 'c' esegue un commit alla fine dell'elaborazione di ogni lotto,
			altrimenti esegue un rollback. per default viene preso il valore della variabile d'ambiente
			WPSOAP_TRANSACTION_MODE o se non impostata 'r' (rollback)
		--commit-after-every-building - Effettua una commit dopo aver lavorato ciascun building. Ignorato se --transaction-mode=r
		-D, --daemon=<secondi> - prepara l'ambiente per eseguire come daemon effettuando il polling ogni <secondi>
			utilizzare start_stop_daemon per lo start e lo stop
		-l, --log-config=<log_config> - file di lavorazione per il logging, per default utilizzato il valore della
			variabile d'ambiente LOG4PERL_CONF
		-d, --debug - attiva messaggi di debug per le API::ART
		-h, --help - questo aiuto
	[*] obbligatorio

";
	exit $rc;
}

my ( $help, $artid, $api_script_user, $api_script_password, $commit, $log_config, $debug, $daemon, $waitsec, $project_id, $commit_after_every_building );

$artid               = $ENV{WPSOAP_ARTID};
$api_script_user     = $ENV{WPSOAP_SCRIPT_USER};
$api_script_password = $ENV{WPSOAP_SCRIPT_PASSWORD};
$log_config          = $ENV{LOG4PERL_CONF};
my $end_string = '===== END ======================================================================';

if ( defined $ENV{WPSOAP_TRANSACTION_MODE} && $ENV{WPSOAP_TRANSACTION_MODE} eq 'c' ) {
	$commit = 1;
} else {
	$commit = 0;
}

GetOptions (
	'help|h'=>				\$help,
	'project-id=s' =>		\$project_id,
	'artid|a=s' =>			\$artid,
	'api-user|u=s' =>		\$api_script_user,
	'api-password|p=s' =>	\$api_script_password,
	'commit-after-every-building' => \$commit_after_every_building,
	'transaction-mode|t=s'=> sub {
		if ( $_[1] eq 'r' ) {
			$commit = 0;
		} elsif ( $_[1] eq 'c' ) {
			$commit = 1;
		} else {
			die( "--transaction-mode puo' valere 'r' o 'c'\n" );
		}
	},
	'daemon|D=i'          => sub {
		$daemon     = 1;
		$waitsec    = $_[1];
		die "Con l'opzione --daemon, <secondi> deve essere > 0\n"
			unless $waitsec > 0;
	},
	'log-config|l=s'=>	\$log_config,
	'debug|d' =>		\$debug,
) or usage( ERROR_WRONG_OPTIONS );
usage( ERROR_SHOW_USAGE ) if ( $help );

my $id_cliente = $ARGV[0];
my $id_contratto = $ARGV[1];
usage( ERROR_MISSING_ID_CLIENTE_OR_ID_CONTRATTO )
	unless defined $id_cliente || defined $id_contratto;

# ripristino @ARGV che e' stato svuotato da Getopt
@ARGV = @ARGV_BACKUP;

my $tmp_log_file_prefix = $id_cliente . '_' . $id_contratto . '_';
$tmp_log_file_prefix =~ s/ /_/g;
$ENV{LOG_FILE_PREFIX} = $tmp_log_file_prefix;

eval { Log::Log4perl->init( $log_config ); };
if ( $@ ) {
	print STDERR "Impossibile inizializzare il sistema di logging: $@";
	exit ERROR_UNABLE_TO_LOAD_LOG_CONFIG;
}

my $logger = get_logger( $logger_name );
$logger->info( '===== BEGIN ====================================================================' );
$logger->info( $command_line );

my $keep_going = 1;
$SIG{HUP}  = sub {
	$logger->info("Intercettato SIGHUP: chiudo i log di log4perl");
	Log::Log4perl->init( $log_config );
	$logger = get_logger( $logger_name );
	$logger->info("Riapro i log di log4perl");
	$logger->info( $command_line );
};
$SIG{INT}  = sub { $logger->warn("Intercettato SIGINT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{QUIT} = sub { $logger->warn("Intercettato SIGQUIT: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };
$SIG{TERM} = sub { $logger->warn("Intercettato SIGTERM: termino le operazioni in corso ed esco"); $logger->info( '===== END ======================================================================' ); $keep_going = 0; };

$logger->debug( "Inizializzo API::ART" );
my $api_art;
eval {
	$api_art = API::ART->new(
		ARTID     => $artid
		,USER     => $api_script_user
		,PASSWORD => $api_script_password
		,AUTOSAVE => 0
		,DEBUG    => $debug
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare API::ART: $@" );
	exit ERROR_CANNOT_INIT_API_ART;
}
my $LOOKUP = $api_art->lookup_get();

my $wpsoap;
eval {
	$wpsoap = WPSOAP->new(
		ART => $api_art
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOAP: $@" );
	exit ERROR_CANNOT_INIT_WPSOAP;
}

my $buildings;
eval {
	$buildings = WPSOAP::Controller::BUILDING->new(
		WPSOAP => $wpsoap
	);
};
if ( $@ ) {
	$logger->fatal( "Impossibile inizializzare WPSOAP::Controller::BUILDING: $@" );
	exit ERROR_CANNOT_INIT_WPSOAP_BUILDINGS;
}

HEAD: while ( $keep_going ) {
	
	# ricarico almeno la lookup per avere le nuove configurazioni
	unless (defined $LOOKUP){
		$LOOKUP = eval{SIRTI::ART::Lookup::ART->new($api_art->_dbh())};
	
		$api_art->lookup_restore($LOOKUP);
	}
	
	$keep_going = 0
		unless $daemon;

	my %p = (customerId => $id_cliente, contractId => $id_contratto);
	if(defined $project_id) {
		$logger->info("Filtro solo sul progetto ".$project_id);
		$p{projectId} = $project_id;
	}
	if($commit && $commit_after_every_building) {
		$p{COMMIT_AFTER_EVERY_BUILDING} = 1;
	}
	if($buildings->create_or_update_buildings_from_sinfo(%p)) {
		$logger->info("Elaborazione buildings terminata");
	} else {
		$logger->fatal("Impossibile creare i nuovi buildings: " . $api_art->last_error());
		exit ERROR_UNABLE_TO_CREATE_NEW_BUILDINGS;
	}
	
	if( $commit ) {
		if(!$commit_after_every_building) {
			$logger->debug( "Eseguo la commit delle modifiche fatte sul db" );
			$api_art->save();
		}
	} else {
		$logger->warn( "Non effettuo la commit in quanto sono in modalita' test" );
	}
	
	if ( $daemon ) {
		undef $LOOKUP;
		$api_art->lookup_delete(); #distruggo la lookup per riapplicarla dopo
		$logger->info( '--------------------------------------------------------------------------------' );
		$logger->info( "Ho lavorato tutti i buildings trovati, aspetto $waitsec secondi" );
		sleep $waitsec;
		next HEAD;
	}
	
	$logger->info( '===== END ======================================================================' );

}

exit ERROR_OK;
