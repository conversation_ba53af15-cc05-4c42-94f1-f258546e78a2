# Language file for package API::ART::APP::Activity::TT001::Binding::Action::DETTAGLI_ANOMALIA_UNICARA.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"API::ART::APP::Activity::TT001::Binding::Action::DETTAGLI_ANOMALIA_UNICARA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:10+0200\n"
"PO-Revision-Date: 2025-07-17 11:10+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <PERSON><PERSON>belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity/TT001/Binding/Action\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: DETTAGLI_ANOMALIA_UNICARA.pm\n"

#: DETTAGLI_ANOMALIA_UNICARA.pm:20
#, perl-brace-format
msgid "action not available for activity with customersystem {customerSystem}"
msgstr ""
"azione non disponibile per attività con 'Sistema cliente' {customerSystem}"

#: DETTAGLI_ANOMALIA_UNICARA.pm:35
#, perl-brace-format
msgid ""
"Invalid reportingUser {reportingUser} value: must be alphanumeric and no "
"more than 8 characters"
msgstr ""
"Valore invalido per reportingUser {reportingUser}: deve essere alfanumerico "
"e non più lungo di 8 caratteri"

#: DETTAGLI_ANOMALIA_UNICARA.pm:44
msgid "Missing mandatory param ATTACHMENTS"
msgstr "Allegati obbligatori"

#: DETTAGLI_ANOMALIA_UNICARA.pm:50
msgid "Exceeded the maximum limit of 3 attachments"
msgstr "Superato il limite massimo di 3 allegati"

#: DETTAGLI_ANOMALIA_UNICARA.pm:60
msgid "Only .doc and .docx attachment type are allowed"
msgstr "Solamente allegati di tipo '.doc' e '.docx' sono permessi"

#: DETTAGLI_ANOMALIA_UNICARA.pm:64
#, perl-brace-format
msgid ""
"Incorrect attachment {format}, Only .doc and .docx attachment type are "
"allowed"
msgstr ""
"Formato allegato non valido {format}, Solamente allegati di tipo '.doc' e "
"'.docx' sono permessi"

#: DETTAGLI_ANOMALIA_UNICARA.pm:69
msgid "Found more than two ERROR attachments, maximum two allowed"
msgstr "Trovati più di due allegati di tipo ERROR"

#: DETTAGLI_ANOMALIA_UNICARA.pm:74
msgid "Only .log attachment type are allowed"
msgstr "Solamente allegati di tipo '.log' sono permessi"

#: DETTAGLI_ANOMALIA_UNICARA.pm:78
#, perl-brace-format
msgid "Incorrect attachment {format}, Only .log attachment type are allowed"
msgstr ""
"Formato allegato non valido {format}, Solamente allegati di tipo '.log' sono "
"permessi"

#: DETTAGLI_ANOMALIA_UNICARA.pm:86
msgid "Attachment size exceeds the 1.5 MB limit"
msgstr "Ecceduta dimensione massima file allegati pari a 1.5 MB"

#: DETTAGLI_ANOMALIA_UNICARA.pm:92
msgid "Missing mandatory attachments of type ERROR_1 or ERROR_2"
msgstr "Allegati obbligatori di tipo ERROR_1 o ERROR_2 mancanti"

#: DETTAGLI_ANOMALIA_UNICARA.pm:141
#, perl-brace-format
msgid "Unable to find email for Username {username} "
msgstr "Impossibile trovare l'email for il nome utente {username} "

#: DETTAGLI_ANOMALIA_UNICARA.pm:161
#, perl-brace-format
msgid "Unable to find email for {label}:{clusterManagerId}"
msgstr "Impossibile trovare l'email per {label}:{clusterManagerId}"

#, perl-brace-format
#~ msgid "Unable to find email for {label}:{technicalAssistantId}"
#~ msgstr "Impossibile trovare l'email per {label}:{technicalAssistantId}"

#, perl-brace-format
#~ msgid "Missing mandatory param '{key}'"
#~ msgstr "Parametro obbligatorio mancante '{key}'"

#, perl-brace-format
#~ msgid "param '{key}' can be worth a maximum of 999"
#~ msgstr "il parametro '{key}' può valere al massimo 999"

#, perl-brace-format
#~ msgid "Invalid email {email}"
#~ msgstr "Email invalida {email}"
