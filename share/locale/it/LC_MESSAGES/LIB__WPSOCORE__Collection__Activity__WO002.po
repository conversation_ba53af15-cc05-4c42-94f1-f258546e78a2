# Language file for package WPSOCORE::Collection::Activity::WO002
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::WO002\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WO002.pm\n"

#: WO002.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Paramentro obbligatorio {paramname} mancante"

#: WO002.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: WO002.pm:94
#, perl-brace-format
msgid "Param {param} must be an array of hashes"
msgstr "Il parametro {param} deve essere un array di hash"

#: WO002.pm:125
#, perl-brace-format
msgid ""
"Found more than one configuration for macroactivity {macro} and working "
"group code {wgc}"
msgstr ""
"Trovata più di una configurazione per macroattività {macro} e centro lavoro "
"{wgc}"

#: WO002.pm:128
#, perl-brace-format
msgid ""
"No configuration found for macroactivity {macro} and working group code {wgc}"
msgstr ""
"Nessuna configurazione trovata per macroattività {macro} e centro lavoro "
"{wgc}"
