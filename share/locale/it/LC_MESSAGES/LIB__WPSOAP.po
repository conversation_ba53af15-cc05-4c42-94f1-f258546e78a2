# Language file for package WPSOAP
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:10+0200\n"
"PO-Revision-Date: 2025-07-17 11:10+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.liv<PERSON><EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOAP.pm\n"

#: WPSOAP.pm:36
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: WPSOAP.pm:37
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: WPSOAP.pm:144
msgid "You cannot use BODY and RAW_BODY jointly"
msgstr "Impossibile usare BODY e RAW_BODY insieme"

#: WPSOAP.pm:148
msgid "You cannot use BODY param with method GET or DELETE"
msgstr "Non puoi usare il parametro BODY con il metodo GET o DELETE"

#: WPSOAP.pm:209
#, perl-brace-format
msgid ""
"Unable to parse server response (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"
msgstr ""
"Impossibile effettuare il parsing della risposta del server (HTTP_STATUS: "
"{response_code}, MESSAGE: {response_content})"

#: WPSOAP.pm:216
#, perl-brace-format
msgid ""
"Client error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato client (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: WPSOAP.pm:220
#, perl-brace-format
msgid ""
"Server error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato server (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: WPSOAP.pm:422
#, perl-brace-format
msgid "Missing mapping for customerId {customerId} and contractId {contractId}"
msgstr "Mappa mancante per customer {customerId} e cliente {contractId}"

#: WPSOAP.pm:458
#, perl-brace-format
msgid "Missing env {env}"
msgstr "Manca env {env}"
