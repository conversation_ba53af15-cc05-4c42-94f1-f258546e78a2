# Language file for package WPSOAP::MQ::Consumer::PERMESSO_BUILDING::LC02Booking
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOAP::MQ::Consumer::PERMESSO_BUILDING::LC02Booking\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON> <F<PERSON>@ext.sirti.it>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer/"
"PERMESSO_BUILDING\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC02Booking.pm\n"

#: LC02Booking.pm:73
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: LC02Booking.pm:74
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: LC02Booking.pm:79
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: LC02Booking.pm:88
#, perl-brace-format
msgid "Bad JSON {err}"
msgstr "JSON non valido {err}"

#: LC02Booking.pm:106
#, perl-brace-format
msgid "Successfully created activity {id}: current status {status}"
msgstr "Attività {id} creata con successo: stato corrente {status}"

#: LC02Booking.pm:132
#, perl-brace-format
msgid "Unknown action {action}"
msgstr "Azione {action} sconosciuta"

#: LC02Booking.pm:139
#, perl-brace-format
msgid "Unable to execute action {action} for activity {id}"
msgstr "Impossibile eseguire l'azione {action} per l'attività {id}"

#: LC02Booking.pm:157
#, perl-brace-format
msgid "Successfully worked activity {id}: current status {status}"
msgstr "Attività {id} lavorata con successo: stato corrente {status}"

#: LC02Booking.pm:160
msgid "All permits has been worked"
msgstr "Tutti i permessi sono stati lavorati"
