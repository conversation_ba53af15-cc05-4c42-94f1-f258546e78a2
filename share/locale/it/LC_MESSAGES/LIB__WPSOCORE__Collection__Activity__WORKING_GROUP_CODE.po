# Language file for package WPSOCORE::Collection::Activity::WORKING_GROUP_CODE
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::WORKING_GROUP_CODE\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WORKING_GROUP_CODE.pm\n"

#: WORKING_GROUP_CODE.pm:29
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro {paramname} obbligatorio mancante"

#: WORKING_GROUP_CODE.pm:30
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: WORKING_GROUP_CODE.pm:87
#, perl-brace-format
msgid ""
"System already present fro companyAbbreviation {companyAbbreviation} and "
"workingGroupCode {workingGroupCode}"
msgstr ""
"Sistema già presente per companyAbbreviation {companyAbbreviation} e centro "
"di lavoro {workingGroupCode}"
