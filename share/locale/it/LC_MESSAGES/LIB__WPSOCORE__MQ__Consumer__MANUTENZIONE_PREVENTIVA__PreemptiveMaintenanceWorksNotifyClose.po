# Language file for package WPSOCORE::MQ::Consumer::MANUTENZIONE_PREVENTIVA::PreemptiveMaintenanceWorksNotifyClose
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::MANUTENZIONE_PREVENTIVA::PreemptiveMaintenanceWorksNotifyClose\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <PERSON><PERSON><EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/"
"MANUTENZIONE_PREVENTIVA\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: PreemptiveMaintenanceWorksNotifyClose.pm\n"

#: PreemptiveMaintenanceWorksNotifyClose.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: PreemptiveMaintenanceWorksNotifyClose.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: PreemptiveMaintenanceWorksNotifyClose.pm:103
#, perl-brace-format
msgid "Missing opening event for work activity id {workActivityId}"
msgstr ""
"Evento di apertura mancante per attività di lavoro con id {workActivityId}"

#: PreemptiveMaintenanceWorksNotifyClose.pm:116
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: PreemptiveMaintenanceWorksNotifyClose.pm:121
#, perl-brace-format
msgid "Unable to find ongoing activity {id}"
msgstr "Impossibile trovare attività {id} in corso"
