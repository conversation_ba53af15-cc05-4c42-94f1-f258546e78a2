# Language file for package WPSOAP::MQ::Consumer::AP::GestioneTT
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::MQ::Consumer::AP::GestioneTT\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer/AP\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: GestioneTT.pm\n"

#: GestioneTT.pm:66
#, perl-brace-format
msgid "Unable to search project: {error}"
msgstr "Impossibile trovare il progetto: {error}"

#: GestioneTT.pm:94
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: GestioneTT.pm:95
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: GestioneTT.pm:100
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: GestioneTT.pm:114
#, perl-brace-format
msgid "Verifing if permits area {permitsAreaId} exists"
msgstr "Verifico se l'area permessi {permitsAreaId} esiste"

#: GestioneTT.pm:116
msgid "Unable to search permitsAreaId"
msgstr "Impossibile ricercare permitsAreaId"

#: GestioneTT.pm:120
#, perl-brace-format
msgid "permitsAreaId {permitsAreaId} not found"
msgstr "permitsAreaId {permitsAreaId} non trovato"

#: GestioneTT.pm:131
msgid "Unable to find AP_LC activity"
msgstr "Impossibile trovare l'attività AP_LC"

#: GestioneTT.pm:134
#, perl-brace-format
msgid "Found AP_LC active activity {number}"
msgstr "Trovate {number} attività AP_LC attive"

#: GestioneTT.pm:144
#, perl-brace-format
msgid "Unable to step AP_LC activity: {error}"
msgstr "Impossibile effettuare lo step sull'attività AP_LC: {error}"

#: GestioneTT.pm:149
#, perl-brace-format
msgid "permitsAreaId {permitsAreaId} found"
msgstr "permitsAreaId {permitsAreaId} trovato"
