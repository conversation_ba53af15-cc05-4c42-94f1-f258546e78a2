# Language file for package WPSOCORE::Collection::Activity::MANUTENZIONE_CORRETTIVA
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::MANUTENZIONE_CORRETTIVA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: MANUTENZIONE_CORRETTIVA.pm\n"

#: MANUTENZIONE_CORRETTIVA.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: MANUTENZIONE_CORRETTIVA.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: MANUTENZIONE_CORRETTIVA.pm:140
#, perl-brace-format
msgid "Parameter \"{param}\" = '{value}' is not a valid email address"
msgstr "Parametro \"{param}\" = '{value}' non è un indirizzo email valido"

#: MANUTENZIONE_CORRETTIVA.pm:208
#, perl-brace-format
msgid "Unable to find {type} with id {id}"
msgstr "Impossibile trovare {type} con id {id}"

#: MANUTENZIONE_CORRETTIVA.pm:208
msgid "directive"
msgstr "direttrice"

#~ msgid "Invalid creation mode"
#~ msgstr "Modalità creazione invalida"

#, perl-brace-format
#~ msgid "Missing cables for activity type '{param}'"
#~ msgstr "Cavi mancanti per tipo attivita '{param}'"

#, perl-brace-format
#~ msgid "Missing param {param}"
#~ msgstr "Parametro {param} mancante"

#, perl-brace-format
#~ msgid "Missing param '{param}'"
#~ msgstr "Parametro '{param}' mancante"

#~ msgid "Unable to find AOA"
#~ msgstr "Impossibile trovare AOA"

#, perl-brace-format
#~ msgid ""
#~ "reporter value {reporter} is only allowed when mntActivityType equals "
#~ "{allowed}"
#~ msgstr ""
#~ "il valore di \"segnalato da\" {reporter} è permesso solo quando \"tipo "
#~ "attività\" è uguale a {allowed}"

#~ msgid "section"
#~ msgstr "sezione"

#~ msgid "cable"
#~ msgstr "cavo"

#, perl-brace-format
#~ msgid ""
#~ "Mismatch between workingGroupCode {wgc} and cable's workinGroupCode {wgcc}"
#~ msgstr ""
#~ "Incongruenza tra il codice lavoro {wgc} il codice lavoro dei cavi {wgcc}"
