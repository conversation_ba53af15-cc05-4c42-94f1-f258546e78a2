# Language file for package WPSOCORE::MQ::Consumer::MANUTENZIONE_CORRETTIVA::BookingACK
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::MANUTENZIONE_CORRETTIVA::BookingACK\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, r.belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/"
"MANUTENZIONE_CORRETTIVA\n"
"X-Poedit-KeywordsList: __;;"
"%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: BookingACK.pm\n"

#: BookingACK.pm:67
msgid "Working RA_ID ack..."
msgstr "Lavoro l'ack RA_ID..."

#: BookingACK.pm:68
#, perl-brace-format
msgid "{type}: {value}"
msgstr "{type}: {value}"

#: BookingACK.pm:68
msgid "GLOBAL"
msgstr "GLOBAL"

#: BookingACK.pm:68
msgid "TARGET ACK"
msgstr "TARGET ACK"

#: BookingACK.pm:71
msgid "Unnecessary"
msgstr "Non necessario"

#: BookingACK.pm:79
#, perl-brace-format
msgid "Error retrieving activity {id}: {error}"
msgstr "Errore nel recupero dell'attività {id}: {error}"

#: BookingACK.pm:89
#, perl-brace-format
msgid "ACK KO for activity {id} not managed: invalid JSON"
msgstr "ACK KO per l'attività {id} non gestita: JSON invalido"

#: BookingACK.pm:110
#, perl-brace-format
msgid "ACK KO for activity {id} correctly managed"
msgstr "ACK KO per l'attività {id} gestito correttamente"
