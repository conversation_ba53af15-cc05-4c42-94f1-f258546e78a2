# Language file for package WPSOCORE::Collection::Activity::LC02
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::LC02\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC02.pm\n"

#: LC02.pm:41
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: LC02.pm:42
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LC02.pm:142
msgid "No active ROEs found"
msgstr "Non è stato trovato alcun ROE attivo"

#: LC02.pm:270
#, perl-brace-format
msgid "Unable to recover id_intervento for Cabinet OdS: '{ODS}'"
msgstr "Impossibile recuperare id_intervento per l'armadio OdS: '{ODS}'"

#: LC02.pm:293
#, perl-brace-format
msgid "externalId found ({found}) inconsistent with expected ({expected})"
msgstr "externalId ({found}) inconsistente col valore atteso ({expected})"

#: LC02.pm:300
msgid "Unable to update EXTERNAL_SYNC system"
msgstr "Impossibile aggiornare il sistema EXTERNAL_SYNC"

#: LC02.pm:456
msgid "Cabinet id must be a string of 3 alphanumeric characters"
msgstr ""
"L'identificativo dell'armadio deve essere una stringa di 3 caratteri "
"alfanumerici"

#: LC02.pm:464
#, perl-brace-format
msgid "Missing mandatory parameter '{key}' for elementType '{elementType}'"
msgstr ""
"Parametro obbligatorio '{key}' mancante per il tipo apparato '{elementType}'"

#: LC02.pm:470
#, perl-brace-format
msgid ""
"cabinetType must be 'CNO', not '{cabinetType}', for elementType "
"'{elementType}'"
msgstr ""
"TIPO ARMADIO dev'essere 'CNO', non '{cabinetType}, quando TIPO ELEMENTO  è "
"'{elementType}'"

#: LC02.pm:479
msgid "idCNO must be a string of 3 alphanumeric characters"
msgstr ""
"L'identificativo del CNO deve essere una stringa di 3 caratteri alfanumeri"

#: LC02.pm:485
#, perl-brace-format
msgid ""
"Parameter networkBasedUpon must be either 'ARMADIO' or 'CNO', not '{value}'"
msgstr ""
"Il parametro NETWORK SU BASE deve essere 'ARMADIO' o 'CNO', non '{value}'"

#: LC02.pm:496
#, perl-brace-format
msgid ""
"cabinetType must be 'ARLO', not '{cabinetType}', for elementType "
"'{elementType}'"
msgstr ""
"TIPO ARMADIO dev'essere 'ARLO', non '{cabinetType}, quando TIPO ELEMENTO è "
"'{elementType}'"

#: LC02.pm:504
#, perl-brace-format
msgid "Parameter 'idCNO' is not compatible with cabinetType '{cabinetType}'"
msgstr ""
"Il parametro ID CNO non è compatibile con il TIPO ARMADIO  '{cabinetType}'"

#: LC02.pm:511
#, perl-brace-format
msgid ""
"Parameter 'networkBasedUpon' is not compatible with cabinetType "
"'{cabinetType}'"
msgstr ""
"Il parametro NETWORK SU BASE non è compatibile con il cabinetType "
"'{cabinetType}'"

#: LC02.pm:526
#, perl-brace-format
msgid "Parameter planningFirm is only allowed for the following cities: {city}"
msgstr ""
"Il parametro IMPRESA PROGETTAZIONE può essere valorizzato solo per le città "
"seguenti: {city}"

#: LC02.pm:537
#, perl-brace-format
msgid "Planning Firm {planningFirm} was not found"
msgstr "Impresa Progettazione {planningFirm} non trovata"

#: LC02.pm:546
#, perl-brace-format
msgid ""
"Invalid workPerformance {workPerformance}, valid values are "
"{allowedWorkPerformances} "
msgstr ""
"Prestazione {workPerformance} non valida: i valori possibili sono "
"{allowedWorkPerformances} "

#: LC02.pm:623
#, perl-brace-format
msgid "Find more than one customer for {customerId}"
msgstr "Trovato più di un cliente per {customerId}"

#: LC02.pm:626
#, perl-brace-format
msgid "Unable to find customer for {customerId}"
msgstr "Impossibile trovare il cliente per {customerId}"

#: LC02.pm:649
#, perl-brace-format
msgid "Find more than one contract for {contractId}"
msgstr "Trovato più di un contratto per {contractId}"

#: LC02.pm:652
#, perl-brace-format
msgid "Unable to find contract for {contractId}"
msgstr "Impossibile trovare il contratto per {contractId}"

#: LC02.pm:673
#, perl-brace-format
msgid "No wbe info found for wbe {wbe} and working group code {wgc}"
msgstr "Nessuna informazione trovata per wbe {wbe} e centro di lavoro {wgc}"

#: LC02.pm:680
#, perl-brace-format
msgid "Found more than one wbe info for wbe {wbe} and working group code {wgc}"
msgstr ""
"Trovata più di un'informazione per la wbe {wbe} e il centro di lavoro {wgc}"

#: LC02.pm:688
#, perl-brace-format
msgid "{param} not found for wbe: {wbe}"
msgstr "{param} non trovato per la WBE {wbe}"

#: LC02.pm:720
#, perl-brace-format
msgid "Unable to find data for work order id {workOrderId} and wbe {wbe}"
msgstr "Impossibile trovare dati per work order {workOrderId} e wbe {wbe}"

#: LC02.pm:730
#, perl-brace-format
msgid ""
"Parameter {param} must be present for a FTTH PTE with accounting {accounting}"
msgstr ""
"Il parametro {param} deve essere presente per un PTE FTTH con contabilità "
"{accounting}"

#: LC02.pm:735
#, perl-brace-format
msgid "Invalid cluster {cluster}, valid values are {allowedClusters} "
msgstr "Cluster {cluster} invalido, i valori validi sono {allowedClusters} "

#: LC02.pm:743
#, perl-brace-format
msgid ""
"Invalid penetrationIndex {penetrationIndex} for cluster {cluster}, valid "
"values are {allowedPIs} "
msgstr ""
"Fascia rendicontazione {penetrationIndex} non valida per il cluster "
"{cluster}, i valori permessi sono {allowedPIs} "

#: LC02.pm:751
#, perl-brace-format
msgid ""
"Invalid externalMacroActivityId {externalMacroActivityId} for "
"clusterJobReport {clusterJobReport}: valid value is {expected} "
msgstr ""
"Macroattività {externalMacroActivityId} invalida per il cluster "
"{clusterJobReport}: il valore valido è {expected} "

#: LC02.pm:759
#, perl-brace-format
msgid ""
"Invalid clusterJobReport {clusterJobReport} for externalMacroActivityId "
"{externalMacroActivityId}: valid value is {expected} "
msgstr ""
"Cluster {clusterJobReport} invalido per la macro attività "
"{externalMacroActivityId}: il valore valido è {expected} "

#: LC02.pm:769
#, perl-brace-format
msgid ""
"Parameter {param} must not be present for a FTTH PTE with accounting "
"{accounting}"
msgstr ""
"Il parametro {param} non deve essere presente per un PTE FTTH con "
"contabilità {accounting}"

#: LC02.pm:776
msgid "Parameter clusteJobReport must not be present for a Creation PTE"
msgstr "Il parametro Cluster non deve essere presente per un PTE Creation"

#: LC02.pm:793
#, perl-brace-format
msgid "{param} not found for {param1} {value1}"
msgstr "{param} non trovato per {param1} {value1}"

#: LC02.pm:799
#, perl-brace-format
msgid ""
"Working group code {value} not available for central with id {centralId}"
msgstr ""
"Il gruppo di lavoro {value} non è disponibile per la centrale con id "
"{centralId}"

#: LC02.pm:804
msgid "no workingGroupCode found for central"
msgstr "nessun centro di lavoro trovato per la centrale"

#: LC02.pm:886
#, perl-brace-format
msgid "EL02 system {id} already exists and it is tested"
msgstr "Il sistema EL02 {id} esiste già ed è collaudato"

#: LC02.pm:989
#, perl-brace-format
msgid "PTE with id {pte} already exists (activity {id})"
msgstr "PTE con {pte} già esistente (attività {id})"

#: LC02.pm:1663 LC02.pm:1673 LC02.pm:1683
#, perl-brace-format
msgid "At least one param between {param} and {param1} must be defined"
msgstr "Almeno un parametro tra {param} e {param1} deve essere definito"

#: LC02.pm:1668 LC02.pm:1678 LC02.pm:1688
#, perl-brace-format
msgid "Onlyone param between {param} and {param1} must be defined"
msgstr "Solo un parametro tra {param} e {param1} deve essere definito"

#: LC02.pm:1716 LC02.pm:1892
#, perl-brace-format
msgid "Too many active networks found with id {paymentNetwork}"
msgstr "Troppo network attive trovate con id {paymentNetwork}"

#: LC02.pm:1729
#, perl-brace-format
msgid "Network {paymentNetwork} in status ({status}): unable to associate"
msgstr "Network {paymentNetwork} nello stato ({status}): impossibile associare"

#: LC02.pm:1745
#, perl-brace-format
msgid ""
"Network {paymentNetwork} not associated to the central: expected {expected}, "
"found {found}"
msgstr ""
"Network {paymentNetwork} non associata alla centrale : trovata {found}, "
"attesa {expected}"

#: LC02.pm:1755
#, perl-brace-format
msgid "Network {paymentNetwork} with purpose not equal to {purpose}"
msgstr "Network {paymentNetwork} con scopo non uguale a {purpose}"

#: LC02.pm:1762 LC02.pm:1771
#, perl-brace-format
msgid ""
"Network {paymentNetwork} of wrong type: expected {expected}, found {found}"
msgstr ""
"Network {paymentNetwork} di tipo errato: trovato {found}, atteso {expected}"

#: LC02.pm:1781
#, perl-brace-format
msgid ""
"Network {paymentNetwork} with unexpected contract: found {found}, expected "
"{expected}"
msgstr ""
"Network {paymentNetwork} con contratto inatteso : trovato {found}, atteso "
"{expected}"

#: LC02.pm:1791
#, perl-brace-format
msgid ""
"Network {paymentNetwork} with different WBE ({networkWBE}) than PTE "
"({PTEWBE})"
msgstr ""
"Network {paymentNetwork} con WBE differente ({networkWBE}) rispetto al PTE "
"({PTEWBE})"

#: LC02.pm:1800
#, perl-brace-format
msgid ""
"Network {paymentNetwork} with different working group code ({networkWGC}) "
"than PTE ({PTEWGC})"
msgstr ""
"Network {paymentNetwork} con un differente centro di lavoro ({networkWGC}) "
"rispetto al PTE ({PTEWGC})"

#: LC02.pm:1809
#, perl-brace-format
msgid ""
"Work order {workOrderId} not coherent with network ({paymentNetwork}) work "
"order {networkWorkOrderId}"
msgstr ""
"ODA {workOrderId} non coerente con la l'oda {networkWorkOrderId} della "
"network ({paymentNetwork})"

#: LC02.pm:1820
#, perl-brace-format
msgid "Network {paymentNetwork} in status {status}: {error}"
msgstr "Network {paymentNetwork} in stato {status}: {error}"

#: LC02.pm:1823
msgid "unable to update"
msgstr "impossibile aggiornare"

#: LC02.pm:1831 LC02.pm:1853 LC02.pm:1917 LC02.pm:1920
#, perl-brace-format
msgid "Network {paymentNetwork} already associated to cabinet {cabinet}"
msgstr "La network {paymentNetwork} è già associata all'armadio {cabinet}"

#: LC02.pm:1872 LC02.pm:1899
#, perl-brace-format
msgid "Network {paymentNetwork} not found"
msgstr "Network {paymentNetwork} non trovata"

#: LC02.pm:1955
#, perl-brace-format
msgid "Anomaly => incoherent PTE id: expected {exp}, found {found}"
msgstr "Anomalia => Id PTE incoerente: atteso {exp}, trovato {found}"

#: LC02.pm:1974
msgid ""
"Unable to change network on pte already take in charge when old work order "
"id is different than new work order id"
msgstr ""
"Impossibile modificare la network per pte già presi in carico in quanto la "
"precedente oda è differente dalla nuova"

#, perl-brace-format
#~ msgid "LC02 activity {id} already exists"
#~ msgstr "L'attività LCO2 {id} esiste già"

#, fuzzy
#~| msgid "Parameter clusteJobReport must be present for a Fibercop PTE"
#~ msgid "Parameter clusteJobReport must be present for a FTTH PTE"
#~ msgstr "Il parametro Cluster deve essere presente per un PTE Fibercop"

#~ msgid "Parameter paymentNetwork must not be present for a Fibercop PTE"
#~ msgstr ""
#~ "Il parametro NW Creation non deve essere presente per un PTE Fibercop"

#~ msgid "Parameter network must not be present for a Creation PTE"
#~ msgstr "Il parametro network non deve essere presente per un PTE Creation"

#, perl-brace-format
#~ msgid "No wbe info found for wbe {wbe}"
#~ msgstr "Nessuna informazione trovata per la WBE {wbe}"

#, perl-brace-format
#~ msgid "Found more than one wbe info for wbe {wbe}"
#~ msgstr "Trovata più di una configurazione per la WBE {wbe}"

#~ msgid "Mandatory parameter WBELevel3 missing"
#~ msgstr "Parametro obbligatorio WBELevel3 mancante"

#~ msgid "workingGroupCode not uniquely determined"
#~ msgstr "gruppo di lavoro non univocamente determinato"

#, perl-brace-format
#~ msgid "PTE WBE '{PTEWBE}' differs from Network WBE '{NetworkWBE}'"
#~ msgstr ""
#~ "La WBE associata a questo PTE '{PTEWBE}' è diversa dalla WBE associata "
#~ "alla network '{NetworkWBE}'"

#~ msgid "Roe migration not available with teamAssistant and without networkId"
#~ msgstr ""
#~ "La migrazione dei ROE non è disponibile quando teamAssistant è definito e "
#~ "networkId non lo è"

#, perl-brace-format
#~ msgid ""
#~ "PTE teamAssistant '{PTEAss}' differs from Network teamAssistant "
#~ "'{NetworkAss}'"
#~ msgstr ""
#~ "Il teamAssistant del PTE '{PTEAss}' differisce dal teamAssistant della "
#~ "Network '{NetworkAss}'"

#, perl-brace-format
#~ msgid ""
#~ "PTE working group code '{PTEWGC}' differs from Cabinet working group code "
#~ "'{CabinetWGC}'"
#~ msgstr ""
#~ "Il centro di lavoro del PTE '{PTEWGC}' differisce dal centro di lavoro "
#~ "dell'armadio '{CabinetWGC}'"

#, perl-brace-format
#~ msgid "Cabinet {cabinet} is not enabled for PTE upload"
#~ msgstr "L'armadio {cabinet} non è abilitato all'aggiornamento dei PTE"

#, perl-brace-format
#~ msgid "Cannot update cabinet with teamAssistant '{PTEAss}' without network'"
#~ msgstr ""
#~ "Impossibile aggiornare l'armadio con il teamAssistant '{PTEAss}', ma "
#~ "senza network"
