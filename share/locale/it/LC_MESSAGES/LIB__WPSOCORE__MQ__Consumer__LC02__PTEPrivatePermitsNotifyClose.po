# Language file for package WPSOCORE::MQ::Consumer::LC02::PTEPrivatePermitsNotifyClose
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::LC02::PTEPrivatePermitsNotifyClose\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> "
"<F.<PERSON>@ext.sirti.it>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, r.belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/LC02\n"
"X-Poedit-KeywordsList: __;;"
"%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: PTEPrivatePermitsNotifyClose.pm\n"

#: PTEPrivatePermitsNotifyClose.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: PTEPrivatePermitsNotifyClose.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: PTEPrivatePermitsNotifyClose.pm:91
#, perl-brace-format
msgid "Found {n} lc02 activity: expected {m}"
msgstr "Trovate {n} attività LC02: attese {m}"

#: PTEPrivatePermitsNotifyClose.pm:110
#, perl-brace-format
msgid ""
"Missing opening event for private permits activity id "
"{privatePermitsActivityId}"
msgstr ""
"Manca l'evento di apertura per l'attività di permesso privato "
"{privatePermitsActivityId}"

#: PTEPrivatePermitsNotifyClose.pm:123
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: PTEPrivatePermitsNotifyClose.pm:129
msgid "Unable to find ptes"
msgstr "Impossibile trovare i PTE"
