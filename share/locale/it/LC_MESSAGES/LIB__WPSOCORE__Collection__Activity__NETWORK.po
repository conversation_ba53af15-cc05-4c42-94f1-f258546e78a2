# Language file for package WPSOCORE::Collection::Activity::NETWORK
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::NETWORK\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NETWORK.pm\n"

#: NETWORK.pm:29
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: NETWORK.pm:30
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: NETWORK.pm:132
#, perl-brace-format
msgid "Unable to find work order {workOrderId} for customer {customerId}"
msgstr ""
"Impossibile recuperare il work order {workOrderId} per il cliente "
"{customerId}"

#: NETWORK.pm:139
msgid "Unable to create network: work order not ready"
msgstr "Impossibile creare la network: work order non pronto"

#: NETWORK.pm:177
#, perl-brace-format
msgid "customerWorkingArea not found for {param} {value}"
msgstr "Area di lavoro cliente non trovata per {param} {value}"

#: NETWORK.pm:240
#, perl-brace-format
msgid "workingGroup not found for {param} {value} and {param1} {value1}"
msgstr "codice lavoro non trovato per {param} {value} e {param1} {value1}"

#~ msgid "Missing mandatory param {param}"
#~ msgstr "Parametro obbligatorio {param} mancante"

#~ msgid ""
#~ "Unable to find contract_info for for centralId {centralId} and "
#~ "workOrderId {workOrderId}"
#~ msgstr ""
#~ "Impossibile trovare le info del contratto per IdCentrale {centralId} and "
#~ "odaId {workOrderId}"

#~ msgid ""
#~ "workingGroup for centralId {centralId} and workOrderId {workOrderId} not "
#~ "found"
#~ msgstr ""
#~ "Centro lavoro per centralId {centralId} e odaId {workOrderId} non trovato"
