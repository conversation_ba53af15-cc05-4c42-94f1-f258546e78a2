# Language file for package WPSOAP::MQ::Consumer::PERMESSO_LAVORI::UpdateStatoAreaPermessi
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOAP::MQ::Consumer::PERMESSO_LAVORI::UpdateStatoAreaPermessi\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, r.be<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer/PERMESSO_LAVORI\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: UpdateStatoAreaPermessi.pm\n"

#: UpdateStatoAreaPermessi.pm:72
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: UpdateStatoAreaPermessi.pm:73
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: UpdateStatoAreaPermessi.pm:78
#, perl-brace-format
msgid "Unable to retrieve activity: {error}"
msgstr "Impossibile recuperare l'attività: {error}"

#: UpdateStatoAreaPermessi.pm:84
#, perl-brace-format
msgid "Mismatch bewteen permit {permitsId} and permitsAreaIdy {permitsAreaId}"
msgstr "Inconsistenza tra permesso {permitsId} e area permessi {permitsAreaId}"

#: UpdateStatoAreaPermessi.pm:99
#, perl-brace-format
msgid "Error calling WS Sinfo: {error}"
msgstr "Errore contattando il WS Sinfo: {error}"

#: UpdateStatoAreaPermessi.pm:104
#, perl-brace-format
msgid "Update permits area {permitsAreaId} for permit {permitId} done!"
msgstr ""
"Aggiornamento area permessi {permitsAreaId} per il permesso {permitId} "
"effettuata!"
