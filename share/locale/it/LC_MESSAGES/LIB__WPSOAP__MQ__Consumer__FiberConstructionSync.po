# Language file for package WPSOAP::MQ::Consumer::FiberConstructionSync
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::MQ::Consumer::FiberConstructionSync\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: FiberConstructionSync.pm\n"

#: FiberConstructionSync.pm:74
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: FiberConstructionSync.pm:75
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: FiberConstructionSync.pm:93
#, perl-brace-format
msgid "Unknown param {param} {type}"
msgstr "Parametro sconosciuto {param} {type}"

#: FiberConstructionSync.pm:107
#, perl-brace-format
msgid "Unable to search system of type {type}: {error}"
msgstr "Impossibile cercare un sistema di tipo {type}: {error}"

#: FiberConstructionSync.pm:114
#, perl-brace-format
msgid "Found more than one system of type {type} for {key} {value}"
msgstr "Trovato più di un sistema di tipo {type} per {key} {value}"

#: FiberConstructionSync.pm:123 FiberConstructionSync.pm:140
#: FiberConstructionSync.pm:155
#, perl-brace-format
msgid "Unable to update system of type {type} with id {id}: {error}"
msgstr "Impossibile aggiornare il sistema di tipo {type} con id {id}: {error}"

#: FiberConstructionSync.pm:160
#, perl-brace-format
msgid "System of type {type} with id {id} updated"
msgstr "Sistema di tipo {type} con id {id} aggiornato"

#: FiberConstructionSync.pm:172
#, perl-brace-format
msgid "Unable to create system of type {type}: {error}"
msgstr "Impossibile creare un sistema di tipo {type}: {error}"

#: FiberConstructionSync.pm:177
#, perl-brace-format
msgid "System of type {type} created with id {id}"
msgstr "Creato sistema di tipo {type} con id {id}"

#: FiberConstructionSync.pm:184
msgid "Missing required data 'groups'"
msgstr "Paramentro obbligatorio 'groups'"

#: FiberConstructionSync.pm:191
msgid "Bad data 'groups': "
msgstr "Parametro 'groups' errato: "

#: FiberConstructionSync.pm:197
msgid "Bad data 'groups': must be an ARRAY"
msgstr "Parametro 'groups' errato: deve essere un ARRAY"

#: FiberConstructionSync.pm:206
#, perl-brace-format
msgid "Missing info group: {info}"
msgstr "Info del gruppo mancanti: {info}"

#: FiberConstructionSync.pm:227
#, perl-brace-format
msgid "Creation of group {groupName} deferred to Service Management"
msgstr "Creazione del gruppo {groupName} delegata al Service Management"

#, perl-brace-format
#~ msgid "Unable to create group {groupName}: {error}"
#~ msgstr "Impossibile creare il gruppo {groupName}: {error}"

#, perl-brace-format
#~ msgid "Group {groupName} created with id {id}"
#~ msgstr "Gruppo {groupName} creato con id {id}"
