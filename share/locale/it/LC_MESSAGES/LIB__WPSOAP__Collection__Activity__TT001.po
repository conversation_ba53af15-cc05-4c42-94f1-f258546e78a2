# Language file for package WPSOAP::Collection::Activity::TT001
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON>@sirti.it>, <PERSON> <e.spazia<PERSON>@sirti.it>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::Collection::Activity::TT001\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:21+0200\n"
"PO-Revision-Date: 2025-07-17 11:24+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: TT001.pm\n"

#: TT001.pm:27
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio mancante {paramname}"

#: TT001.pm:28
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: TT001.pm:143 TT001.pm:157
#, perl-brace-format
msgid "networkid {networkId} not found"
msgstr "networkId {networkId} non trovata"

#: TT001.pm:146 TT001.pm:160
#, perl-brace-format
msgid ""
"Sorry, an error response code {response_code} occurred. Please contact the "
"system administrator"
msgstr ""
"Attenzione, avvenuto codice errore {response_code}. Per favore contattare "
"l'amministratore di sistema"

#: TT001.pm:151 TT001.pm:165
msgid ""
"Sorry, an error occurred. Please try again in a minute. If the issue "
"persists, please contact the system administrator"
msgstr ""
"Attenzione, avvenuto errore. Per favore riprovare fra un minuto. Se il "
"problema persiste, contattare gentilmente l'amministratore di sistema"

#: TT001.pm:182
msgid "You are not authorized to create a Ticket for this network"
msgstr "Non sei autorizzato a creare un ticket per questa network"

#: TT001.pm:187 TT001.pm:192
msgid "workingGroupCode not defined on the network"
msgstr "Centro lavoro non definito sulla network"

#: TT001.pm:197
#, perl-brace-format
msgid "Network {networkId} is status {status}"
msgstr "La network {networkId} è nello stato {status}"

#: TT001.pm:208
#, perl-brace-format
msgid "Field '{field}' is mandatory for multiCabinet network and scope {scope}"
msgstr ""
"Il campo '{field}' è obbligatorio per la network multiCabinet e lo scopo "
"{scope}"

#: TT001.pm:228
#, perl-brace-format
msgid ""
"Ticket already present for the network {networkId} and field '{field}' "
"{tt001TargetAsset}: found activity with id {id}"
msgstr ""
"Ticket già presente per la network {networkId} e il campo '{field}' "
"{tt001TargetAsset}: trovata attività con id {id}"

#: TT001.pm:230
#, perl-brace-format
msgid ""
"Ticket already present for the network {networkId}: found activity with id "
"{id}"
msgstr ""
"Ticket già presente per la network {networkId}: trovata attività con id {id}"

#: TT001.pm:263
#, perl-brace-format
msgid "Failed to get SECTOR,AOR,RO for centralId {centralId}"
msgstr "Impossibile recuperare SECTOR,AOR,RO per la centrale {centralId}"

#: TT001.pm:348
#, perl-brace-format
msgid "Activity of type {type} created with id {id}"
msgstr "Creata attività di tipo {type} con id {id}"
