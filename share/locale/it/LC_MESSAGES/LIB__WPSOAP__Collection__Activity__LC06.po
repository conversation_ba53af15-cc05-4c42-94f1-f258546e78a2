# Language file for package WPSOAP::Collection::Activity::LC06
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::Collection::Activity::LC06\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:10+0200\n"
"PO-Revision-Date: 2025-07-17 11:10+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC06.pm\n"

#: LC06.pm:27
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio mancante {paramname}"

#: LC06.pm:28
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LC06.pm:87
#, perl-brace-format
msgid "Param {param} must be defined"
msgstr "Il parametro {param} deve essere definito"

#: LC06.pm:107
#, perl-brace-format
msgid "Site already present: found activity with id {id}"
msgstr "Sito già presente: trovata attività con id {id}"

#: LC06.pm:254
#, perl-brace-format
msgid "Activity of type {type} created with id {id}"
msgstr "Attività di tipo {type} creata con id {id}"
