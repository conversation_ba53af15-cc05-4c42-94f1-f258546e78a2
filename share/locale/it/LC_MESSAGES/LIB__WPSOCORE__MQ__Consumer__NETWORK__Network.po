# Language file for package WPSOCORE::MQ::Consumer::NETWORK::Network
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::MQ::Consumer::NETWORK::Network\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.Ben<PERSON><EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/NETWORK\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: Network.pm\n"

#: Network.pm:55
#, perl-brace-format
msgid "Find more than one customer for {customerId}"
msgstr "Trovato più di un cliente per {customerId}"

#: Network.pm:58
#, perl-brace-format
msgid "Unable to find customer for {customerId}"
msgstr "Impossibile trovare il cliente per {customerId}"

#: Network.pm:110
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: Network.pm:111
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: Network.pm:116
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: Network.pm:125 Network.pm:131
#, perl-brace-format
msgid "Invalid xml: {error}"
msgstr "Xml invalido: {error}"

#: Network.pm:131
msgid "Missing tag heading"
msgstr "Mancante tag heading"

#: Network.pm:146
#, perl-brace-format
msgid "Invalid date {date} for param {param}: {error}"
msgstr "Data invalida {date} per il parametro {param}: {error}"

#: Network.pm:306
#, perl-brace-format
msgid "Found more than one workOrder with id {workOrderId}"
msgstr "Trovato più di un'ODA con id {workOrderId}"

#: Network.pm:556
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: Network.pm:622
#, perl-brace-format
msgid "WorkOrder {workOrderId} ended"
msgstr "ODA {workOrderId} terminata"

#: Network.pm:631
#, perl-brace-format
msgid "externalWorkTypeId {externalWorkTypeId} not available"
msgstr "{externalWorkTypeId} externalWorkTypeId non disponibile"

#: Network.pm:666
#, perl-brace-format
msgid "Skip creation network {networkId}: work order {workOrderId} not ready"
msgstr ""
"Ignoro la creazione della network {networkId}: ODA {workOrderId} non pronta"

#: Network.pm:670
msgid "Network already managed"
msgstr "Network già gestita"

#: Network.pm:687
#, perl-brace-format
msgid "Property {property} updated for work order {workOrderId}"
msgstr "Proprietà {property} aggiornata per ODA {workOrderId}"

#: Network.pm:697
#, perl-brace-format
msgid "Unable to find open network {networkId}: unable to cancel"
msgstr "Impossibile trovare network {networkId} aperta: impossibile annullare"

#: Network.pm:731
#, perl-brace-format
msgid "Param {param} not found for network {networkId}"
msgstr "Parametro {param} non trovato per la network {networkId}"

#: Network.pm:779
#, perl-brace-format
msgid "Activity created with id {id} ({networkId})"
msgstr "Creata attività con id {id} ({networkId})"

#, perl-brace-format
#~ msgid "Unable to find the list of workOrder {workOrderId}: {error}"
#~ msgstr "Impossibile trovare la lista dell'ODA {workOrderId}: {error}"

#, perl-brace-format
#~ msgid "WorkOrder {workOrderId} created with id {id}"
#~ msgstr "ODA {workOrderId} creata con id {id}"

#, perl-brace-format
#~ msgid "WorkOrder {workOrderId} updated"
#~ msgstr "ODA {workOrderId} aggiornata"

#, perl-brace-format
#~ msgid "Error in updating workOrder {workOrderId}: {error}"
#~ msgstr "Errore in fase di aggiornamento dell'ODA {workOrderId}: {error}"

#, perl-brace-format
#~ msgid "Unable to find contract with supplier = {supplier}"
#~ msgstr "Impossibile trovare il contratto per impresa = {supplier}"
