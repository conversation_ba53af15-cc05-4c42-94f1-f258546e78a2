# Language file for package WPSOCORE::MQ::Consumer::FieldService
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::MQ::Consumer::FieldService\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: FieldService.pm\n"

#: FieldService.pm:49 FieldService.pm:169
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: FieldService.pm:50 FieldService.pm:170
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: FieldService.pm:77
msgid "Skipping because externalId is not yet defined"
msgstr "Ignoro in quanto l'externalId non è ancora definito"

#: FieldService.pm:103
msgid "Skipping because activity is waiting for a retry"
msgstr "Ignoro in quanto l'attività è in attesa di un nuovo tentativo"

#: FieldService.pm:109
msgid "Skipping because activity is in status ASSURANCE"
msgstr "Ignoro in quanto l'attività è nello stato ASSURANCE"

#: FieldService.pm:113
msgid "KO because activity is in status ERROR"
msgstr "KO in quanto l'attività è nello stato ERROR"

#: FieldService.pm:119
#, perl-brace-format
msgid "Activity {id} of type {type} retrieved in state {state}"
msgstr "L'attività {id} di tipo {type} è stata recuperata nello stato {state}"

#: FieldService.pm:143
#, perl-brace-format
msgid "Activity {id} of type {type} created in state {state}"
msgstr "L'attività {id} di tipo {type} è stata creata nello stato {state}"

#: FieldService.pm:156
#, perl-brace-format
msgid "Step {action} on {type} activity {id} successfully executed"
msgstr ""
"Eseguita con successo l'azione {action} sull'attività {id} di tipo {type}"

#: FieldService.pm:183
msgid "Child "
msgstr "Figlio"

#: FieldService.pm:194
#, perl-brace-format
msgid ""
"Cannot close activity {id1} with EXTERNAL_SYNC_FS child activity {id2} still "
"active"
msgstr ""
"Impossibile chiudere l'attività {id1} mentre il figlio EXTERNAL_SYNC_FS "
"{id2} è ancora attivo"

#: FieldService.pm:202
msgid "All EXTERNAL_SYNC_FS children have been succesfully closed"
msgstr ""
"Tutte le attività figle EXTERNAL_SYNC_FS sono state chiuse correttamente"
