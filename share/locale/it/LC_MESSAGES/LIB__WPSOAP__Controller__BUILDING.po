# Language file for package WPSOAP::Controller::BUILDING
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::Controller::BUILDING\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/Controller\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: BUILDING.pm\n"

#: BUILDING.pm:57
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: BUILDING.pm:58
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: BUILDING.pm:92
#, perl-brace-format
msgid "Getting buildings from SiNFO by WS for project {project}"
msgstr "Recupero building da SiNFO attraverso il WS per il progetto {project}"

#: BUILDING.pm:99
#, perl-brace-format
msgid "Found {number} buildings"
msgstr "Trovati {number} buildings"

#: BUILDING.pm:131
#, perl-brace-format
msgid "Unable to init WPSOAP::Collection::System::PROJECT: {error}"
msgstr "Impossibile inizializzare WPSOAP::Collection::System::PROJECT: {error}"

#: BUILDING.pm:135
msgid "Getting local projects"
msgstr "Recupero progetti locali"

#: BUILDING.pm:143
#, perl-brace-format
msgid ""
"Found {number} local projects for customerId {id_cliente}, contractId "
"{id_contratto}"
msgstr ""
"Trovati {number} progetti locali per id_cliente {id_cliente}, id_contratto "
"{id_contratto}"

#: BUILDING.pm:145
#, perl-brace-format
msgid "Working on project {project}"
msgstr "Lavoro sul progetto {project}"

#: BUILDING.pm:151
#, perl-brace-format
msgid "Unable to get buildings from SiNFO from project {project}: "
msgstr "Impossibile recuperare i building da SiNFO dal progetto {project}"

#: BUILDING.pm:164
#, perl-brace-format
msgid ""
"Found more than one system for buildingSinfoId {id_sinfo}, customerId "
"{id_cliente}, contractId {id_contratto}, projectId {projectId}"
msgstr ""
"Trovato più di un sistema per buildingSinfoId {id_sinfo}, customerId "
"{id_cliente}, contractId {id_contratto}, projectId {projectId}"

#: BUILDING.pm:185
#, perl-brace-format
msgid ""
"Found {number} activities for buildingSinfoId {id_sinfo}, customerId "
"{id_cliente}, contractId {id_contratto}, projectId {projectId}"
msgstr ""
"Trovate {number} attività per buildingSinfoId {id_sinfo}, id_cliente "
"{id_cliente}, id_contratto {id_contratto}, id_progetto {projectId}"

#: BUILDING.pm:189
#, perl-brace-format
msgid ""
"Found a building with buildingSinfoId {idsinfo} already present ({n}/{m})"
msgstr ""
"Trovato un building con buildingSinfoId {idsinfo} già presente ({n}/{m})"

#: BUILDING.pm:210
#, perl-brace-format
msgid "Property {property} to add or modify"
msgstr "Property {property} da aggiungere o modificare"

#: BUILDING.pm:214
#, perl-brace-format
msgid "Property {property} to delete"
msgstr "Property {property} da rimuovere"

#: BUILDING.pm:223
#, perl-brace-format
msgid ""
"Activity suspended for buildingSinfoId {idsinfo}, found differences: proceed "
"to modify and unsuspend"
msgstr ""
"Attività sospesa per buildingSinfoId {idsinfo}, trovate differenze: procedo "
"a modificare e desospendere"

#: BUILDING.pm:226
#, perl-brace-format
msgid "Found differences for buildingSinfoId {idsinfo}: proceed to modify"
msgstr "Trovate differenze per buildingSinfoId {idsinfo}, procedo a modificare"

#: BUILDING.pm:231
#, perl-brace-format
msgid "Activity suspended for buildingSinfoId {idsinfo}: proceed to unsuspend"
msgstr "Attività sospesa per buildingSinfoId {idsinfo}: procedo a desospendere"

#: BUILDING.pm:234
msgid "Nothing to do"
msgstr "Nulla da fare"

#: BUILDING.pm:243
#, perl-brace-format
msgid "Found a new building with buildingSinfoId {idsinfo} ({n}/{m})"
msgstr "Trovato un nuovo building con buildingSinfoId {idsinfo} ({n}/{m})"

#: BUILDING.pm:262
#, perl-brace-format
msgid "Building with buildingSinfoId {idsinfo} created"
msgstr "Creato building con buildingSinfoId {idsinfo}"

#: BUILDING.pm:266 BUILDING.pm:289
msgid "Do commit"
msgstr "Effettua il commit"

#: BUILDING.pm:279
#, perl-brace-format
msgid "Found building with buildingSinfoId {idsinfo} in ART but not in SINFO"
msgstr "Trovato building con buildingSinfoId {idsinfo} in ART ma non in SINFO"

#: BUILDING.pm:281
#, perl-brace-format
msgid "Building is suspended for buildingSinfoId {idsinfo}, nothing to do"
msgstr "Il building è sospeso per buildingSinfoId {idsinfo}, niente da fare"

#: BUILDING.pm:283
#, perl-brace-format
msgid "Suspending the building for buildingSinfoId {idsinfo}"
msgstr "Sto sospendendo il building per buildingSinfoId {idsinfo}"

#~ msgid ""
#~ "Found {number} systems for buildingSinfoId {id_sinfo}, customerId "
#~ "{id_cliente}, contractId {id_contratto}, projectId {projectId}"
#~ msgstr ""
#~ "Trovati {number} sistemi per buildingSinfoId {id_sinfo}, id_cliente "
#~ "{id_cliente}, id_contratto {id_contratto}, id_progetto {projectId}"

#~ msgid ""
#~ "Activity suspended, found differences: proceed to modify and unsuspend"
#~ msgstr ""
#~ "Attività sospesa, trovate differenze: procedo a modificare e desospendere"

#~ msgid "Found differences: proceed to modify"
#~ msgstr "Trovate differenze, procedo a modificare"

#~ msgid "Activity suspended: proceed to unsuspend"
#~ msgstr "Attività sospesa: procedo a desospendere"

#~ msgid "Building is suspended, nothing to do"
#~ msgstr "Il building è sospeso, niente da fare"

#~ msgid "Suspending the building"
#~ msgstr "Sto sospendendo il building"

#~ msgid "Found a building with buildingSinfoId {idsinfo} already present"
#~ msgstr "Trovato un building con buildingSinfoId {idsinfo} già presente"

#~ msgid "Found a new building with buildingSinfoId {idsinfo}"
#~ msgstr "Trovato un nuovo building con buildingSinfoId {idsinfo}"

#~ msgid "Found differences, proceed to modify"
#~ msgstr "Trovate differenze, procedo alla modifica"

#~ msgid "Unable to get buildings from SiNFO form project {project}: "
#~ msgstr ""
#~ "Impossibile recuperare i building da SiNFO attraverso il WS per il "
#~ "progetto {project}"
