# Language file for package WPSOCORE::Collection::Activity::MANUTENZIONE_PREVENTIVA_01
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::Collection::Activity::MANUTENZIONE_PREVENTIVA_01\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: MANUTENZIONE_PREVENTIVA_01.pm\n"

#: MANUTENZIONE_PREVENTIVA_01.pm:26
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio mancante {paramname}"

#: MANUTENZIONE_PREVENTIVA_01.pm:27
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: MANUTENZIONE_PREVENTIVA_01.pm:76
#, perl-brace-format
msgid "Only one element must be present in param {param}"
msgstr "Solamente un elemento deve essere presente nel parametro {param}"

#: MANUTENZIONE_PREVENTIVA_01.pm:135
#, perl-brace-format
msgid "Unable to find {type} with id {id}"
msgstr "Impossibile trovare {type} con id {id}"

#: MANUTENZIONE_PREVENTIVA_01.pm:135
msgid "route"
msgstr "tratta"
