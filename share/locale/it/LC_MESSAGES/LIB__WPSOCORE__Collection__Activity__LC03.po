# Language file for package WPSOCORE::Collection::Activity::LC03
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::LC03\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC03.pm\n"

#: LC03.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: LC03.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LC03.pm:120
#, perl-brace-format
msgid "Find more than one customer for {customerId}"
msgstr "Trovato più di un cliente per {customerId}"

#: LC03.pm:124
#, perl-brace-format
msgid "Unable to find customer for {customerId}"
msgstr "Impossibile trovare il cliente per {customerId}"

#: LC03.pm:149
#, perl-brace-format
msgid "Find more than one contract for {contractId}"
msgstr "Trovato più di un contratto per {contractId}"

#: LC03.pm:153
#, perl-brace-format
msgid "Unable to find contract for {contractId}"
msgstr "Impossibile trovare il contratto per {contractId}"

#: LC03.pm:186
msgid "Please check the Pfp data for Region, Province and City"
msgstr "Controllare i dati Pfp per Regione, Provincia e Città"

#: LC03.pm:199
#, perl-brace-format
msgid "Unable to normalize mandatory field {field}"
msgstr "Impossibile normalizzare il campo obbligatorio {field}"

#: LC03.pm:208
#, perl-brace-format
msgid "Unable to normalize address: {error}"
msgstr "Impossibile normalizzare l'indirizzo: {error}"

#: LC03.pm:266
#, perl-brace-format
msgid ""
"already exsist a LC03 activity ({id}) for params {param1} {value1}, {param2} "
"{value2} and {param3} {value3}"
msgstr ""
"esito già l'attività LC03 ({id}) per i parametri {param1} {value1}, {param2} "
"{value2} e {param3} {value3}"
