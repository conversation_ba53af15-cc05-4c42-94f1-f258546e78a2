# Language file for package API::ART::APP::Activity::TT::Binding::Status::APERTA.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: API::ART::APP::Activity::TT::Binding::Status::APERTA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:43+0200\n"
"PO-Revision-Date: 2025-08-06 09:43+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity/TT/Binding/Status\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: APERTA.pm\n"

#: APERTA.pm:19
msgid "Property requestType can value \"Sopralluogo\", \"As-Built\""
msgstr "La proprietà requestType può valere \"Sopralluogo\", \"As-Built\""

#: APERTA.pm:23
msgid ""
"Property requestContext is allowed only if requestType values \"As-Built\""
msgstr ""
"La proprietà requestContext è permessa solo se requestType vale \"As-Built\""

#: APERTA.pm:27
msgid ""
"Property requestContext is mandatory if requestSource values \"As-Built\""
msgstr ""
"La proprietà requestContext è obbligatoria se requestSource vale \"As-Built\""

#: APERTA.pm:31
msgid ""
"Property authority is allowed only if requestType values \"Sopralluogo\""
msgstr ""
"La proprietà authority è permessa solo se requestType vale \"Sopralluogo\""

#: APERTA.pm:35
msgid "Property authority is mandatory if requestSource values \"Sopralluogo\""
msgstr ""
"La proprietà authority è obbligatoria se requestSource vale \"Sopralluogo\""

#: APERTA.pm:39
msgid ""
"Property permitsAreaId is mandatory if requestSource values \"Sopralluogo\""
msgstr ""
"La proprietà permitsAreaId è obbligatoria se requestSource vale "
"\"Sopralluogo\""

#: APERTA.pm:43
msgid "Property asBuiltId is allowed only if requestor values \"works\""
msgstr "La proprietà asBuiltId è permessa solo se requestor vale \"works\""

#: APERTA.pm:47
msgid "Property asBuiltId is mandatory if requestor values \"works\""
msgstr "La proprietà asBuiltId è obbligatoria se requestor vale \"works\""
