# Language file for package WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectBOTSpeedarkResponse
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectBOTSpeedarkResponse\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: Rizzard<PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.l<PERSON><PERSON><PERSON>@sirti.it, <EMAIL>, "
"r.belot<PERSON>@ext.sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer/"
"LC_CUSTOMER_PROJECT\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LcCustomerProjectBOTSpeedarkResponse.pm\n"

#: LcCustomerProjectBOTSpeedarkResponse.pm:66
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "NOME_EVENTO: {event_name}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:67
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:73
#, perl-brace-format
msgid "Error retrieving shared_resources object: {error}"
msgstr "Errore nel recupero dell'oggetto shared_resources: {error}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:85
#, perl-brace-format
msgid "Error retrieving activity {id}: {error}"
msgstr "Errore nel recupero dell'attività {id}: {error}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:102
#, perl-brace-format
msgid "Error retrieving shared resource file {id}: {error}"
msgstr "Errore nel recupero del file shared resource {id}: {error}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:116
#, perl-brace-format
msgid "ACK OK for activity {id} error: invalid param TIPO_ALLEGATO"
msgstr "ACK OK per l'attività {id}: parametro TIPO_ALLEGATO invalido"

#: LcCustomerProjectBOTSpeedarkResponse.pm:122
#, perl-brace-format
msgid "Error opening tmpfile for writing file {file}: {error}"
msgstr "Errore apertura tmpfile per file in scrittura {file}: {error}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:135
#, perl-brace-format
msgid "Activity with id {id} already managed"
msgstr "Attività con id {id} già gestita"

#: LcCustomerProjectBOTSpeedarkResponse.pm:152
#, perl-brace-format
msgid "Error deleting shared resource file {id}: {error}"
msgstr "Errore nella cancellazione del file shared resource {id}: {error}"

#: LcCustomerProjectBOTSpeedarkResponse.pm:157
#, perl-brace-format
msgid "Activity {id} correctly managed"
msgstr "Attività {id} gestita correttamente"
