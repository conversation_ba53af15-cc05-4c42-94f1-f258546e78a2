# Language file for package WPSOCORE::Collection::Activity::LC04
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::LC04\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC04.pm\n"

#: LC04.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: LC04.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LC04.pm:113
#, perl-brace-format
msgid "At least one param between {param1} or {param2} must be defined"
msgstr "Almeno un parametro tra {param1} e {param2} deve essere definito"

#: LC04.pm:131
#, perl-brace-format
msgid "Find more than one customer for {customerId}"
msgstr "Trovato più di un cliente per {customerId}"

#: LC04.pm:135
#, perl-brace-format
msgid "Unable to find customer for {customerId}"
msgstr "Impossibile trovare il cliente per {customerId}"

#: LC04.pm:160
#, perl-brace-format
msgid "Find more than one contract for {contractId}"
msgstr "Trovato più di un contratto per {contractId}"

#: LC04.pm:164
#, perl-brace-format
msgid "Unable to find contract for {contractId}"
msgstr "Impossibile trovare il contratto per {contractId}"

#: LC04.pm:176
#, perl-brace-format
msgid "Unable to find loader with id {requestId}: {error}"
msgstr "Impossibile trovare loader con id {requestId}: {error}"

#: LC04.pm:185
#, perl-brace-format
msgid "Unable to find system with id {parentAssetId}: {error}"
msgstr "Impossibile trovare il sistema con id {parentAssetId}: {error}"

#: LC04.pm:202
#, perl-brace-format
msgid "No cabinet activity found for parentAssetId {parentAssetId}"
msgstr "Nessun armadio trovato per parentAssetId {parentAssetId}"

#: LC04.pm:206
#, perl-brace-format
msgid ""
"Found more than one cabinet activity for parentAssetId {parentAssetId}: "
"please contact system administrator"
msgstr ""
"Trovato più di un armadio per parentAssetId {parentAssetId}: contattare "
"gentilmente l'amministratore di sistema"

#: LC04.pm:222 LC04.pm:241
#, perl-brace-format
msgid "contractId or customerId {contractId} not equal "
msgstr "contractId o customerId {contractId} non uguali "

#: LC04.pm:236
msgid "Cabinet with more than one working group code: please open ServiceMe"
msgstr "Armadio con più di un centro di lavoro: per favore aprire un ServiceMe"

#: LC04.pm:245
#, perl-brace-format
msgid "{param} {value} not valid: expected {exp}"
msgstr "{param} {value} non valido: atteso {exp}"

#: LC04.pm:294
#, perl-brace-format
msgid ""
"already exsist a LC04 activity ({id}) for params {param1} {value1}, {param2} "
"{value2}, {param3} {value3} and {param4} {value4}"
msgstr ""
"esiste già l'attività LC04 ({id}) per i parametri {param1} {value1}, "
"{param2} {value2}, {param3} {value3} e {param4} {value4}"
