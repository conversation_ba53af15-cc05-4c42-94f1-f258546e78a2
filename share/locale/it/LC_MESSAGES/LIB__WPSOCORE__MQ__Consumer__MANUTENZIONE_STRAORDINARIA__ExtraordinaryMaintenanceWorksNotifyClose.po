# Language file for package WPSOCORE::MQ::Consumer::MANUTENZIONE_STRAORDINARIA::ExtraordinaryMaintenanceWorksNotifyClose.
# Copyright (C) 2021 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::MANUTENZIONE_STRAORDINARIA::ExtraordinaryMaintenanceWorksNotifyClose\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.l<PERSON>@sirti.it, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/"
"MANUTENZIONE_STRAORDINARIA\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ExtraordinaryMaintenanceWorksNotifyClose.pm\n"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:103
#, perl-brace-format
msgid "Missing opening event for work activity id {workActivityId}"
msgstr ""
"Evento di apertura mancante per l'id attività lavorativa {workActivityId}"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:112
#, perl-brace-format
msgid "Activity {id} is locked: wait {x} seconds"
msgstr "L'attività {id} è bloccata: attesa di {x} secondi"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:116
#, perl-brace-format
msgid "Activity {id} is still locked after {x} seconds"
msgstr "L'attività {id} è ancora bloccata dopo {x} secondi"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:131
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: ExtraordinaryMaintenanceWorksNotifyClose.pm:136
#, perl-brace-format
msgid "Unable to find ongoing activity {id}"
msgstr "Impossibile trovare l'attività in corso {id}"
