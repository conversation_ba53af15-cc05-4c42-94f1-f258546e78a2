# Language file for package WPSOCORE::CS01GW
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::CS01GW\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: CS01GW.pm\n"

#: CS01GW.pm:22
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: CS01GW.pm:23
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: CS01GW.pm:217
msgid "With NOTIFY param ID_CS01 cannot be null"
msgstr "Con NOTIFY il parametro ID_CS01 non può essere null"

#: CS01GW.pm:221
msgid "With REQUEST param ID_CS01 must be null"
msgstr "Con REQUEST il parametro ID_CS01 deve essere null"

#: CS01GW.pm:238
#, perl-brace-format
msgid "Unable to insert tbl_art_enqueue_h: {message}"
msgstr "Impossibile inserire in tbl_art_enqueue_h: {message}"

#: CS01GW.pm:249
#, perl-brace-format
msgid "Unable to insert tbl_art_enqueue_b: {message}"
msgstr "Impossibile inserire in tbl_art_enqueue_b: {message}"

#: CS01GW.pm:345
#, perl-brace-format
msgid ""
"Missing entries in table tbl_art_enqueue_h or table tbl_art_enqueue_b for "
"message id {id}"
msgstr ""
"Record mancanti nella tabella table tbl_art_enqueue_h o table "
"tbl_art_enqueue_b per il messaggio con id {id}"

#: CS01GW.pm:360
#, perl-brace-format
msgid "Unable to clone in tbl_art_enqueue_h: {message}"
msgstr "Impossibile clonare in tbl_art_enqueue_h: {message}"

#: CS01GW.pm:369
#, perl-brace-format
msgid "Unable to clone in tbl_art_enqueue_b: {message}"
msgstr "Impossibile clonare in tbl_art_enqueue_b: {message}"

#: CS01GW.pm:381
#, perl-brace-format
msgid "Unable to update single key in tbl_art_enqueue_b: {message}"
msgstr ""
"Impossibile aggiornare la singola chiave in tbl_art_enqueue_b: {message}"

#: CS01GW.pm:458 CS01GW.pm:474
#, perl-brace-format
msgid "Unable to find message id {id}"
msgstr "Impossibile trovare il messaggio con id {id}"
