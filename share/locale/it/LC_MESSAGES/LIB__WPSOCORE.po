# Language file for package WPSOCORE
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<PERSON><PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.liv<PERSON><EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCORE.pm\n"

#: WPSOCORE.pm:44
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: WPSOCORE.pm:45
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: WPSOCORE.pm:239
msgid "You cannot use BODY and RAW_BODY jointly"
msgstr "Impossibile usare BODY e RAW_BODY insieme"

#: WPSOCORE.pm:245
msgid "You cannot use BODY param with method GET, HEAD or DELETE"
msgstr "Non puoi usare il parametro BODY con il metodo GET, HEAD o DELETE"

#: WPSOCORE.pm:305
#, perl-brace-format
msgid ""
"Unable to parse server response (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"
msgstr ""
"Impossibile effettuare il parsing della risposta del server (HTTP_STATUS: "
"{response_code}, MESSAGE: {response_content})"

#: WPSOCORE.pm:313
#, perl-brace-format
msgid ""
"Client error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato client (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: WPSOCORE.pm:318
#, perl-brace-format
msgid ""
"Server error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato server (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: WPSOCORE.pm:552
#, perl-brace-format
msgid ""
"Found more than one central info for centralId {centralId}: please contact "
"system administrator"
msgstr ""
"Trovata più di una centrale per centralId {centralId}: per favore contattare "
"l'amministratore di sistema"

#: WPSOCORE.pm:623 WPSOCORE.pm:1849
#, perl-brace-format
msgid "At least one param between {param} and {param1} must be defined"
msgstr "Almeno un parametro tra {param} e {param1} deve essere definito"

#: WPSOCORE.pm:627 WPSOCORE.pm:1853
#, perl-brace-format
msgid "Only one param between {param} and {param1} must be defined"
msgstr "Solamente un parametro tra {param} e {param1} deve essere definito"

#: WPSOCORE.pm:835 WPSOCORE.pm:977 WPSOCORE.pm:1363
#, perl-brace-format
msgid "Work orders not enable for customer {customerId}"
msgstr "Ordini di lavoro non abilitati per il cliente {customerId}"

#: WPSOCORE.pm:940
#, perl-brace-format
msgid ""
"Configuration not found for WBE {wbe} activityId {activity} and "
"macroActivityId {macroActivity}"
msgstr ""
"Configurazione non trovata per WBE {wbe} id attività {activity} e id macro "
"attivita {macroActivity}"

#: WPSOCORE.pm:1176 WPSOCORE.pm:1273
#, perl-brace-format
msgid ""
"Working group code search for {param} not enable for customer {customerId}"
msgstr ""
"Ricerca del codice lavoro per {param} non disponibile per il cliente "
"{customerId}"

#: WPSOCORE.pm:1513
#, perl-brace-format
msgid "WBE info not enable for customer {customerId}"
msgstr "Informazioni sulla WBE non abilitate per il cliente {customerId}"

#: WPSOCORE.pm:1517
#, perl-brace-format
msgid "Param {param1} and param {param2} must be passed in pair"
msgstr "I parametri {param1} e {param2} devono essere passati in coppia"

#: WPSOCORE.pm:1670
#, perl-brace-format
msgid "Param {param} must contain at least one element"
msgstr "Il parametro {param} deve contenere almeno un elemento"

#: WPSOCORE.pm:1972
msgid "Working group code not unique: please contact system administrator"
msgstr ""
"Centro di lavoro non univocamente determinato, per favore contattare "
"l'amministratore di sistema"

#: WPSOCORE.pm:1975
msgid "Working group code not found: please contact system administrator"
msgstr ""
"Centro di lavoro non trovato, per favore contattare l'amministratore di "
"sistema"

#: WPSOCORE.pm:2057
msgid "Region not unique: please contact system administrator"
msgstr ""
"Regione non univocamente determinata, per favore contattare l'amministratore "
"di sistema"

#: WPSOCORE.pm:2060
msgid "Region not found: please contact system administrator"
msgstr "Regione non trovata, per favore contattare l'amministratore di sistema"

#: WPSOCORE.pm:2292 WPSOCORE.pm:2335
#, perl-brace-format
msgid "Unable to retrieve OdS for externalId {externalId}"
msgstr "Impossibile recuperare l'OdS per l'externalId {externalId}"

#: WPSOCORE.pm:2375 WPSOCORE.pm:3146 WPSOCORE.pm:3189
#, perl-brace-format
msgid "More than one info record for OdS {ODS}"
msgstr "Trovato più di un risultato per le informazioni dell'OdS {ODS}"

#: WPSOCORE.pm:2378
#, perl-brace-format
msgid "Unable to retrieve info for OdS {ODS}"
msgstr "Impossibile recuperare le informazione per l'OdS {ODS}"

#: WPSOCORE.pm:2487
msgid "Automatic at assignment configuration not found"
msgstr "Configurazione non trovata per l'assegnazione automatica all'AT"

#: WPSOCORE.pm:2561
msgid "Automatic works assignment configuration not found"
msgstr "Configurazione non trovata per l'assegnazione automatica dei lavori"

#: WPSOCORE.pm:2613
msgid "Automatic works timing configuration not found"
msgstr "Configurazione non trovata per timing lavori"

#: WPSOCORE.pm:2678
#, perl-brace-format
msgid "Missing env {env}"
msgstr "Mancante env {env}"

#: WPSOCORE.pm:2742 WPSOCORE.pm:2774
#, perl-brace-format
msgid "Unable to find {type} with id {id}"
msgstr "Impossibile recuperare {type} con id {id}"

#: WPSOCORE.pm:2742
msgid "section"
msgstr "sezione"

#: WPSOCORE.pm:2774
msgid "cable"
msgstr "cavo"

#: WPSOCORE.pm:2780
#, perl-brace-format
msgid ""
"Mismatch between workingGroupCode {wgc} and cable's workingGroupCode {wgcc}"
msgstr ""
"Mancata corrispondenza tra il workingGroupCode {wgc} e il centro di lavoro "
"del cavo {wgcc}"

#: WPSOCORE.pm:2797
#, perl-brace-format
msgid ""
"Unable to find cables for directive {directiveId}, section {sectionId} and "
"workingr group code {workingGroupCode}"
msgstr ""
"Impossibile trovare i cavi per la direttrice {directiveId}, sezione "
"{sectionId} e centro di lavoro {workingGroupCode}"

#: WPSOCORE.pm:2901
#, perl-brace-format
msgid "Unable to find subcontract with name {name}"
msgstr "Impossibile trovare il sottocontratto con nome {name}"

#: WPSOCORE.pm:2912
#, perl-brace-format
msgid "Error while generation template: {error}"
msgstr "Errore in fase di generazione del template: {error}"

#: WPSOCORE.pm:2951
#, perl-brace-format
msgid "Unable to find subcontract item with name {name}"
msgstr "Impossibile trovare il dettaglio del sottocontratto con nome {name}"

#~ msgid "Central info for centralId {centralId} not found"
#~ msgstr "Informazioni della centrale per centralId {centralId} non trovate"

#~ msgid "AOL for centralId {centralId} not found"
#~ msgstr "AOL per centralId {centralId} non trovato"

#~ msgid ""
#~ "Found more than one AOL for centralId {centralId}: please contact system "
#~ "administrator"
#~ msgstr ""
#~ "Trovato più di un AOL per centralId {centralId}: per favore contattare "
#~ "l'amministratore di sistema"

#~ msgid "RO for centralId {centralId} not found"
#~ msgstr "RO per centralId {centralId} non trovato"
