# Language file for package WPSOCORE::MQ::Consumer::NETWORK::NetworkWorksReport
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::MQ::Consumer::NETWORK::NetworkWorksReport\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:45+0200\n"
"PO-Revision-Date: 2025-08-06 09:45+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/NETWORK\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkWorksReport.pm\n"

#: NetworkWorksReport.pm:71
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: NetworkWorksReport.pm:72
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkWorksReport.pm:77 NetworkWorksReport.pm:127
#, perl-brace-format
msgid "Missing mandatory parameter {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: NetworkWorksReport.pm:86 NetworkWorksReport.pm:120
#, perl-brace-format
msgid "Invalid xml: {error}"
msgstr "Xml invalido: {error}"

#: NetworkWorksReport.pm:120
msgid "Missing tag ESITO"
msgstr "Tag ESITO mancante"

#: NetworkWorksReport.pm:163
#, perl-brace-format
msgid "No network found for networkId {networkId}"
msgstr "Nessuna network trovata per networkId {networkId}"

#: NetworkWorksReport.pm:172
#, perl-brace-format
msgid "NetworkId {networkId} with {param} {value}: worked ko"
msgstr "NetworkId {networkId} con {param} {value}: lavorata ko"

#: NetworkWorksReport.pm:229 NetworkWorksReport.pm:258
#: NetworkWorksReport.pm:265
#, perl-brace-format
msgid "Error working activity {id}: {error}"
msgstr "Impossibile lavorare l'attività {id}: {error}"

#: NetworkWorksReport.pm:229
#, perl-brace-format
msgid "Error creating activity of type {type} for activity {id}: {error}"
msgstr ""
"Errore creando l'attività di tipo {type} per l'id attività {id}: {error}"

#: NetworkWorksReport.pm:234
#, perl-brace-format
msgid "Successfully worked activity {id}: activity {type} {childId} created"
msgstr ""
"Attività con id {id} lavorata correttamente: attività {type} {childId} creata"

#: NetworkWorksReport.pm:243
#, perl-brace-format
msgid "Error retrieving children {type}: {error}"
msgstr "Errore nel recupero dei figli {type}: {error}"

#: NetworkWorksReport.pm:258
#, perl-brace-format
msgid "Unable to find on-going child of type {type}"
msgstr "Impossibile recuperare i figli in corso di tipo {type}"

#: NetworkWorksReport.pm:265
#, perl-brace-format
msgid "Child of type {type} already closed for activity {id}"
msgstr "Figlio di tipo {type} già chiuso per l'id attività {id}"

#: NetworkWorksReport.pm:288
#, perl-brace-format
msgid "Error working child {childId} of activity {id}: {error}"
msgstr "Errore lavorando il figlio {childId} dell'attività {id}: {error}"

#: NetworkWorksReport.pm:293
#, perl-brace-format
msgid "Successfully worked activity {id}: current status {status}"
msgstr "Attività con id {id} lavorata con successo: stato corrente {status}"
