# Language file for package WPSOCORE::MQ::Consumer::NETWORK::NetworkROENotifyClose
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::NETWORK::NetworkROENotifyClose\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:45+0200\n"
"PO-Revision-Date: 2025-08-06 09:45+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/NETWORK\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkROENotifyClose.pm\n"

#: NetworkROENotifyClose.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: NetworkROENotifyClose.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkROENotifyClose.pm:86
msgid "Found more than one Network"
msgstr "Trovata più di una Network"

#: NetworkROENotifyClose.pm:96
#, perl-brace-format
msgid "Activity {id} locked"
msgstr "Attività {id} lockata"

#: NetworkROENotifyClose.pm:105
#, perl-brace-format
msgid "Missing opening event for ROE activity id {ROEActivityId}"
msgstr "Evento di apertura mancante per attività ROE con id {ROEActivityId}"

#: NetworkROENotifyClose.pm:118
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: NetworkROENotifyClose.pm:123
#, perl-brace-format
msgid "Unable to find ongoing network {networkId}"
msgstr "Impossibile trovare network {networkId} in corso"
