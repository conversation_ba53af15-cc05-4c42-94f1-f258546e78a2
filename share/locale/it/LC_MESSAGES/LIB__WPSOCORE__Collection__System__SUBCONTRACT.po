# Language file for package WPSOCORE::Collection::System::SUBCONTRACT
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::System::SUBCONTRACT\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/System\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: SUBCONTRACT.pm\n"

#: SUBCONTRACT.pm:124
#, perl-brace-format
msgid "Unable to find contracts for customer {customerId}"
msgstr "Impossibile trovare i contratti per il cliente {customerId}"

#: SUBCONTRACT.pm:131
#, perl-brace-format
msgid "Unable to find contract {contractId} for customer {customerId}"
msgstr ""
"Impossibile troare il contratto {contractId} per il cliente {customerId}"

#: SUBCONTRACT.pm:143
#, perl-brace-format
msgid "SUBCONTRACT with name {name} already present!"
msgstr "Sottocontratto con nome {name} già presente!"
