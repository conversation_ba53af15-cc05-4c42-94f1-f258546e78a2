# Language file for package WPSOCORE::MQ::Consumer::NETWORK::NetworkPrivatePermitsNotifyClose
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::NETWORK::NetworkPrivatePermitsNotifyClose\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, r.belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/NETWORK\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkPrivatePermitsNotifyClose.pm\n"

#: NetworkPrivatePermitsNotifyClose.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: NetworkPrivatePermitsNotifyClose.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkPrivatePermitsNotifyClose.pm:103
#, perl-brace-format
msgid ""
"Missing opening event for private permits activity id "
"{privatePermitsActivityId}"
msgstr ""
"Evento di apertura mancante per l'attività di permessi privati con id "
"{privatePermitsActivityId}"

#: NetworkPrivatePermitsNotifyClose.pm:116
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: NetworkPrivatePermitsNotifyClose.pm:121
#, perl-brace-format
msgid "Unable to find ongoing network {networkId}"
msgstr "Impossibile trovare network {networkId} in corso"
