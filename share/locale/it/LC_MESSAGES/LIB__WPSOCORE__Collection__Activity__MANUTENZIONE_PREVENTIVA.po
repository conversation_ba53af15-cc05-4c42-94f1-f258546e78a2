# Language file for package WPSOCORE::Collection::Activity::MANUTENZIONE_PREVENTIVA
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::MANUTENZIONE_PREVENTIVA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: MANUTENZIONE_PREVENTIVA.pm\n"

#: MANUTENZIONE_PREVENTIVA.pm:30
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: MANUTENZIONE_PREVENTIVA.pm:31
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: MANUTENZIONE_PREVENTIVA.pm:102 MANUTENZIONE_PREVENTIVA.pm:129
#, perl-brace-format
msgid "Missing param {param}"
msgstr "Parametro {param} mancante"

#: MANUTENZIONE_PREVENTIVA.pm:109
#, perl-brace-format
msgid ""
"Parameter {label1} must not be valorized unless parameter {label2} equals "
"ALTRO"
msgstr ""
"Il parametro {label1} non deve essere valorizzato a meno che il parametro "
"{label2} sia uguale a ALTRO"

#: MANUTENZIONE_PREVENTIVA.pm:119
#, perl-brace-format
msgid ""
"Parameter {label1} must be valorized when parameter {label2} equals ALTRO"
msgstr ""
"Il parametro {label1} deve essere valorizzato quando il parametro {label2} è "
"uguale a ALTRO"

#: MANUTENZIONE_PREVENTIVA.pm:185 MANUTENZIONE_PREVENTIVA.pm:217
#: MANUTENZIONE_PREVENTIVA.pm:250
#, perl-brace-format
msgid "Unable to find {type} with id {id}"
msgstr "Impossibile trovare {type} con id {id}"

#: MANUTENZIONE_PREVENTIVA.pm:185
msgid "directive"
msgstr "direttrice"

#: MANUTENZIONE_PREVENTIVA.pm:217
msgid "section"
msgstr "sezione"

#: MANUTENZIONE_PREVENTIVA.pm:250
msgid "cable"
msgstr "cavo"

#: MANUTENZIONE_PREVENTIVA.pm:256
#, perl-brace-format
msgid ""
"Mismatch between workingGroupCode {wgc} and cable's workinGroupCode {wgcc}"
msgstr ""
"Incongruenza tra il codice lavoro {wgc} il codice lavoro dei cavi {wgcc}"

#: MANUTENZIONE_PREVENTIVA.pm:315
msgid "Unable to find AOA"
msgstr "Impossibile trovare AOA"
