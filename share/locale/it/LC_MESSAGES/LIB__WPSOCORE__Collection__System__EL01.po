# Language file for package WPSOCORE::Collection::System::EL01
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::System::EL01\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<PERSON><PERSON>@ext.sirti.it>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/System\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: EL01.pm\n"

#: EL01.pm:148
msgid ""
"Cabinet id must be a string of 3 alphanumeric characters optionally followed "
"by a '/' and the CNO id"
msgstr ""
"L'identificativo dell'armadio deve essere una stringa di 3 caratteri "
"alfanumerici, opzionalmente seguita da un '/' e dall'id del CNO"

#: EL01.pm:156
#, perl-brace-format
msgid "Bad value for parameter cabinetType '{cabinetType}'"
msgstr "Valore errato per il tipo armadio '{cabinetType}'"

#: EL01.pm:167
#, perl-brace-format
msgid "EL01 with id {id} already present!"
msgstr "Il sistema EL01 con id {id} è già presente!"

#: EL01.pm:193
#, perl-brace-format
msgid "Unsupported system properties for system type EL01 ignored: {params}"
msgstr ""
"Ignorate le proprietà di sistema sconosciute per il tipo sistema EL01: "
"{params}"

#~ msgid "Cabinet id must be a string of 3 digits"
#~ msgstr "L'identificativo dell'armadio deve essere una stringa di 3 numeri"
