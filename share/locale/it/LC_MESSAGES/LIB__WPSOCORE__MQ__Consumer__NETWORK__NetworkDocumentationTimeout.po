# Language file for package WPSOCORE::MQ::Consumer::NETWORK::NetworkDocumentationTimeout
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::NETWORK::NetworkDocumentationTimeout\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/"
"NETWORK\n"
"X-Poedit-KeywordsList: __;;"
"%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkDocumentationTimeout.pm\n"

#: NetworkDocumentationTimeout.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: NetworkDocumentationTimeout.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkDocumentationTimeout.pm:72
#, perl-brace-format
msgid "Error retrieving activity {id}: {error}"
msgstr "Impossibile recuperare l'attività {id}: {error}"

#: NetworkDocumentationTimeout.pm:132
#, perl-brace-format
msgid "Error working activity {id}: {error}"
msgstr "Impossibile elaborare l'attività {id}: {error}"

#: NetworkDocumentationTimeout.pm:153
#, perl-brace-format
msgid "Activity with id {id} successfully worked ({status})"
msgstr "Attività con id {id} lavorata correttamente ({status})"

#: NetworkDocumentationTimeout.pm:158
#, perl-brace-format
msgid "Activity with id {id} already managed"
msgstr "Attività con id {id} già gestita"
