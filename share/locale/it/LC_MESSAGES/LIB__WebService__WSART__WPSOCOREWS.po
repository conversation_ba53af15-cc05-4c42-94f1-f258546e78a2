# Language file for package WebService::WSART::WPSOCOREWS.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOCOREWS\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:45+0200\n"
"PO-Revision-Date: 2025-08-06 09:45+0200\n"
"Last-Translator: <PERSON> <<PERSON><PERSON>@ext.sirti.it>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOCOREWS.pm\n"

#: WPSOCOREWS.pm:283
msgid "Not existent attachment"
msgstr "Allegato non esistente"

#: WPSOCOREWS.pm:287
#, perl-brace-format
msgid "Unable to open index file {file}.idx: {error}"
msgstr "Impssibile aprire il file indice {file}.idx: {error}"

#: WPSOCOREWS.pm:296
#, perl-brace-format
msgid "Unable to parse JSON idxfile {file}.idx: {error}"
msgstr "Impossibile effettuare il parsing del file JSON {file}.idx: {error}"

#: WPSOCOREWS.pm:299
#, perl-brace-format
msgid "Unable to copy file from {file} to {file1}: {error}"
msgstr "Impossibile copiare il file {file} in {file1}: {error}"

#: WPSOCOREWS.pm:332
msgid "Customers not found!"
msgstr "Clienti non trovati!"

#: WPSOCOREWS.pm:356
msgid "Contracts not found!"
msgstr "Contratti non trovati!"

#: WPSOCOREWS.pm:374 WPSOCOREWS.pm:2093
msgid "City not found!"
msgstr "Città non trovata!"

#: WPSOCOREWS.pm:392
msgid "Project not found!"
msgstr "Progetto non trovato!"

#: WPSOCOREWS.pm:418
msgid "Project not found"
msgstr "Progetto non trovato"

#: WPSOCOREWS.pm:445 WPSOCOREWS.pm:472
msgid "Activity not found"
msgstr "Attività non trovata"

#: WPSOCOREWS.pm:511 WPSOCOREWS.pm:550 WPSOCOREWS.pm:588 WPSOCOREWS.pm:615
#: WPSOCOREWS.pm:653 WPSOCOREWS.pm:671 WPSOCOREWS.pm:703
msgid "System not found"
msgstr "Sistema non trovato"

#: WPSOCOREWS.pm:759
msgid "Workorder not found"
msgstr "ODA non trovata"

#: WPSOCOREWS.pm:922 WPSOCOREWS.pm:951
msgid "User not allowed to request the resource"
msgstr "L'utente non ha il permesso per richiedere la risorsa"

#: WPSOCOREWS.pm:955 WPSOCOREWS.pm:1280 WPSOCOREWS.pm:1749 WPSOCOREWS.pm:4238
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: WPSOCOREWS.pm:985
msgid "Only 'user admin' user can view at's list"
msgstr ""
"Solamente gli utenti 'user admin' possono visualizzare la lista degli AT"

#: WPSOCOREWS.pm:1082
msgid ""
"Only project manager user and num user can update groups for technical "
"assistant"
msgstr ""
"Solamente gli utenti project manager e num possono aggiornare i gruppi per "
"gli assistenti tecnici"

#: WPSOCOREWS.pm:1087 WPSOCOREWS.pm:1196 WPSOCOREWS.pm:1346 WPSOCOREWS.pm:1700
#: WPSOCOREWS.pm:3478 WPSOCOREWS.pm:3699
msgid "Invalid JSON"
msgstr "JSON non valido"

#: WPSOCOREWS.pm:1090 WPSOCOREWS.pm:1268 WPSOCOREWS.pm:1349 WPSOCOREWS.pm:1355
#: WPSOCOREWS.pm:1703
#, perl-brace-format
msgid "Missing {paramname} param"
msgstr "Parametro {paramname} mancante"

#: WPSOCOREWS.pm:1093 WPSOCOREWS.pm:1274 WPSOCOREWS.pm:1352 WPSOCOREWS.pm:1358
#: WPSOCOREWS.pm:1706
#, perl-brace-format
msgid "Bad value for {paramname} param"
msgstr "Valore errato per il parametro {paramname}"

#: WPSOCOREWS.pm:1099
msgid "Num user can not update himself"
msgstr "Gli utenti num non possono aggiornare se stessi"

#: WPSOCOREWS.pm:1103
#, perl-brace-format
msgid "Unknown user {user}"
msgstr "Utente {user} sconosciuto"

#: WPSOCOREWS.pm:1114
msgid "Num user can not update another num user"
msgstr "Gli utenti num non possono aggiornare un altro utente num"

#: WPSOCOREWS.pm:1124
#, perl-brace-format
msgid "User can not manage working group code {workingGroupCode}"
msgstr "L'utente non può gestire il centro di lavoro {workingGroupCode}"

#: WPSOCOREWS.pm:1226
msgid "Activity ID not present or it is not possible to add documentation"
msgstr "ID attività non presente o non è possibile aggiungere documentazione"

#: WPSOCOREWS.pm:1229
msgid "More than one record found, anomaly"
msgstr "Trovato più di un record, anomalia"

#: WPSOCOREWS.pm:1233
msgid "Inconsistency between activityId and sourceId"
msgstr "Inconsistenza tra activityId e sourceId"

#: WPSOCOREWS.pm:1271 WPSOCOREWS.pm:1283
#, perl-brace-format
msgid "Param {paramname} must be a SCALAR"
msgstr "Il parametro {paramname} deve essere uno SCALARE"

#: WPSOCOREWS.pm:1298
#, perl-brace-format
msgid "External activities not found for {param} {value} and {param1} {value1}"
msgstr "Attività esterne non trovato per {param} {value} e {param1} {value1}"

#: WPSOCOREWS.pm:1369
#, perl-brace-format
msgid "Work order {workOrder} not active"
msgstr "ODA {workOrder} non attiva"

#: WPSOCOREWS.pm:1422
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#. TRANSLATORS: es. Param values is a list of file extensions (eg. 'xlsx','json','csv')
#: WPSOCOREWS.pm:1426
#, perl-brace-format
msgid "Param {paramname} can be {values}"
msgstr "Il parametro {paramname} può valere {values}"

#: WPSOCOREWS.pm:1717
msgid "Work order not active"
msgstr "ODA non attiva"

#: WPSOCOREWS.pm:1819
#, perl-brace-format
msgid "Param {param} {value} not found for projects"
msgstr "Parametro {param} {value} non trovato per i progetti"

#: WPSOCOREWS.pm:1911
#, perl-brace-format
msgid "Network {value} not found"
msgstr "Network {value} non trovata"

#: WPSOCOREWS.pm:2002
#, perl-brace-format
msgid "Missing mandatory system property {prop}"
msgstr "Mancante proprietà del sistema obbligatoria {prop}"

#: WPSOCOREWS.pm:2090
msgid "Found more than a city!"
msgstr "Trovata più di una città!"

#: WPSOCOREWS.pm:2352
msgid "Missing mandatory param team"
msgstr "Parametro obbligatorio team mancante"

#: WPSOCOREWS.pm:2760 WPSOCOREWS.pm:2774
#, perl-brace-format
msgid "Param type can not value {value}"
msgstr "Il parametro type non può valere {value}"

#: WPSOCOREWS.pm:2809
#, perl-brace-format
msgid ""
"Unable to find activities for customer {customer} and contract {contract}"
msgstr ""
"Impossibile trovare attività per cliente {customer} e contratto {contract}"

#: WPSOCOREWS.pm:2846 WPSOCOREWS.pm:2887
#, perl-brace-format
msgid "Unknown BLOCK_ID {value}"
msgstr "BLOCK_ID {value} sconosciuto"

#: WPSOCOREWS.pm:2944 WPSOCOREWS.pm:3154
msgid "Missing mandatory param users"
msgstr "Parametro obbligatorio users mancante"

#: WPSOCOREWS.pm:3383
msgid "Param main-process can value only 1"
msgstr "Il parametro main-process può valere solamente 1"

#, perl-brace-format
#~ msgid "Missing mandatory param {param}"
#~ msgstr "Parametro obbligatorio {param} mancante"

#, perl-brace-format
#~ msgid "Param {param} must be an array"
#~ msgstr "Il parametro {param} deve essere un array"

#, perl-brace-format
#~ msgid "Param {param} can not be a zero-length array"
#~ msgstr "Il parametro {param} non può essere un array a lughezza zero"

#, perl-brace-format
#~ msgid "Max lenght exceeded for param {param}: max value {value}"
#~ msgstr ""
#~ "Lunghezza massima ecceduta per il parametro {param}: valore massimo "
#~ "{value}"

#, perl-brace-format
#~ msgid "Param {param} can contain only numeric values"
#~ msgstr "Il parametro {param} può contenere solo valori numerici"

#, perl-brace-format
#~ msgid "Duplicated value {value} in param {param}"
#~ msgstr "Valore duplicato {value} nel parametro {param}"

#, perl-brace-format
#~ msgid "Param {param} must be an hash"
#~ msgstr "Il parametro {param} deve essere un hash"

#, perl-brace-format
#~ msgid "Found {found} activities: expected {expected}"
#~ msgstr "Trovate {found} attività: attese {expected}"

#, perl-brace-format
#~ msgid "Transition not allowed for {tdt} \"{from}->{to}\""
#~ msgstr "Transizione non permessa per {tdt} \"{from}->{to}\""
