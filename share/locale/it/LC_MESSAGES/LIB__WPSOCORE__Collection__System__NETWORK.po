# Language file for package WPSOCORE::Collection::System::NETWORK
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::System::NETWORK\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/System\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NETWORK.pm\n"

#: NETWORK.pm:28
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: NETWORK.pm:29
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: NETWORK.pm:140
#, perl-brace-format
msgid "Network with networkdId {networkdId} already present!"
msgstr "Network con networkdId {networkdId} già presente!"

#: NETWORK.pm:151
msgid "workingGroupCodeList must be a non-zero length array"
msgstr "workingGroupCodeList deve essere un array di lunghezza non zero"

#: NETWORK.pm:163
#, perl-brace-format
msgid "Group not found for workingGroup {workingGroup}"
msgstr "Gruppo non trovato per codice lavoro {workingGroup}"

#~ msgid "Anomaly: unable to find workingGroups for central {centralId}"
#~ msgstr ""
#~ "Anomalia: impossibile trovare un gruppo lavoro per la centrale {centralId}"
