# Language file for package WPSOCORE::MQ::Consumer::LC02::PTEWorksNotifyOpen
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::LC02::PTEWorksNotifyOpen\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> "
"<F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/LC02\n"
"X-Poedit-KeywordsList: __;;"
"%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: PTEWorksNotifyOpen.pm\n"

#: PTEWorksNotifyOpen.pm:61
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: PTEWorksNotifyOpen.pm:62
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: PTEWorksNotifyOpen.pm:89
#, perl-brace-format
msgid "Found {n} lc02 activity: expected {m}"
msgstr "Trovate {n} attività LC02: attese {m}"

#: PTEWorksNotifyOpen.pm:98
#, perl-brace-format
msgid "Activity {id} is locked: wait {x} seconds"
msgstr "L'attività {id} è bloccata: attesa di {x} secondi"

#: PTEWorksNotifyOpen.pm:102
#, perl-brace-format
msgid "Activity {id} is still locked after {x} seconds"
msgstr "L'attività {id} è ancora bloccata dopo {x} secondi"

#: PTEWorksNotifyOpen.pm:119
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: PTEWorksNotifyOpen.pm:125
msgid "Unable to find ptes"
msgstr "Impossibile trovare i PTE"
