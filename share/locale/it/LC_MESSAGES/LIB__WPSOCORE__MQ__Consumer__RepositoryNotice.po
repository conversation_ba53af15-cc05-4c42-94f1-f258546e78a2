# Language file for package WPSOCORE::MQ::Consumer::RepositoryNotice.
# Copyright (C) 2021 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::MQ::Consumer::RepositoryNotice\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:45+0200\n"
"PO-Revision-Date: 2025-08-06 09:45+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: RepositoryNotice.pm\n"

#: RepositoryNotice.pm:182
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: RepositoryNotice.pm:183
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: RepositoryNotice.pm:190
#, perl-brace-format
msgid "Unsupported event {event}"
msgstr "Evento non supportato {event}"

#: RepositoryNotice.pm:220
#, perl-brace-format
msgid "Nothing to do: no {objects} defined"
msgstr "Nessuna azione necessaria: nessun {objects} è definito"

#: RepositoryNotice.pm:226
#, perl-brace-format
msgid "handling cabinet id {cabinets} and group id {groups}"
msgstr "Gestione di id cabinet {cabinets} e id gruppo {groups}"

#: RepositoryNotice.pm:249
msgid "Event correctly managed"
msgstr "Evento correttamente gestito"
