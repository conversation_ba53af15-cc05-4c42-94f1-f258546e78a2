# Language file for package API::ART::APP::Activity::ROE::Binding::Action::PRESA_IN_CARICO.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"API::ART::APP::Activity::ROE::Binding::Action::PRESA_IN_CARICO\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:43+0200\n"
"PO-Revision-Date: 2025-08-06 09:43+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <PERSON><PERSON>be<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity/ROE/Binding/Action\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: PRESA_IN_CARICO.pm\n"

#: PRESA_IN_CARICO.pm:35
#, perl-brace-format
msgid "Unable to find {param} for {param1} {value1}"
msgstr "Impossibile trovare {param} per {param1} {value1}"

#: PRESA_IN_CARICO.pm:39
msgid ""
"Anomaly: no workingGroupCode found for user: please contact system "
"administrator"
msgstr ""
"Anomalia: nessun centro lavoro trovato per l'utente: per favore contattare "
"l'amministratore di sistema"

#: PRESA_IN_CARICO.pm:52
#, perl-brace-format
msgid "User can not select workingGroupCode {workingGroupCode}"
msgstr "L'utente non può selezione il centro lavoro {workingGroupCode}"

#: PRESA_IN_CARICO.pm:69
#, perl-brace-format
msgid "ROE {id}: please select a workingGroupCode from {workingGroupCodeList}"
msgstr ""
"ROE {id}: scegliere gentilmente un centro lavoro tra {workingGroupCodeList}"

#: PRESA_IN_CARICO.pm:77
#, perl-brace-format
msgid ""
"ROE's workingGroupCode must be equal to network's workingGroupCode: expected "
"{exp}, found => {found}"
msgstr ""
"Il centro lavoro dei ROE deve essere uguale a quello della network: atteso "
"{exp}, trovato => {found}"

#: PRESA_IN_CARICO.pm:98 PRESA_IN_CARICO.pm:106
#, perl-brace-format
msgid "Unable to update system groups for system id {id}: {error}"
msgstr ""
"Impossibile aggiornare i gruppi del sistema per l'id sistema {id}: {error}"

#: PRESA_IN_CARICO.pm:111
#, perl-brace-format
msgid "Unable to update system properties for system id {id}: {error}"
msgstr ""
"Impossibile aggiornare le proprietà del sistema per l'id sistema {id}: "
"{error}"

#: PRESA_IN_CARICO.pm:129
#, perl-brace-format
msgid "Unable to find info for user with login {login}"
msgstr "Impossibile recuperare le informazioni per l'utente con login {login}"
