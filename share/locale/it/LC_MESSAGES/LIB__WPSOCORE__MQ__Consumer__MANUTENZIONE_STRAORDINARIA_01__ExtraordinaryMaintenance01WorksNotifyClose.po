# Language file for package WPSOCORE::MQ::Consumer::MANUTENZIONE_STRAORDINARIA_01::ExtraordinaryMaintenance01WorksNotifyClose.
# Copyright (C) 2021 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::MANUTENZIONE_STRAORDINARIA_01::ExtraordinaryMaintenance01WorksNotifyClose\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>, "
"r.belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/"
"MANUTENZIONE_STRAORDINARIA_01\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ExtraordinaryMaintenance01WorksNotifyClose.pm\n"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:63
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:64
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:103
#, perl-brace-format
msgid "Missing opening event for work activity id {workActivityId}"
msgstr "Evento apertura mancante per attività lavoro con id {workActivityId}"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:112
#, perl-brace-format
msgid "Activity {id} is locked: wait {x} seconds"
msgstr "Attività con {id} locked: aspetto {x} secondi"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:116
#, perl-brace-format
msgid "Activity {id} is still locked after {x} seconds"
msgstr "Attività con {id} è ancora locked dopo {x} secondi"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:131
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: ExtraordinaryMaintenance01WorksNotifyClose.pm:136
#, perl-brace-format
msgid "Unable to find ongoing activity {id}"
msgstr "Impossibile trovare un'attività in corso con id {id}"
