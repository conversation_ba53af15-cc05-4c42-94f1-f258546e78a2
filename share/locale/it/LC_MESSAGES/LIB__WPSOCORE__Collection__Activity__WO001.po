# Language file for package WPSOCORE::Collection::Activity::WO001
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::WO001\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WO001.pm\n"

#: WO001.pm:32
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro {paramname} obbligatorio mancante"

#: WO001.pm:33
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere del tipo {type}"

#: WO001.pm:95
#, perl-brace-format
msgid "Param {param} must be an array of hashes"
msgstr "Il parametro {param} deve essere un array di hash"

#: WO001.pm:124
#, perl-brace-format
msgid "Find more than one customer for {customerId}"
msgstr "Trovato più di un cliente per {customerId}"

#: WO001.pm:128
#, perl-brace-format
msgid "Unable to find customer for {customerId}"
msgstr "Impossibile trovare cliente per {customerId}"

#: WO001.pm:153
#, perl-brace-format
msgid "Find more than one contract for {contractId}"
msgstr "Trovato più di un cliente per {contractId}"

#: WO001.pm:157
#, perl-brace-format
msgid "Unable to find contract for {contractId}"
msgstr "Impossibile trovare contratto per {contractId}"

#: WO001.pm:178
#, perl-brace-format
msgid "When param '{param}' = '{value}' then param '{param1}' is mandatory"
msgstr ""
"Quando il parametro '{param}' = '{value}' allora il parametro '{param1}' è "
"obbligatorio"

#: WO001.pm:188
#, perl-brace-format
msgid "When param '{param}' is defined then param '{param1}' is mandatory"
msgstr ""
"Quando il parametro '{param}' è definito allora il parametro '{param1}' è "
"obbligatorio"

#: WO001.pm:196
#, perl-brace-format
msgid "Invalid date '{value}' for param '{param}'"
msgstr "Data non valida '{value}' per il parametro '{param}'"

#: WO001.pm:201
#, perl-brace-format
msgid "Invalid date '{value}' for param '{param}': can not be in the future"
msgstr ""
"Data non valida '{value}' per il parametro '{param}': non può essere nel "
"futuro"

#: WO001.pm:220
#, perl-brace-format
msgid "Sirti Area not found for region '{region}'"
msgstr "Area Sirti non trovata per la regione '{region}'"

#: WO001.pm:234
#, perl-brace-format
msgid "Customer Area not found for region '{region}'"
msgstr "Area Cliente non trovata per la regione '{region}'"

#: WO001.pm:243 WO001.pm:247
#, perl-brace-format
msgid "Working group code {wgc} not available for region '{region}'"
msgstr "Centro lavoro {wgc} non disponibile per la regione '{region}'"

#, perl-brace-format
#~ msgid "Invalid param '{param}' = '{value}' for param '{param1}'"
#~ msgstr ""
#~ "Parametro invalido '{param}' = '{value}' per il parametro '{param1}'"

#, perl-brace-format
#~ msgid ""
#~ "Found more than one configuration for macroactivity {macro} and working "
#~ "group code {wgc}"
#~ msgstr ""
#~ "Trovata più di una configurazione per macroattività {macro} e centro "
#~ "lavoro {wgc}"

#, perl-brace-format
#~ msgid ""
#~ "No configuration found for macroactivity {macro} and working group code "
#~ "{wgc}"
#~ msgstr ""
#~ "Nessna configurazione trovata per macroattività {macro} e centro lavoro "
#~ "{wgc}"
