# Language file for package WebService::WSART::WPSOAPWS.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WSART::WPSOAPWS\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-24 16:57+0200\n"
"PO-Revision-Date: 2025-07-24 16:57+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WSART\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: WPSOAPWS.pm\n"

#: WPSOAPWS.pm:203
msgid "Project not found!"
msgstr "Progetto non trovato!"

#: WPSOAPWS.pm:220 WPSOAPWS.pm:233
msgid "Permits Area not found!"
msgstr "Area Permessi non trovata!"

#: WPSOAPWS.pm:253
msgid "Public permit not found!"
msgstr "Permesso lavori non trovato!"

#: WPSOAPWS.pm:286
msgid "Permit not found!"
msgstr "Permesso non trovato!"

#: WPSOAPWS.pm:320
msgid "Site not found!"
msgstr "Sito non trovato!"

#: WPSOAPWS.pm:338 WPSOAPWS.pm:351
msgid "Building not found!"
msgstr "Building non trovato!"

#: WPSOAPWS.pm:371
msgid "Private permit not found!"
msgstr "Permesso bulding non trovato!"

#: WPSOAPWS.pm:389
msgid "Not existent attachment"
msgstr "Allegato non esistente"

#: WPSOAPWS.pm:393
#, perl-brace-format
msgid "Unable to open index file {file}.idx: {error}"
msgstr "Impossibile aprire il file indice {file}.idx: {error}"

#: WPSOAPWS.pm:402
#, perl-brace-format
msgid "Unable to parse JSON idxfile {file}.idx: {error}"
msgstr "Impossibile analizzare il file indice ISON {file}.idx: {error}"

#: WPSOAPWS.pm:405
#, perl-brace-format
msgid "Unable to copy file from {file} to {file1}: {error}"
msgstr "Impossibile copiare il file da {file} a {file1}: {error}"

#: WPSOAPWS.pm:544 WPSOAPWS.pm:641 WPSOAPWS.pm:737 WPSOAPWS.pm:1024
#: WPSOAPWS.pm:1137 WPSOAPWS.pm:1584 WPSOAPWS.pm:1696 WPSOAPWS.pm:1996
#: WPSOAPWS.pm:2390 WPSOAPWS.pm:2603 WPSOAPWS.pm:2841 WPSOAPWS.pm:3160
#: WPSOAPWS.pm:3365
msgid "Invalid JSON"
msgstr "JSON non valido"

#: WPSOAPWS.pm:645
msgid "Mismatch on param permitsAreaId between uri and body"
msgstr "Incoerenza sul parametro permitsAreaId tra uri e body"

#: WPSOAPWS.pm:1999
#, perl-brace-format
msgid "Missing param {param}"
msgstr "Parametro {param} mancante"

#: WPSOAPWS.pm:2003
#, perl-brace-format
msgid "Param {param} can value only {values}"
msgstr "Il parametro {param} può valere solamente {values}"

#: WPSOAPWS.pm:2014
#, perl-brace-format
msgid "Unknown type {type}"
msgstr "Tipo {type} sconosciuto"

#: WPSOAPWS.pm:2099
#, perl-brace-format
msgid "Missing mandatory param {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: WPSOAPWS.pm:2255 WPSOAPWS.pm:2300 WPSOAPWS.pm:3025 WPSOAPWS.pm:3070
#, perl-brace-format
msgid "Unknown BLOCK_ID {value}"
msgstr "BLOCK_ID {value} sconosciuto"

#: WPSOAPWS.pm:2924
#, perl-brace-format
msgid "Activity status: {stato} not valid"
msgstr "Stato attività: {stato} non valido"

#: WPSOAPWS.pm:2974
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio mancante {paramname}"
