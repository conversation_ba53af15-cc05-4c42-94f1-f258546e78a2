# Language file for package WPSOAP::MQ::Consumer::ProjectCitiesGroupCreazione
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::MQ::Consumer::ProjectCitiesGroupCreazione\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ProjectCitiesGroupCreazione.pm\n"

#: ProjectCitiesGroupCreazione.pm:73
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: ProjectCitiesGroupCreazione.pm:74
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: ProjectCitiesGroupCreazione.pm:87
#, perl-brace-format
msgid "Unable to search system of type {type}: {error}"
msgstr "Impossibile cercare un sistema di tipo {type}: {error}"

#: ProjectCitiesGroupCreazione.pm:94
#, perl-brace-format
msgid "Found more than one system of type {type} for {key} {value}"
msgstr "Trovato più di un sistema di tipo {type} per {key} {value}"

#: ProjectCitiesGroupCreazione.pm:115 ProjectCitiesGroupCreazione.pm:132
#: ProjectCitiesGroupCreazione.pm:139
#, perl-brace-format
msgid "Unable to update system of type {type} with id {id}: {error}"
msgstr "Impossibile aggiornare il sistema di tipo {type} con id {id}: {error}"

#: ProjectCitiesGroupCreazione.pm:144
#, perl-brace-format
msgid "System of type {type} with id {id} updated"
msgstr "Sistema di tipo {type} con id {id} aggiornato"

#: ProjectCitiesGroupCreazione.pm:175
#, perl-brace-format
msgid "Unable to create system of type {type}: {error}"
msgstr "Impossibile creare un sistema di tipo {type}: {error}"

#: ProjectCitiesGroupCreazione.pm:180
#, perl-brace-format
msgid "System of type {type} created with id {id}"
msgstr "Creato sistema di tipo {type} con id {id}"

#: ProjectCitiesGroupCreazione.pm:187
msgid "Missing required data 'groups'"
msgstr "Paramentro obbligatorio 'groups'"

#: ProjectCitiesGroupCreazione.pm:194
msgid "Bad data 'groups': "
msgstr "Parametro 'groups' errato: "

#: ProjectCitiesGroupCreazione.pm:200
msgid "Bad data 'groups': must be an ARRAY"
msgstr "Parametro 'groups' errato: deve essere un ARRAY"

#: ProjectCitiesGroupCreazione.pm:209
#, perl-brace-format
msgid "Missing info group: {info}"
msgstr "Info del gruppo mancanti: {info}"

#: ProjectCitiesGroupCreazione.pm:224
#, perl-brace-format
msgid "Unable to create group {groupName}: {error}"
msgstr "Impossibile creare il gruppo {groupName}: {error}"

#: ProjectCitiesGroupCreazione.pm:229
#, perl-brace-format
msgid "Group {groupName} created with id {id}"
msgstr "Gruppo {groupName} creato con id {id}"
