# Language file for package WPSOAP::Collection::Activity::PERMESSO_BUILDING
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::Collection::Activity::PERMESSO_BUILDING\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: PERMESSO_BUILDING.pm\n"

#: PERMESSO_BUILDING.pm:28
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: PERMESSO_BUILDING.pm:29
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: PERMESSO_BUILDING.pm:86
#, perl-brace-format
msgid "User {username} can not open {type}"
msgstr "L'utente {username} non può aprire {type}"

#: PERMESSO_BUILDING.pm:156 PERMESSO_BUILDING.pm:256
#, perl-brace-format
msgid "Activity of type {type} created with id {id}"
msgstr "Attività di tipo {type} creata con id {id}"

#: PERMESSO_BUILDING.pm:175
#, perl-brace-format
msgid "Missing mandatory param {param}"
msgstr "Parametro obbligatorio {param} mancante"

#: PERMESSO_BUILDING.pm:181
#, perl-brace-format
msgid "For {param} {value} {param1} can have only one value"
msgstr "Per {param} {value} {param1} può avere solo un valore"

#: PERMESSO_BUILDING.pm:185
#, perl-brace-format
msgid "For {param} {value} {param1} must be an array"
msgstr "Per {param} {value} {param1} deve essere un array"

#: PERMESSO_BUILDING.pm:204 PERMESSO_BUILDING.pm:363
#, perl-brace-format
msgid ""
"No network found for customer {customerId}, contract {contractId} and "
"networkId {networkId}"
msgstr ""
"Nessuna network trovato per cliente {customerId}, contratto {contractId} e "
"networkId {networkId}"

#: PERMESSO_BUILDING.pm:277
msgid "assetId must be an integer"
msgstr "assetId deve essere un intero"

#: PERMESSO_BUILDING.pm:469
#, perl-brace-format
msgid "Activity of type {type} created with id {id} and assetId => {assetId}"
msgstr "Attività di tipo {type} creata con id {id} e assetId => {assetId}"
