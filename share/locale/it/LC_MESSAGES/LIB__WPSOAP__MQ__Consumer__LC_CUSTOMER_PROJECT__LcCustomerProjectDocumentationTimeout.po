# Language file for package WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectDocumentationTimeout
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectDocumentationTimeout\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.l<PERSON><PERSON>@sirti.it, <EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/MQ/Consumer/"
"LC_CUSTOMER_PROJECT\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LcCustomerProjectDocumentationTimeout.pm\n"

#: LcCustomerProjectDocumentationTimeout.pm:58
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "NOME_EVENTO: {event_name}"

#: LcCustomerProjectDocumentationTimeout.pm:59
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: LcCustomerProjectDocumentationTimeout.pm:67
#, perl-brace-format
msgid "Error retrieving activity {id}: {error}"
msgstr "Errore nel recupero dell'attività {id}: {error}"

#: LcCustomerProjectDocumentationTimeout.pm:99
#, perl-brace-format
msgid "Activity with id {id} successfully worked ({status})"
msgstr "Attività con id {id} lavorata con successo ({status})"

#: LcCustomerProjectDocumentationTimeout.pm:104
#, perl-brace-format
msgid "Activity with id {id} already managed"
msgstr "Attività con id {id} già gestita"
