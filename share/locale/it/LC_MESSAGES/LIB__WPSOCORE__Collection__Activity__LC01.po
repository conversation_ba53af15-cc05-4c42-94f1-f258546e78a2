# Language file for package WPSOCORE::Collection::Activity::LC01
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::LC01\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: ;__;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC01.pm\n"

#: LC01.pm:35
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: LC01.pm:36
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LC01.pm:94
#, perl-brace-format
msgid ""
"Too many active networks of purpose {purpose} found associated to cabinet "
"{cabinetId}"
msgstr ""
"Troppe network attive di scopo {purpose} sono associate a questo armadio "
"{cabinetId}"

#: LC01.pm:147
msgid ""
"Cabinet id must be a string of 3 alphanumeric characters optionally followed "
"by a '/' and the CNO id"
msgstr ""
"L'identificativo dell'armadio deve essere una stringa di 3 caratteri "
"alfanumerici, opzionalmente seguita da un '/' e dall'id del CNO"

#: LC01.pm:163
#, perl-brace-format
msgid "Find more than one customer for {customerId}"
msgstr "Trovato più di un cliente per {customerId}"

#: LC01.pm:167
#, perl-brace-format
msgid "Unable to find customer for {customerId}"
msgstr "Impossibile trovare il cliente per {customerId}"

#: LC01.pm:192
#, perl-brace-format
msgid "Find more than one contract for {contractId}"
msgstr "Trovato più di un contratto per {contractId}"

#: LC01.pm:196
#, perl-brace-format
msgid "Unable to find contract for {contractId}"
msgstr "Impossibile trovare il contratto per {contractId}"

#: LC01.pm:249
#, perl-brace-format
msgid "Unable to retrieve {field} for cabinet {id}"
msgstr "Impossibile recuperare {field} per l'armadio {id}"

#: LC01.pm:271
msgid "No params to calculate Cabinet coordinates"
msgstr ""
"Nessun parametro presente per poter calcolare le coordinate dell'Armadio"

#: LC01.pm:289
#, perl-brace-format
msgid "{param} not found for {param1} {value1}"
msgstr "{param} non trovato per {param1} {value1}"

#: LC01.pm:296
#, perl-brace-format
msgid ""
"Working group code {value} not available for central with id {centralId}"
msgstr ""
"Centro di lavoro {value} non disponibile per la centrale con id {centralId}"

#: LC01.pm:301
msgid "no workingGroupCode found"
msgstr "nessun gruppo di lavoro trovato"

#: LC01.pm:315
#, perl-brace-format
msgid "Error while searching subcontract items, {error}"
msgstr "Errore durante la ricerca di voci di subappalto, {error}"

#: LC01.pm:337
#, perl-brace-format
msgid ""
"Existing cabinet found with type '{found}', but now type '{new}' was "
"specified"
msgstr ""
"Esiste l'armadio con tipo '{found}', ma ora è stato specificato il tipo "
"'{new}'"

#: LC01.pm:346
#, perl-brace-format
msgid ""
"Existing cabinet found based on '{found}', but based on '{new}' was now "
"requested"
msgstr ""
"Esiste l'armadio con network su base '{found}', ma ora è stato specificata "
"network su base  '{new}'"

#: LC01.pm:363
#, perl-brace-format
msgid "Existing cabinet found with same cabined ID '{found} as now requested'"
msgstr "Trovato un armadio esistente con lo stesso ID '{found}' ora richiesto"

#: LC01.pm:367
#, perl-brace-format
msgid ""
"Existing cabinet found with incompatible cabined ID '{found}', but ID "
"'{new}' was now requested"
msgstr ""
"Trovato un armadio esistente con l'ID '{found}', incompatibile con l'ID "
"richiesto '{new}'"

#: LC01.pm:405
#, perl-brace-format
msgid "LC01 activity {id} already exists"
msgstr "L'attività LC01 {id} esiste già"

#, perl-brace-format
#~ msgid "No wbe info found for wbe {wbe}"
#~ msgstr "Nessuna informazione sulla wbe trovata per wbe {wbe}"

#, perl-brace-format
#~ msgid "No wbe info found for wbe {wbe} and working group code {wgc}"
#~ msgstr "Nessuna informazione trovata per wbe {wbe} e centro di lavoro {wgc}"

#, perl-brace-format
#~ msgid ""
#~ "Found more than one wbe info for wbe {wbe} and working group code {wgc}"
#~ msgstr ""
#~ "Trovata più di un'informazione per la wbe {wbe} e il centro di lavoro "
#~ "{wgc}"

#, perl-brace-format
#~ msgid "Found more than one wbe info for wbe {wbe}"
#~ msgstr "Trovata più di un'informazione sulla wbe per la wbe {wbe}"

#, perl-brace-format
#~ msgid "{param} not found for wbe: {wbe}"
#~ msgstr "{param} non trovato per wbe: {wbe}"

#~ msgid "workingGroupCode not uniquely determined"
#~ msgstr "gruppo di lavoro non univocamente determinato"

#, perl-brace-format
#~ msgid ""
#~ "The WBE of the PTE associated to this cabinet '{cabinetWBE}' is different "
#~ "from the WBE '{networkWBE}' associated to network {network}"
#~ msgstr ""
#~ "La WBE dei PTE associati a questo armadio '{cabinetWBE}' differisce da "
#~ "quella '{networkWBE}' associata alla network {network}"

#, perl-brace-format
#~ msgid "Unable to recover id_intervento for Cabinet OdS: '{ODS}'"
#~ msgstr "Impossibile recuperare id_intervento per l'armadio OdS: '{ODS}'"

#~ msgid "Cabinet id must be a string of 3 digits"
#~ msgstr "L'identificativo dell'armadio deve essere una stringa di 3 numeri"

#~ msgid "Mandatory parameter cabinetWBELevel3 missing"
#~ msgstr "Parametro obbligatorio cabinetWBELevel3 mancante"

#, perl-brace-format
#~ msgid "EL01 system {id} already exists"
#~ msgstr "Il sistema EL01 {id} esiste già"
