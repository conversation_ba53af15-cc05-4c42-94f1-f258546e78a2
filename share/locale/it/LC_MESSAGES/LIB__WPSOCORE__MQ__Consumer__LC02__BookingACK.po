# Language file for package WPSOCORE::MQ::Consumer::LC02::BookingACK
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::MQ::Consumer::LC02::BookingACK\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> "
"<F.Ben<PERSON>@ext.sirti.it>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/LC02\n"
"X-Poedit-KeywordsList: ;__;"
"%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: BookingACK.pm\n"

#: BookingACK.pm:67
msgid "Working RA_ID ack..."
msgstr "Lavorando RA_ID ack..."

#: BookingACK.pm:68
#, perl-brace-format
msgid "{type}: {value}"
msgstr "{type}: {value}"

#: BookingACK.pm:68
msgid "GLOBAL"
msgstr "GLOBALE"

#: BookingACK.pm:68
msgid "TARGET ACK"
msgstr "TARGET ACK"

#: BookingACK.pm:71
msgid "Unnecessary"
msgstr "Non necessario"

#: BookingACK.pm:79
#, perl-brace-format
msgid "Error retrieving activity {id}: {error}"
msgstr "Impossibile recuperare l'attività {id}: {error}"

#: BookingACK.pm:89
#, perl-brace-format
msgid "ACK KO for activity {id} not managed: invalid JSON"
msgstr "ACK KO per l'attività {id} non gestito: JSON non valido"

#: BookingACK.pm:115
#, perl-brace-format
msgid "ACK KO for activity {id} correctly managed"
msgstr "ACK KO per l'attività {id} correttamente gestito"
