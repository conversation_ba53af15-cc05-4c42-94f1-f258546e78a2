# Language file for package WPSOCORE::Collection::Activity::ROE
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::ROE\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ROE.pm\n"

#: ROE.pm:28
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: ROE.pm:29
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: ROE.pm:126
#, perl-brace-format
msgid "Contract {contractId} not found for customer {customerId}"
msgstr "Contratto {contractId} non trovato per cliente {customerId}"

#: ROE.pm:137
#, perl-brace-format
msgid "Inconsistent {param}: expected {exp}, found => {found}"
msgstr "Inconsistenza {param}: atteso {exp}, trovato => {found}"

#: ROE.pm:158
#, perl-brace-format
msgid "Inconsistent centralId: expected {exp}, found => {found}"
msgstr "centralId inconsistente: atteso {exp}, trovato => {found}"

#: ROE.pm:169
#, perl-brace-format
msgid ""
"Network {networkId} not found for customer {customerId}, contract "
"{contractId} "
msgstr ""
"Network {networkId} non trovata per cliente {customerId}, contratto "
"{contractId} "

#: ROE.pm:178
#, perl-brace-format
msgid "For roe of type '{type}' is not possible to set '{param}'"
msgstr "Per roe di tipo '{type}' non è possibile impostare '{param}'"

#: ROE.pm:182
#, perl-brace-format
msgid "For roe of type '{type}' param '{param}' is mandatory"
msgstr "Per roe di tipo '{type}' il parametro {param} è obbligatorio"

#: ROE.pm:203
#, perl-brace-format
msgid "No wbe info found for wbe {wbe}"
msgstr "Nessuna informazione sulla wbe trovata per wbe {wbe}"

#: ROE.pm:207
#, perl-brace-format
msgid "Found more than one wbe info for wbe {wbe}"
msgstr "Trovata più di un'informazione sulla wbe per la wbe {wbe}"

#: ROE.pm:216
#, perl-brace-format
msgid "{param} not found for wbe: {wbe}"
msgstr "{param} non trovato per wbe: {wbe}"

#: ROE.pm:246
#, perl-brace-format
msgid "{param} not found for {param1} {value1}"
msgstr "{param} non trovato per {param1} {value1}"

#: ROE.pm:251
#, perl-brace-format
msgid ""
"Working group code {value} not available for central with id {centralId}"
msgstr ""
"Centro di lavoro {value} non disponibile per la centrale con id {centralId}"

#: ROE.pm:262
#, perl-brace-format
msgid "Unknown {param} {value}"
msgstr "Parametro {param} {value} sconosciuto"

#~ msgid "Missing mandatory param: {param}"
#~ msgstr "Parametro obbligatorio mancante: {param}"

#~ msgid "Bad {param} {value} for {param1} {value1}"
#~ msgstr "{param} {value} errata per {param1} {value1}"
