# Language file for package WPSOAP::Collection::Activity::LC_CUSTOMER_PROJECT
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::Collection::Activity::LC_CUSTOMER_PROJECT\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:10+0200\n"
"PO-Revision-Date: 2025-07-17 11:10+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: LC_CUSTOMER_PROJECT.pm\n"

#: LC_CUSTOMER_PROJECT.pm:27
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: LC_CUSTOMER_PROJECT.pm:28
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: LC_CUSTOMER_PROJECT.pm:123
#, perl-brace-format
msgid ""
"Activity of type {type} with id {id} updated (projectCode => {projectCode}, "
"status => {status}"
msgstr ""
"Attività di tipo {type} con id {id} aggiornata (codice progetto => "
"{projectCode}, stato => {status}"

#: LC_CUSTOMER_PROJECT.pm:182
#, perl-brace-format
msgid ""
"Activity of type {type} created with id {id} and projectCode => {projectCode}"
msgstr ""
"Attività di tipo {type} creata con id {id} e codice progetto => {projectCode}"
