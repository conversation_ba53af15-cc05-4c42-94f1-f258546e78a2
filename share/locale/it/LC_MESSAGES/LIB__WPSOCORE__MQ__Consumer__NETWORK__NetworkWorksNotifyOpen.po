# Language file for package WPSOCORE::MQ::Consumer::NETWORK::NetworkWorksNotifyOpen
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::NETWORK::NetworkWorksNotifyOpen\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:45+0200\n"
"PO-Revision-Date: 2025-08-06 09:45+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/"
"NETWORK\n"
"X-Poedit-KeywordsList: __;;"
"%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkWorksNotifyOpen.pm\n"

#: NetworkWorksNotifyOpen.pm:61
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENTO: {event_name}"

#: NetworkWorksNotifyOpen.pm:62
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkWorksNotifyOpen.pm:95
#, perl-brace-format
msgid "Activity with id {id} updated"
msgstr "Attività con id {id} aggiornata"

#: NetworkWorksNotifyOpen.pm:100
#, perl-brace-format
msgid "Unable to find ongoing network {networkId}"
msgstr "Impossibile trovare network {networkId} in corso"
