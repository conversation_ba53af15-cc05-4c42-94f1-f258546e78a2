# Language file for package WPSOCORE::Collection::System::ROE
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::System::ROE\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/System\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ROE.pm\n"

#: ROE.pm:27
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: ROE.pm:28
#, perl-brace-format
msgid "Bad type for param {paramname}"
msgstr "Tipo errato per il parametro {paramname}"

#: ROE.pm:157 ROE.pm:161
#, perl-brace-format
msgid ""
"Bad param {param} in class constructor: found {found} expected {expected}"
msgstr ""
"Parametro {param} errato nel costruttore di classe: trovato {found} atteso "
"{expected}"

#: ROE.pm:174
#, perl-brace-format
msgid ""
"ROE with centralId {centralId}, CNO {CNO} and ROE {ROE} already present!"
msgstr "ROE con centralId {centralId}, CNO {CNO} e ROE {ROE} già presente!"

#~ msgid "Param {paramname} must be of type {type}"
#~ msgstr "Il parametro {paramname} deve essere di tipo {type}"
