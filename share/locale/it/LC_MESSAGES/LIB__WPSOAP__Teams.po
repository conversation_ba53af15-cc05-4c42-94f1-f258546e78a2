# Language file for package WPSOAP::Teams
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.l<PERSON><PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOAP::Teams\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:11+0200\n"
"PO-Revision-Date: 2025-07-17 11:11+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOAP\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: Teams.pm\n"

#: Teams.pm:72
#, perl-brace-format
msgid "One param between {param1}, {param2} or {param3} must be defined"
msgstr "Un parametro tra {param1}, {param2} o {param3} deve essere definito"

#: Teams.pm:76
#, perl-brace-format
msgid "Only one param between {param1}, {param2} and {param3} must be defined"
msgstr ""
"Solamente un parametro tra {param1}, {param2} e {param3} deve essere definito"

#, perl-brace-format
#~ msgid "You must defined param {param1} or the params {param2}/{param3}"
#~ msgstr "Devi definire il parametro {param1} o i parametri {param2}/{param3}"

#, perl-brace-format
#~ msgid "Only param {param1} or the params {param2}/{param3} must be defined"
#~ msgstr ""
#~ "Solamente il parametro {param1} o i parametri {param2}/{param3} devono "
#~ "essere definiti"

#, perl-brace-format
#~ msgid "Unable to find customer {param}"
#~ msgstr "Impossibile trovare cliente {param}"

#, perl-brace-format
#~ msgid "Find {scalar} customers {param}"
#~ msgstr "Trovati {scalar} clienti {param}"

#, perl-brace-format
#~ msgid "Unable to find contract {param}"
#~ msgstr "Impossibile trovare contratto {param}"

#, perl-brace-format
#~ msgid "Find {scalar} contracts {param}"
#~ msgstr "Trovati {scalar} contratti {param}"
