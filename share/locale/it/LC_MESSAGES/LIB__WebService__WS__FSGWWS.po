# Language file for package WebService::WS::FSGWWS.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018.
#
msgid ""
msgstr ""
"Project-Id-Version: WebService::WS::FSGWWS\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:45+0200\n"
"PO-Revision-Date: 2025-08-06 09:45+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WebService/WS\n"
"X-Poedit-KeywordsList: __;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: FSGWWS.pm\n"

#: FSGWWS.pm:117
#, perl-brace-format
msgid ""
"Client error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato client (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: FSGWWS.pm:119
#, perl-brace-format
msgid ""
"Server error (HTTP_STATUS: {response_code}, MESSAGE: {response_content})"
msgstr ""
"Errore lato server (HTTP_STATUS: {response_code}, MESSAGE: "
"{response_content})"

#: FSGWWS.pm:129
msgid "Missing header x-api-key"
msgstr "Header x-api-key mancante"

#: FSGWWS.pm:133 FSGWWS.pm:139
msgid "Invalid value for header x-api-key"
msgstr "Valore invalido per l'header x-api-key"

#: FSGWWS.pm:142
msgid "Mismatche between user and x-api-key"
msgstr "Incongruenza tra user d x-api-key"

#: FSGWWS.pm:319 FSGWWS.pm:500 FSGWWS.pm:640 FSGWWS.pm:767 FSGWWS.pm:895
msgid "Invalid JSON"
msgstr "JSON non valido"

#: FSGWWS.pm:323 FSGWWS.pm:504 FSGWWS.pm:644 FSGWWS.pm:771 FSGWWS.pm:899
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: FSGWWS.pm:328 FSGWWS.pm:509 FSGWWS.pm:649 FSGWWS.pm:776 FSGWWS.pm:904
#, perl-brace-format
msgid "Bad value for {paramname} param: must be a scalar"
msgstr "Valore errato per il parametro {paramname}: deve essere uno scalare"

#: FSGWWS.pm:333 FSGWWS.pm:514 FSGWWS.pm:654 FSGWWS.pm:909
#, perl-brace-format
msgid "Bad value for {paramname} param: must be an hash"
msgstr "Valore errato per il parametro {paramname}: deve essere un hash"

#: FSGWWS.pm:391 FSGWWS.pm:564 FSGWWS.pm:705 FSGWWS.pm:833 FSGWWS.pm:939
#: FSGWWS.pm:1049
msgid "Missing configuration for this request"
msgstr "Configurazione mancante per questa richiesta"

#: FSGWWS.pm:533 FSGWWS.pm:677 FSGWWS.pm:805 FSGWWS.pm:1022
#, perl-brace-format
msgid "Unable to find resource for fieldServiceId {fieldServiceId}"
msgstr "Impossibile trovare una risorsa per fieldServiceId {fieldServiceId}"

#: FSGWWS.pm:914
#, perl-brace-format
msgid "Bad value for {paramname} param: expected {expect}"
msgstr "Valore errato per il parametro {paramname}: atteso {expect}"

#: FSGWWS.pm:1003
#, perl-brace-format
msgid "Invalid uri_param {param} = {value}"
msgstr "Invalido uri_param {param} = {value}"
