# Language file for package API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Action::CONFERMA_POSITIVA.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"API::ART::APP::Activity::PERMESSO_LAVORI::Binding::Action::CONFERMA_POSITIVA\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:10+0200\n"
"PO-Revision-Date: 2025-07-17 11:10+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.liv<PERSON><EMAIL>, <EMAIL>, <PERSON><PERSON>be<PERSON><PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity/PERMESSO_LAVORI/"
"Binding/Action\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: CONFERMA_POSITIVA.pm\n"

#: CONFERMA_POSITIVA.pm:31
#, perl-brace-format
msgid "Param {name} must be a {type}"
msgstr "Il parametro {name} deve essere un {type}"

#: CONFERMA_POSITIVA.pm:31
msgid "amount"
msgstr "quantità"

#: CONFERMA_POSITIVA.pm:31
msgid "integer"
msgstr "intero"

#: CONFERMA_POSITIVA.pm:38
#, perl-brace-format
msgid "Date {date1} must be equal or greater than {date2}"
msgstr "Data {date1} deve essere uguale o maggiore a {date2}"

#: CONFERMA_POSITIVA.pm:38
msgid "endAuthorizationDate"
msgstr "dataFineAutorizzazione"

#: CONFERMA_POSITIVA.pm:38
msgid "authorizationDate"
msgstr "dataAutorizzazione"
