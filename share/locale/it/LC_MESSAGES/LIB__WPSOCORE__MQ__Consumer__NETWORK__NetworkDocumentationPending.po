# Language file for package WPSOCORE::MQ::Consumer::NETWORK::NetworkDocumentationPending
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"WPSOCORE::MQ::Consumer::NETWORK::NetworkDocumentationPending\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer/NETWORK\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: NetworkDocumentationPending.pm\n"

#: NetworkDocumentationPending.pm:54
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: NetworkDocumentationPending.pm:55
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: NetworkDocumentationPending.pm:62
#, perl-brace-format
msgid "Error retrieving activity {id}: {error}"
msgstr "Impossibile recuperare l'attività {id}: {error}"

#: NetworkDocumentationPending.pm:120
#, perl-brace-format
msgid "Error working activity {id}: {error}"
msgstr "Impossibile lavorare l'attività {id}: {error}"

#: NetworkDocumentationPending.pm:125
#, perl-brace-format
msgid "No mailing list defined for workingGroup {CdL}"
msgstr "Nessuna mailing list definita per il centro di lavoro {CdL}"

#: NetworkDocumentationPending.pm:128
#, perl-brace-format
msgid "Activity with id {id} successfully worked"
msgstr "Attività con id {id} lavorata correttamente"

#: NetworkDocumentationPending.pm:133
#, perl-brace-format
msgid "Activity with id {id} already managed"
msgstr "Attività con id {id} già gestita"
