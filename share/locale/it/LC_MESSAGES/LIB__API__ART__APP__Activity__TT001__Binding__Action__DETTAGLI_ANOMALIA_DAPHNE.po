# Language file for package API::ART::APP::Activity::TT001::Binding::Action::DETTAGLI_ANOMALIA_DAPHNE.
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: "
"API::ART::APP::Activity::TT001::Binding::Action::DETTAGLI_ANOMALIA_DAPHNE\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-17 11:10+0200\n"
"PO-Revision-Date: 2025-07-17 11:10+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<a.liv<PERSON><EMAIL>, <EMAIL>, <PERSON><PERSON>belot<PERSON>@sirti.it>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/API/ART/APP/Activity/TT001/Binding/"
"Action\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: DETTAGLI_ANOMALIA_DAPHNE.pm\n"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:20
#, perl-brace-format
msgid "action not available for activity with customersystem {customerSystem}"
msgstr ""
"azione non disponibile per attività per 'Sistema cliente' {customerSystem}"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:40
#, perl-brace-format
msgid ""
"Invalid reportingUser {reportingUser} value: must be alphanumeric and no "
"more than 8 characters"
msgstr ""
"Valore invalido per reportingUser {reportingUser}: deve essere alfanumerico "
"e non più lungo di 8 caratteri"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:48
msgid "Exceeded the maximum limit of 3 attachments"
msgstr "Superato il limite massimo di 3 allegati"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:56
msgid "Attachment size exceeds the 1.5 MB limit"
msgstr "Ecceduta dimensione massima file allegati pari a 1.5 MB"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:65
#, perl-brace-format
msgid "Missing mandatory param '{key}'"
msgstr "Parametro obbligatorio mancante '{key}'"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:73
#, perl-brace-format
msgid "Field '{key}' to be populated only if Network of type FTTH"
msgstr ""
"Il campo '{key}' deve essere compilato solo se la network è di tipo FTTH"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:80
#, perl-brace-format
msgid "param '{key}' can be worth a maximum of 999"
msgstr "il parametro '{key}' può valere al massimo 999"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:85
#, perl-brace-format
msgid "param '{key}' can't be longer than 20 characters"
msgstr "il parametro '{key}' non può essere più lungo di 20 caratteri"

#: DETTAGLI_ANOMALIA_DAPHNE.pm:133
#, perl-brace-format
msgid "Unable to find email for Username {username} "
msgstr "Impossibile trovare l'email for il nome utente {username} "

#: DETTAGLI_ANOMALIA_DAPHNE.pm:153
#, perl-brace-format
msgid "Unable to find email for {label}:{clusterManagerId}"
msgstr "Impossibile trovare l'email per {label}:{clusterManagerId}"

#, perl-brace-format
#~ msgid "Unable to find email for {label}:{technicalAssistantId}"
#~ msgstr "Impossibile trovare l'email per {label}:{technicalAssistantId}"

#, perl-brace-format
#~ msgid "Invalid email {email}"
#~ msgstr "Email invalida {email}"

#~ msgid "Missing mandatory param ATTACHMENTS"
#~ msgstr "Allegati obbligatori"
