# Language file for package WPSOCORE::MQ::Consumer::ApNotifyGenericEvent.
# Copyright (C) 2021 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><EMAIL>>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::MQ::Consumer::ApNotifyGenericEvent\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <F.<PERSON>@ext.sirti.net>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, "
"<EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/MQ/Consumer\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: ApNotifyGenericEvent.pm\n"

#: ApNotifyGenericEvent.pm:61
#, perl-brace-format
msgid "EVENT_NAME: {event_name}"
msgstr "EVENT_NAME: {event_name}"

#: ApNotifyGenericEvent.pm:62
#, perl-brace-format
msgid "SOURCE_REF: {source_ref}"
msgstr "SOURCE_REF: {source_ref}"

#: ApNotifyGenericEvent.pm:140
#, perl-brace-format
msgid "Unable to find ongoing network {networkId}"
msgstr "Impossibile trovare la network {networkId}"

#: ApNotifyGenericEvent.pm:153
#, perl-brace-format
msgid "Network {networkId} (activity id {id}) successfully worked"
msgstr "Network {networkId} (id attivita {id}) lavorata con successo"

#, perl-brace-format
#~ msgid "Not processed as network {networkId} already closed"
#~ msgstr "Non processato in quanto la network {networkId} è già chiusa"
