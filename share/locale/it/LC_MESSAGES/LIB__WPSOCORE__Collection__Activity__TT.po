# Language file for package WPSOCORE::Collection::Activity::TT
# Copyright (C) 2016 Sirti S.p.A.
# <PERSON><PERSON> <a.liv<PERSON><PERSON>@sirti.it>, <PERSON> <<EMAIL>>, <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: WPSOCORE::Collection::Activity::TT\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-06 09:44+0200\n"
"PO-Revision-Date: 2025-08-06 09:44+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> "
"<<EMAIL>, <EMAIL>, <EMAIL>>\n"
"Language: it\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.6\n"
"X-Poedit-Basepath: ../../../perl5/WPSOCORE/Collection/Activity\n"
"X-Poedit-KeywordsList: __;;%__;__x;__n:1,2;__nx:1,2;__xn:1,2;N__\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-SearchPath-0: TT.pm\n"

#: TT.pm:29
#, perl-brace-format
msgid "Missing mandatory param {paramname}"
msgstr "Parametro obbligatorio {paramname} mancante"

#: TT.pm:30
#, perl-brace-format
msgid "Param {paramname} must be of type {type}"
msgstr "Il parametro {paramname} deve essere di tipo {type}"

#: TT.pm:161
#, perl-brace-format
msgid ""
"Project {project} for customer_id {customer_id} and contract_id "
"{contract_id} not found!"
msgstr ""
"Progetto {project} for id_cliente {customer_id} e id_contratto {contract_id} "
"non trovato!"

#: TT.pm:200
#, perl-brace-format
msgid "{param} parameter is mandatory since requestType is 'Sopralluogo'"
msgstr "{param} parametro è obbligatorio se requestType è 'Sopralluogo'"

#: TT.pm:208
msgid "requestContext parameter is mandatory since requestType is 'As-Built'"
msgstr "Il parametro requestContext è obbligatorio se requestType è 'As-Built'"

#: TT.pm:217
msgid "asBuiltId parameter is mandatory since requestor is 'works'"
msgstr "Il parametro asBuiltId è obbligatorio se requestor vale \"works\""
