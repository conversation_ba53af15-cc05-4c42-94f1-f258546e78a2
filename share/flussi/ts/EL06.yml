---
nome_tipo_sistema: EL06
descrizione: Gestione siti
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: anticipated
    descrizione: Anticipato
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: anticipated
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cartographicUpdate
    descrizione: Aggiornamento cartografico
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cartographicUpdate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: central
    descrizione: Centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: central
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: centralCLLI
    descrizione: CLLI centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: centralCLLI
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: centralId
    descrizione: Id centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: centralId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cityId
    descrizione: Comune
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cityId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerType
    descrizione: Tipologia cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: FOL
    descrizione: FOL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: FOL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: investor
    descrizione: Investitore
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: investor
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: primaryWorks
    descrizione: Lavori di primaria
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: primaryWorks
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: regionId
    descrizione: Regione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: regionId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: restorationWorks
    descrizione: Lavori di ripristino
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: restorationWorks
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: securityDocumentation
    descrizione: Documentazione sicurezza
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: securityDocumentation
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteConstructionNetwork
    descrizione: Network realizzazione
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteConstructionNetwork
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteConstructionNetworkOld
    descrizione: Network realizzazione precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteConstructionNetworkOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteId
    descrizione: Codice sito
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteOtherNetwork
    descrizione: Network altro
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteOtherNetwork
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteOtherNetworkOld
    descrizione: Network altro precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteOtherNetworkOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteOwner
    descrizione: Proprietà
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteOwner
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: sitePrimaryNetwork
    descrizione: Network primaria
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: sitePrimaryNetwork
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: sitePrimaryNetworkOld
    descrizione: Network primaria precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: sitePrimaryNetworkOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteRestorationNetwork
    descrizione: Network ripristino
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteRestorationNetwork
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteRestorationNetworkOld
    descrizione: Network ripristino precedente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteRestorationNetworkOld
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: siteType
    descrizione: Tipologia sito
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: siteType
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: validationDL
    descrizione: Validazione DL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: validationDL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
