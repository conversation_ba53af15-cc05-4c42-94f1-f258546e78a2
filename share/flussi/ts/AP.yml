---
nome_tipo_sistema: AP
descrizione: Rappresenta ogni Area Permessi creata in SiNFO Web e che viene comunicata ad ORCHESTRATOR.
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: acceptedPermits
    descrizione: acceptedPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: acceptedPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cancelledPermits
    descrizione: cancelledPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cancelledPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: deniedPermits
    descrizione: deniedPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: deniedPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: forecastEndDate
    descrizione: forecastEndDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: forecastEndDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: forecastStartDate
    descrizione: forecastStartDate
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: forecastStartDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ongoingPermits
    descrizione: ongoingPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ongoingPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ongoingTT
    descrizione: ongoingTT
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ongoingTT
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: permitsAreaId
    descrizione: permitsAreaId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: permitsAreaId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
