---
nome_tipo_sistema: TT001
descrizione: Trouble ticketing Fibercop
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: anomalyFound
    descrizione: Anomalia trovata
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: anomalyFound
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: AOR
    descrizione: AOR
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: AOR
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: central
    descrizione: Centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: central
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: centralId
    descrizione: Id centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: centralId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: clusterManagerId
    descrizione: Ruolo Aziendale Cluster Manager
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: clusterManagerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: clusterManagerName
    descrizione: Cluster Manager
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: clusterManagerName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerContactsCC
    descrizione: Email Cliente CC
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerContactsCC
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerContactsTO
    descrizione: Email Cliente TO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerContactsTO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerSystem
    descrizione: Sistema Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerSystem
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerTicketId
    descrizione: ID ticket cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerTicketId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: errorSTD
    descrizione: Errore STD
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: errorSTD
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: FictitiousOL
    descrizione: OL Fittizio
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: FictitiousOL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: nameJOB
    descrizione: Nome JOB
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: nameJOB
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: nameOL
    descrizione: Nome OL
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: nameOL
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: nameWO
    descrizione: Nome WO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: nameWO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkId
    descrizione: Id network
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkLookup
    descrizione: Network Lookup
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: networkLookup
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: problemDescription
    descrizione: Descrizione Problema
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: problemDescription
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: qtyPTELocked
    descrizione: Quantita Pte bloccati
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: qtyPTELocked
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: reportingUser
    descrizione: Utente segnalante
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: reportingUser
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: RO
    descrizione: RO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: RO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: scope
    descrizione: Ambito
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: scope
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: scopeWO
    descrizione: Ambito WO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: scopeWO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: sector
    descrizione: settore
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: sector
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: specialisationJOB
    descrizione: Specializzazione JOB
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: specialisationJOB
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: specialisationWO
    descrizione: Specializzazione WO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: specialisationWO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: stepOperationPerformed
    descrizione: Step Operazione Eseguita
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: stepOperationPerformed
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: technicalAssistantId
    descrizione: Ruolo Aziendale AT
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: technicalAssistantId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: technicalAssistantName
    descrizione: Ass. Sirti
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: technicalAssistantName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: tt001TargetAsset
    descrizione: Centrale/Armadio
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: tt0001TargetAsset
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: typeJOB
    descrizione: Tipo JOB
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: typeJOB
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: typeWO
    descrizione: Tipo WO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: typeWO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCode
    descrizione: Centro Lavoro
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
