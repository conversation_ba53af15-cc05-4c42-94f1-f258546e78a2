---
nome_tipo_sistema: CUSTOMER_PROJECT
descrizione: Progetti cliente
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: assignmentDate
    descrizione: Data assegnazione
    tipo: TIMESTAMP
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: assignmentDate
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: central
    descrizione: Centrale
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: central
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: networkIds
    descrizione: Elenco networkId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    alias: networkIds
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectCode
    descrizione: Codice progetto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectCode
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectTypology
    descrizione: Tipologia progetto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectTypology
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: RO
    descrizione: RO
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: RO
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workingGroupCodeList
    descrizione: Elenco centri lavoro
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: Y
    um: ~
    misura: ~
    contatore: ~
    alias: workingGroupCodeList
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
