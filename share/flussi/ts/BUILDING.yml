---
nome_tipo_sistema: BUILDING
descrizione: Rappresenza ogni immobile/edificio che SiNFO dichiara come oggetto di fornitura.
manutenibile: Y
prefisso_import_automatico: ~
tdt:
  - nome: acceptedPermits
    descrizione: acceptedPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: acceptedPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: administratorEmail
    descrizione: administratorEmail
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: administratorEmail
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: administratorName
    descrizione: administratorName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: administrator<PERSON>ame
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: administratorPhone
    descrizione: administratorPhone
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: administratorPhone
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: buildingId
    descrizione: buildingId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: buildingId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: buildingSinfoId
    descrizione: buildingSinfoId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: buildingSinfoId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: cancelledPermits
    descrizione: cancelledPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: cancelledPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: city
    descrizione: city
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: city
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: completeAddress
    descrizione: completeAddress
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: completeAddress
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: contractId
    descrizione: Id Contratto
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: contractId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: country
    descrizione: country
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: country
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: customerId
    descrizione: Id Cliente
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: customerId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: deniedPermits
    descrizione: deniedPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: deniedPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: excluded
    descrizione: excluded
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: excluded
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: floorsCount
    descrizione: floorsCount
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: floorsCount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: latitude
    descrizione: latitude
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: latitude
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: locatedPte
    descrizione: locatedPte
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: locatedPte
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: longitude
    descrizione: longitude
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: longitude
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ongoingPermits
    descrizione: ongoingPermits
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ongoingPermits
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: owner
    descrizione: owner
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: owner
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: permitsAreaId
    descrizione: permitsAreaId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: permitsAreaId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: placeName
    descrizione: placeName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: placeName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: privateUI
    descrizione: privateUI
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: privateUI
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: projectId
    descrizione: projectId
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: projectId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: province
    descrizione: province
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: province
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: ptaId
    descrizione: ptaId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: ptaId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: pteId
    descrizione: pteId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: pteId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: smallBusinessUI
    descrizione: smallBusinessUI
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: smallBusinessUI
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: staircase
    descrizione: staircase
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: staircase
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: staircasesCount
    descrizione: staircasesCount
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: staircasesCount
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: streetName
    descrizione: streetName
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: streetName
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: streetNumber
    descrizione: streetNumber
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: streetNumber
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: TlcColumn
    descrizione: TlcColumn
    tipo: BOOL
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: TlcColumn
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: UIperFloor
    descrizione: UIperFloor
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: UIperFloor
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: uprightColumn
    descrizione: uprightColumn
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: uprightColumn
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: verticalConstruction
    descrizione: verticalConstruction
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: verticalConstruction
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: workZoneId
    descrizione: workZoneId
    tipo: INTEGER
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: workZoneId
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
  - nome: zip
    descrizione: zip
    tipo: STRING
    form_sequence: 1
    form_group: 1
    secret_value: N
    multiplex_value: N
    um: ~
    misura: ~
    contatore: ~
    alias: zip
    valore_min: ~
    valore_max: ~
    precisione: ~
    morto: ~
