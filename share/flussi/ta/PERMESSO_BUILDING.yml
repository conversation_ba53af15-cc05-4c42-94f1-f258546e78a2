---
tipo_attivita: PERMESSO_BUILDING
tipi_sistema:
  - BUILDING
  - NETWORK
  - PERMIT
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: privatePermitDescription
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: privatePermitCategory
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Realizzazione civile,Sottotubazione,Posa aerea'
      - posizione: 2
        descrizione: privatePermitType
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Scavo civile,Apertura pozzetto,Tubazione esterna UI,<PERSON><PERSON> in facciata'
      - posizione: 3
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: buildingSinfoId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: targetAsset
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: __DTC_CPO
        etichetta: DOC. PERMESSI OTTENUTI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: __DTC_CPR
        etichetta: DOC. PERMESSI RICHIESTI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: ref00
        etichetta: Riferimento 00
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: ref01
        etichetta: Riferimento 01
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: invalidatePreviousReferences
        etichetta: Invalida riferimenti pregresso
        tipo_ui: 'BOOL  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIUNGI_INFO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: ___ANY_STATUS___
    action: INVALIDAZIONE
    stato_finale: INVALIDATO
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: invalidateActivityId
        etichetta: Id attivitÃ  invalidazione
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: SOCIETARIZZAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectIdOld
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: NUOVO_PERMESSO
    stato_finale: NUOVA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: NUOVA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
  - stato_iniziale: NUOVA
    action: RICHIESTA_PERMESSO
    stato_finale: INOLTRATA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
    tdta:
      - posizione: 0
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: expectedAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: protocol
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: protocolDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: NUOVA
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: INOLTRATA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
  - stato_iniziale: INOLTRATA
    action: CONFERMA_POSITIVA
    stato_finale: OTTENUTA
    gruppi:
      - ADMIN
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
    tdta:
      - posizione: 0
        descrizione: authorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: INOLTRATA
    action: PERMESSO_NON_NECESSARIO
    stato_finale: NON_NECESSARIA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: INOLTRATA
    action: PERMESSO_OTTENUTO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: authorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: INOLTRATA
    action: PERMESSO_RIFIUTATO
    stato_finale: RIFIUTATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
  - stato_iniziale: INOLTRATA
    action: RIFIUTO
    stato_finale: RESPINTA
    gruppi:
      - ADMIN
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
  - stato_iniziale: INOLTRATA
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SOSPESA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SOSPESA
    action: PERMESSO_NON_NECESSARIO
    stato_finale: NON_NECESSARIA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: SOSPESA
    action: PERMESSO_OTTENUTO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: authorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SOSPESA
    action: PERMESSO_RIFIUTATO
    stato_finale: RIFIUTATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: cancelReason
        etichetta: Motivazione annullamento
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'LAVORI KO,MANCANZA PERMESSO PRIVATO,MANCANZA PERMESSO ENTE,TUBAZIONE INTERROTTA,STABILE INACCESSIBILE,ALTRO (CON NOTE),KO - Eccessiva Onerosità,KO - Utente non FTTH (Banca / Caserma / Scuola etc...),KO - Distributore Rame Non Presente,KO - Stabile disabitato/in demolizione,KO - Permesso Privato,KO - Permesso Ente,KO - In attesa Variante progetto,KO - In attesa autorizzazione utilizzo infrastruttura altro ente (IRU / illuminazione pubblica)'
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: RIPRESA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_BUILDING
      - ROOT
      - SERVICE
  - stato_iniziale: OTTENUTA
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RESPINTA
    action: CHIUSURA
    stato_finale: RIFIUTATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA
    action: RITORNA_IN_INOLTRATA
    stato_finale: INOLTRATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA
    action: RITORNA_IN_NUOVA
    stato_finale: NUOVA
    gruppi:
      - ADMIN
      - ROOT
