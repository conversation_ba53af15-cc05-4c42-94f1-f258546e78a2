---
tipo_attivita: LC_CUSTOMER_PROJECT
tipi_sistema:
  - CUSTOMER_PROJECT
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerId
        etichetta: Id Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectCode
        etichetta: Codice progetto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: assignmentDate
        etichetta: Data assegnazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: RO
        etichetta: RO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: projectTypology
        etichetta: Tipologia progetto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Anticipato,Inoltrato'
      - posizione: 5
        descrizione: central
        etichetta: Centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: __DTC_SDOC
        etichetta: 'SpeedArk - Documentazione'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: __DTC_SDIS
        etichetta: 'SpeedArk - Distinta Materiali'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: __DTC_SMIN
        etichetta: 'SpeedArk - Mail ingaggio'
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: APERTA
    action: RICHIESTA_DOCUMENTAZIONE
    stato_finale: ATTESA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: assignmentDate
        etichetta: Data assegnazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectTypology
        etichetta: Tipologia progetto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Anticipato,Inoltrato'
      - posizione: 2
        descrizione: central
        etichetta: Centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_DOCUMENTAZIONE
    action: DOCUMENTAZIONE_NON_RECUPERATA
    stato_finale: DOCUMENTAZIONE_NON_RECUPERATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_DOCUMENTAZIONE
    action: DOCUMENTAZIONE_RECUPERATA
    stato_finale: DOCUMENTAZIONE_RECUPERATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_DOCUMENTAZIONE
    action: RICEZIONE_NOTIFICA
    stato_finale: ATTESA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: assignmentDate
        etichetta: Data assegnazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectTypology
        etichetta: Tipologia progetto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Anticipato,Inoltrato'
      - posizione: 2
        descrizione: central
        etichetta: Centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: DOCUMENTAZIONE_NON_RECUPERATA
    action: RICHIESTA_DOCUMENTAZIONE
    stato_finale: ATTESA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: assignmentDate
        etichetta: Data assegnazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectTypology
        etichetta: Tipologia progetto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Anticipato,Inoltrato'
      - posizione: 2
        descrizione: central
        etichetta: Centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: DOCUMENTAZIONE_RECUPERATA
    action: RICHIESTA_DOCUMENTAZIONE
    stato_finale: ATTESA_DOCUMENTAZIONE
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: assignmentDate
        etichetta: Data assegnazione
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectTypology
        etichetta: Tipologia progetto
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Anticipato,Inoltrato'
      - posizione: 2
        descrizione: central
        etichetta: Centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
