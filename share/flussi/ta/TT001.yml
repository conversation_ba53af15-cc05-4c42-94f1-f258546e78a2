---
tipo_attivita: TT001
tipi_sistema:
  - TT001
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: customerId
        etichetta: Id Cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: contractId
        etichetta: Id Contratto
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 2
        descrizione: networkId
        etichetta: Id network
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: workingGroupCode
        etichetta: Centro Lavoro
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 4
        descrizione: centralId
        etichetta: Id centrale
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 5
        descrizione: central
        etichetta: Centrale
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 6
        descrizione: sector
        etichetta: settore
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 7
        descrizione: AOR
        etichetta: AOR
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: RO
        etichetta: RO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: technicalAssistantName
        etichetta: Ass. Sirti
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 10
        descrizione: technicalAssistantId
        etichetta: Ruolo Aziendale AT
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 11
        descrizione: clusterManagerName
        etichetta: Cluster Manager
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 12
        descrizione: clusterManagerId
        etichetta: Ruolo Aziendale Cluster Manager
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 13
        descrizione: customerSystem
        etichetta: Sistema Cliente
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'NGNEER,DAPHNE,UNICARA'
      - posizione: 14
        descrizione: customerContactsTO
        etichetta: Email Cliente TO
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 15
        descrizione: customerContactsCC
        etichetta: Email Cliente CC
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 16
        descrizione: creationDateEx
        etichetta: Data Creazione
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 17
        descrizione: supplierCode
        etichetta: Codice Fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 18
        descrizione: tt001externalId
        etichetta: ID TT applicazione di origine
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 19
        descrizione: __DTC_LOG
        etichetta: File di Log
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 20
        descrizione: __DTC_ERROR_1
        etichetta: File di Error 1
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 21
        descrizione: __DTC_ERROR_2
        etichetta: File di Error 2
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 22
        descrizione: __DTC_ALT
        etichetta: Altro
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 23
        descrizione: networkLookup
        etichetta: Ricerca Network
        tipo_ui: LOOKUP
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: |-
          {
            "TYPE": "URI",
            "URI": "../../../customers/TIM/so/projects/lookups/networkLookup",
            "LAYOUT": "AUTOCOMPLETE",
            "CONTEXT": "Config",
            "ADD_FROM_AUTOCOMPLETE_ONLY": true
          }
      - posizione: 24
        descrizione: tt001TargetAsset
        etichetta: Centrale/Armadio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 25
        descrizione: subcontractCode
        etichetta: Codice Fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 26
        descrizione: creationUserRole
        etichetta: Ruolo Utente Creazione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI_DAPHNE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: nameJOB
        etichetta: Nome JOB
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: specialisationJOB
        etichetta: Specializzazione JOB
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '5GCO,5GBH,FCOP,FCOP 1G,FFIB,TIM 1G,Altra,Nessuna'
      - posizione: 2
        descrizione: typeJOB
        etichetta: Tipo JOB
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Creation,Creation con NTW Fittizia'
      - posizione: 3
        descrizione: qtyPTELocked
        etichetta: Quantita Pte bloccati
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: reportingUser
        etichetta: Utente segnalante
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 5
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI_NGNEER
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: nameWO
        etichetta: Nome WO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: specialisationWO
        etichetta: Specializzazione WO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '5GCO,5GBH,FCOP,FCOP 1G,FFIB,TIM 1G,Altra,Nessuna'
      - posizione: 2
        descrizione: typeWO
        etichetta: Tipo WO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Aggiornamento Impresa,Network'
      - posizione: 3
        descrizione: scopeWO
        etichetta: Ambito WO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'NGN2,NGN AO,Ottico,Rame'
      - posizione: 4
        descrizione: scope
        etichetta: Ambito
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Accesso,Portanti'
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "Accesso"
          }
      - posizione: 5
        descrizione: qtyPTELocked
        etichetta: Quantita Pte bloccati
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 6
        descrizione: reportingUser
        etichetta: Utente segnalante
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 7
        descrizione: FictitiousOL
        etichetta: OL Fittizio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 8
        descrizione: errorSTD
        etichetta: Errore STD
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: 'Errore reconcile,Errore Sistemistico,Network drive X,Errore Disco Samba,Altro'
      - posizione: 9
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 10
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIORNAMENTO_DATI_UNICARA
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: anomalyFound
        etichetta: Anomalia trovata
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: nameOL
        etichetta: nameOL
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: reportingUser
        etichetta: Utente segnalante
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 3
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ___ANY_STATUS___
    action: NUOVA_DOCUMENTAZIONE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
  - stato_iniziale: APERTA
    action: ANNULLAMENTO_TT
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
  - stato_iniziale: APERTA
    action: DETTAGLI_ANOMALIA_DAPHNE
    stato_finale: ANALISI
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: nameJOB
        etichetta: Nome JOB
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: specialisationJOB
        etichetta: Specializzazione JOB
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '5GCO,5GBH,FCOP,FCOP 1G,FFIB,TIM 1G,Altra,Nessuna'
      - posizione: 2
        descrizione: typeJOB
        etichetta: Tipo JOB
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Creation,Creation con NTW Fittizia'
      - posizione: 3
        descrizione: qtyPTELocked
        etichetta: Quantita Pte bloccati
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: reportingUser
        etichetta: Utente segnalante
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: DETTAGLI_ANOMALIA_NGNEER
    stato_finale: ANALISI
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: nameWO
        etichetta: Nome WO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: specialisationWO
        etichetta: Specializzazione WO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
        valori: '5GCO,5GBH,FCOP,FCOP 1G,FFIB,TIM 1G,Altra,Nessuna'
      - posizione: 2
        descrizione: typeWO
        etichetta: Tipo WO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Aggiornamento Impresa,Network'
      - posizione: 3
        descrizione: scopeWO
        etichetta: Ambito WO
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'NGN2,NGN AO,Ottico,Rame'
      - posizione: 4
        descrizione: scope
        etichetta: Ambito
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Accesso,Portanti'
        valore_default: ''
        valore_predefinito: |-
          {
            "type": "const",
            "value": "Accesso"
          }
      - posizione: 5
        descrizione: qtyPTELocked
        etichetta: Quantita Pte bloccati
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: reportingUser
        etichetta: Utente segnalante
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: FictitiousOL
        etichetta: OL Fittizio
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 8
        descrizione: errorSTD
        etichetta: Errore STD
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Errore reconcile,Errore Sistemistico,Network drive X,Errore Disco Samba,Altro'
      - posizione: 9
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 10
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: DETTAGLI_ANOMALIA_UNICARA
    stato_finale: ANALISI
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: anomalyFound
        etichetta: Anomalia trovata
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: nameOL
        etichetta: nameOL
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: reportingUser
        etichetta: Utente segnalante
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ANALISI
    action: ANNULLAMENTO_TT
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - GBD
      - ROOT
  - stato_iniziale: ANALISI
    action: APERTURA_TT_CLIENTE
    stato_finale: ATTESA_PRESA_IN_CARICO_CLIENTE
    gruppi:
      - ADMIN
      - GBD
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerContactsTO
        etichetta: Email Cliente TO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: customerContactsCC
        etichetta: Email Cliente CC
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: telephoneContact
        etichetta: Recapito telefonico
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 5
        descrizione: dateRequestTT
        etichetta: Data Invio Richiesta
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ANALISI
    action: ASSEGNAZIONE_AD_ALTRO_AT
    stato_finale: ANALISI
    gruppi:
      - ADMIN
      - GBD
      - ROOT
    tdta:
      - posizione: 0
        descrizione: operatorId
        etichetta: Gestore Identificativo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: operatorName
        etichetta: Gestore Nome
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ANALISI
    action: NON_ACCETTATO
    stato_finale: APERTA
    gruppi:
      - ADMIN
      - GBD
      - ROOT
  - stato_iniziale: ANALISI
    action: PRESA_IN_CARICO_SIRTI
    stato_finale: ATTESA_VERIFICA_SIRTI
    gruppi:
      - ADMIN
      - GBD
      - ROOT
    tdta:
      - posizione: 0
        descrizione: operatorId
        etichetta: Gestore Identificativo
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 1
        descrizione: operatorName
        etichetta: Gestore Nome
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_PRESA_IN_CARICO_CLIENTE
    action: NON_ACCETTATO_CLIENTE
    stato_finale: ANALISI
    gruppi:
      - ADMIN
      - GBD
      - ROOT
  - stato_iniziale: ATTESA_PRESA_IN_CARICO_CLIENTE
    action: PRESA_IN_CARICO_CLIENTE
    stato_finale: IN_LAVORAZIONE_CLIENTE
    gruppi:
      - ADMIN
      - GBD
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerTicketId
        etichetta: ID ticket cliente
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ATTESA_VERIFICA_SIRTI
    action: APERTURA_TT_CLIENTE
    stato_finale: ATTESA_PRESA_IN_CARICO_CLIENTE
    gruppi:
      - ADMIN
      - GBD
      - ROOT
    tdta:
      - posizione: 0
        descrizione: customerContactsTO
        etichetta: Email Cliente TO
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: customerContactsCC
        etichetta: Email Cliente CC
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 2
        descrizione: telephoneContact
        etichetta: Recapito telefonico
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 3
        descrizione: stepOperationPerformed
        etichetta: Step Operazione Eseguita
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 4
        descrizione: problemDescription
        etichetta: Descrizione Problema
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 5
        descrizione: dateRequestTT
        etichetta: Data Invio Richiesta
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: ATTESA_VERIFICA_SIRTI
    action: RISOLUZIONE
    stato_finale: RISOLTA
    gruppi:
      - ADMIN
      - GBD
      - ROOT
  - stato_iniziale: IN_LAVORAZIONE_CLIENTE
    action: CHIUSURA_TT_CLIENTE
    stato_finale: RISOLUZIONE_LAVORAZIONE_CLIENTE
    gruppi:
      - ADMIN
      - GBD
      - ROOT
  - stato_iniziale: RISOLUZIONE_LAVORAZIONE_CLIENTE
    action: VALIDAZIONE_KO
    stato_finale: VALIDAZIONE_KO
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
  - stato_iniziale: RISOLUZIONE_LAVORAZIONE_CLIENTE
    action: VALIDAZIONE_OK
    stato_finale: RISOLTA_CLIENTE
    gruppi:
      - ADMIN
      - AT
      - GBD
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: dateOutcome
        etichetta: Data Esito TT
        tipo_ui: 'DAY   '
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
  - stato_iniziale: VALIDAZIONE_KO
    action: RIPRESA_LAVORAZIONE_CLIENTE
    stato_finale: IN_LAVORAZIONE_CLIENTE
    gruppi:
      - ADMIN
      - GBD
      - ROOT
