---
tipo_attivita: PERMESSO_LAVORI
tipi_sistema:
  - AP
  - NETWORK
  - PERMIT
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - AT
      - ROOT
    tdta:
      - posizione: 0
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: permitsAreaId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: publicPermitDescription
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: publicPermitCategory
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Realizzazione civile,Sottotubazione,Posa aerea'
      - posizione: 4
        descrizione: authority
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Comune,Provincia,ANAS,FF.SS.,Autostrade,Altro Ente'
      - posizione: 5
        descrizione: publicPermitType
        etichetta: ''
        tipo_ui: 'POPUP '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
        valori: 'Scavo tradizionale,No-Dig,Minitrincea'
      - posizione: 6
        descrizione: targetAsset
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 7
        descrizione: __DTC_CPO
        etichetta: DOC. PERMESSI OTTENUTI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 8
        descrizione: __DTC_CPR
        etichetta: DOC. PERMESSI RICHIESTI
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 9
        descrizione: __DTC_VAR
        etichetta: DOC. VARIE
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: Y
        prepopola: N
      - posizione: 10
        descrizione: ref00
        etichetta: Riferimento 00
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 11
        descrizione: ref01
        etichetta: Riferimento 01
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 12
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 13
        descrizione: expectedAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 14
        descrizione: expectedEndAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 15
        descrizione: maker
        etichetta: Esecutore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 16
        descrizione: teamId
        etichetta: Identificativo squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 17
        descrizione: teamName
        etichetta: Nome squadra
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 18
        descrizione: subContractCode
        etichetta: Identificativo fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 19
        descrizione: subContractName
        etichetta: Nome fornitore
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ___ANY_STATUS___
    action: AGGIUNGI_INFO
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: ___ANY_STATUS___
    action: SOCIETARIZZAZIONE_CLIENTE
    stato_finale: ___ANY_STATUS___
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectIdOld
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: NUOVO_PERMESSO
    stato_finale: NUOVA
    gruppi:
      - ADMIN
      - AT
      - ROOT
  - stato_iniziale: NUOVA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_AP
      - ROOT
  - stato_iniziale: NUOVA
    action: RICHIESTA_PERMESSO
    stato_finale: INOLTRATA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_AP
      - ROOT
    tdta:
      - posizione: 0
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: expectedAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: expectedEndAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: company
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: protocol
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: protocolDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: NUOVA
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_AP
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: INOLTRATA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_AP
      - ROOT
  - stato_iniziale: INOLTRATA
    action: CONFERMA_POSITIVA
    stato_finale: OTTENUTA
    gruppi:
      - ADMIN
      - REFERENTE_PERMESSI_AP
      - ROOT
    tdta:
      - posizione: 0
        descrizione: authorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: amount
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: INOLTRATA
    action: PERMESSO_NON_NECESSARIO
    stato_finale: NON_NECESSARIA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: INOLTRATA
    action: PERMESSO_OTTENUTO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: authorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: amount
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: INOLTRATA
    action: PERMESSO_RIFIUTATO
    stato_finale: RIFIUTATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: INOLTRATA
    action: RIFIUTO
    stato_finale: RESPINTA
    gruppi:
      - ADMIN
      - REFERENTE_PERMESSI_AP
      - ROOT
  - stato_iniziale: INOLTRATA
    action: SOSPENSIONE
    stato_finale: SOSPESA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_AP
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: suspensionReason
        etichetta: Motivo sospensione
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SOSPESA
    action: ANNULLAMENTO
    stato_finale: ANNULLATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: SOSPESA
    action: PERMESSO_NON_NECESSARIO
    stato_finale: NON_NECESSARIA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: SOSPESA
    action: PERMESSO_OTTENUTO
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
    tdta:
      - posizione: 0
        descrizione: authorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: endAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: amount
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: SOSPESA
    action: PERMESSO_RIFIUTATO
    stato_finale: RIFIUTATA
    gruppi:
      - ADMIN
      - AT
      - ROOT
      - SERVICE
  - stato_iniziale: SOSPESA
    action: RIPRESA
    stato_finale: RIPRESA
    gruppi:
      - ADMIN
      - AT
      - REFERENTE_PERMESSI_AP
      - ROOT
      - SERVICE
  - stato_iniziale: OTTENUTA
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: OTTENUTA
    action: RICHIESTA_DEROGA
    stato_finale: INOLTRATA
    gruppi:
      - ADMIN
      - REFERENTE_PERMESSI_AP
      - ROOT
    tdta:
      - posizione: 0
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: expectedAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: expectedEndAuthorizationDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: company
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: protocol
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: protocolDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: RESPINTA
    action: CHIUSURA
    stato_finale: RIFIUTATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA
    action: RITORNA_IN_INOLTRATA
    stato_finale: INOLTRATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: RIPRESA
    action: RITORNA_IN_NUOVA
    stato_finale: NUOVA
    gruppi:
      - ADMIN
      - ROOT
