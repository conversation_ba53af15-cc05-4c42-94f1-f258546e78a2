---
tipo_attivita: AP_LC
tipi_sistema:
  - AP
transizioni:
  - stato_iniziale: START
    action: APERTURA
    stato_finale: APERTA
    gruppi:
      - ROOT
    tdta:
      - posizione: 0
        descrizione: permitsAreaId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: projectId
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: name
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: polygon
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 5
        descrizione: infrastructureLength
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 6
        descrizione: note
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: APERTA
    action: ACQUISIZIONE
    stato_finale: ATTESA_FORECASTING
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_FORECASTING
    action: CANCELLAZIONE
    stato_finale: CANCELLATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ATTESA_FORECASTING
    action: FORECASTING
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - AT_CIVILE
      - COORDINATORE_AT_CIVILE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: forecastStartDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: forecastEndDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ATTESA_FORECASTING
    action: MODIFICA_AREA
    stato_finale: ATTESA_FORECASTING
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: name
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: polygon
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: infrastructureLength
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: note
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ACQUISITA
    action: APERTURA_TT
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ttId
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ACQUISITA
    action: CANCELLAZIONE
    stato_finale: CANCELLATA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ACQUISITA
    action: CHIUSURA
    stato_finale: CHIUSA
    gruppi:
      - ADMIN
      - ROOT
  - stato_iniziale: ACQUISITA
    action: CHIUSURA_TT
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: ttId
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ACQUISITA
    action: FORECASTING
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - AT_CIVILE
      - COORDINATORE_AT_CIVILE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: forecastStartDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
      - posizione: 1
        descrizione: forecastEndDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: Y
  - stato_iniziale: ACQUISITA
    action: MODIFICA_AREA
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: name
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 1
        descrizione: requestDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 2
        descrizione: polygon
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 3
        descrizione: infrastructureLength
        etichetta: ''
        tipo_ui: NUMBER
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
      - posizione: 4
        descrizione: note
        etichetta: ''
        tipo_ui: 'MEMO  '
        obbligatorio: N
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ACQUISITA
    action: SOPRALLUOGO
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - AT_CIVILE
      - COORDINATORE_AT_CIVILE
      - ROOT
    tdta:
      - posizione: 0
        descrizione: surveyDate
        etichetta: ''
        tipo_ui: ISODAT
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
  - stato_iniziale: ACQUISITA
    action: TENTATIVO_CANCELLAZIONE
    stato_finale: ACQUISITA
    gruppi:
      - ADMIN
      - ROOT
    tdta:
      - posizione: 0
        descrizione: reasonCancelKO
        etichetta: ''
        tipo_ui: STRING
        obbligatorio: Y
        solo_lettura: N
        nascosto: N
        prepopola: N
