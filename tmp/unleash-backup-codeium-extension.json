{"version": 2, "features": [{"name": "ACTIVITY_CONTEXT_WEIGHT", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ACTIVITY_CONTEXT_WEIGHT", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "API_SERVER_CLIENT_USE_HTTP_2", "type": "kill-switch", "description": "Enables HTTP/2 within the language server (eg. for communication with the API server).", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_CLIENT_USE_HTTP_2", "rollout": "100", "stickiness": "installationId"}, "variants": []}], "variants": []}, {"name": "API_SERVER_CUTOFF", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_CUTOFF", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "API_SERVER_ENABLE_MORE_LOGGING", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_ENABLE_MORE_LOGGING", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "API_SERVER_LIVENESS_PROBE", "type": "release", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_LIVENESS_PROBE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "API_SERVER_LOG_CONNECT_CODES", "type": "operational", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_LOG_CONNECT_CODES", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "codes", "weight": 1000, "payload": {"type": "string", "value": "invalid_argument"}, "stickiness": "default"}]}], "variants": []}, {"name": "API_SERVER_PROMPT_CACHE_REPLICAS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "4_replicas", "weight": 1000, "payload": {"type": "string", "value": "4"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "4_replicas", "weight": 1000, "payload": {"type": "string", "value": "4"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "continent", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["Europe", "North America"]}], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "4_replicas", "weight": 1000, "payload": {"type": "string", "value": "4"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_PROMPT_CACHE_REPLICAS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "2_replicas", "weight": 1000, "payload": {"type": "string", "value": "2"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "API_SERVER_VERBOSE_ERRORS", "type": "operational", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "API_SERVER_VERBOSE_ERRORS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ATTRIBUTION_KILL_SWITCH", "type": "kill-switch", "description": "Enable this kill switch to disable attribution for all users.", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [], "parameters": {}, "variants": []}, {"name": "userWithId", "constraints": [], "parameters": {"userIds": "b0ad0514-e7d5-4226-af68-21eaf43117a2"}, "variants": []}], "variants": []}, {"name": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}, {"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["false"]}, {"contextName": "continent", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["Asia"]}, {"contextName": "country", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["singapore"]}], "parameters": {"groupId": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "weight": 1000, "payload": {"type": "number", "value": "600"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "planName", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["Free"]}], "parameters": {"groupId": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "fast", "weight": 1000, "payload": {"type": "string", "value": "20"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTOCOMPLETE_DEFAULT_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "slow", "weight": 1000, "payload": {"type": "number", "value": "90"}, "stickiness": "default"}]}], "variants": []}, {"name": "AUTOCOMPLETE_FAST_DEBOUNCE_MS", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "AUTOCOMPLETE_FAST_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "semi-slow", "weight": 1000, "payload": {"type": "number", "value": "5000"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTOCOMPLETE_FAST_DEBOUNCE_MS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "5", "weight": 1000, "payload": {"type": "number", "value": "0"}, "stickiness": "default"}]}], "variants": []}, {"name": "AUTOCOMPLETE_HIDDEN_ERROR_REGEX", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTOCOMPLETE_HIDDEN_ERROR_REGEX", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "basic", "weight": 1000, "payload": {"type": "string", "value": "an internal error occurred"}, "stickiness": "default"}]}], "variants": []}, {"name": "AUTO_BANNER_KILL_SWITCH", "type": "kill-switch", "description": "Kill switch for content filter-based auto banning", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "AUTO_BANNER_KILL_SWITCH", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "BANNED_DOMAINS", "type": "permission", "description": "Banned email domains for spam mitigation.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "BANNED_DOMAINS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "string", "value": "jixiangry.com,kaixintt.com,yongyuanjk.com,lianzhanao.com,jonathana.com,joanjiaeuj.org,medicinexx.org,krystalyte.org,knockeduy.org,yvbtenp.top,senderosie.com,sttfarm.us,stpro89.us,hakatoy.us,pdpc.us,vvccu89.us,kataui.us,starbip.us,hotoph89.us,bpkt.us,nataduba.us,jianvan.com,rammohila.org,adylife.us,blatota.us,jxh.us,xinian.fun,wendin.fun,belayet.org,weigeniu.cc,ku.youngesta.org,tktok.cc,ddip88.com,kazuto123.com,kazuto1.com,kousei00.com,kousei.com,kousei2.com,haoniua.cc,fadacaia.com,hongyunyj.com,bufeng666.com,guanlig.cc,becausean.com,outsidevau.com,zhiwangtu.cc,lndfe.life,tfgzs.com,kgnce.life,diankaot.run,cotn.uk,fewne.life,youngesta.org,tkiii.vip,changebt.com,caokaf.online,eoqjjqg.com,keyboard.run,nbvidapp.cc,exiannvk.com,xintz.fun,butingquan.com,nullsto.edu.pl,mailto.plus,chitthi.in,rover.info,mailbox.in.ua,fexpost.com,fexbox.org,fextemp.com,bkle.uk,kazuto123.com,kazuto1.com,kousei00.com,kousei2.com,kousei.com,hahzo.com,merepost.com,edny.net,any.pink,dis.hopto.org,emaily.pro,lyx13.xyz,schooll.chat,zodiacalgf.com,kaixinhen.com,as.grandmada.org,involvedop.com,sciencekg.com,clulu.fun,beduo.fun,eemm.online,shunli.online,documenit.com,sanyu.online,duolun.xyz,bnsteps.com,apklamp.com,egvoo.com,calmpros.com,commentsf.com,arinuse.com,mxl001.win,442587.xyz,51788.top,tmmad.com,zvvzuv.com,wywnxa.com,x866.cc,cra335.site,qwer.usphms.shop,dapiao.online,ancd.us"}, "stickiness": "default"}]}], "variants": []}, {"name": "BANNED_IP_SOURCES", "type": "release", "description": "Banned source IP hashes, currently used for fighting spammers.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "BANNED_IP_SOURCES", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "banned", "weight": 1000, "payload": {"type": "string", "value": "33750fa8a94af0a452d2d30bad9e944fa569e112b58ba911e8d60b206282e937,5795b0391560d0bc128169ae18c9966b1304c27db3066bed4716261f89151<PERSON>,eb5acc865f3bb9f0c2c09b73875587dca11a6c802242ea15f20187b7af9717d2,20e92056a1d135ce8946ead78f64a897d8598194edf94d9175c23d4a3e855334,30859ff2e79feefe9a4bc7928dc9856fccbf7def5cbebe28f8ca0c9cf2d76527,ae59f6b6fbc480f226011071a80dc7cf736abb2a4c38b653dcc20ff0c3fcf24e,dafa1290ed88b6333824c0536c553b3f80bd72c9c0c12f9ede18a115aacd123b,5061518da28cd4ceca7721a291658cb8a81df6557ac26ce7a539114c70b86825,f9cb8cac50da7d5650719c48830907badf3330e7d1b745ea615cd246b30df687,2fff58be99f209eafbb2d43066365a1bf791c0c4986940b233f159d1225246ec,dd87f9425894c41b8fab77dc92951d791bd910fd4cf0a68463192025c7523f99,9319cbe98d571b8a3c11dfb4414c785e88694fd144048e60329cc0b862a522ed,**************,*************,*************8,************,*************8,95.216.45.233,43.134.17.224,95.217.72.181,43.134.12.154,194.36.171.49,119.28.119.172,43.153.195.56,85.194.243.117,91.103.121.31,138.201.82.55,199.245.101.143,198.56.6.108,104.194.80.193,149.88.90.176,45.134.107.27,136.227.183.162,45.84.80.16,45.143.175.113,134.199.78.221,194.187.37.144,154.30.99.146,134.199.75.67,136.227.181.96,45.88.100.36,136.227.168.216,136.227.190.22,134.199.80.223,204.217.149.135,5.253.185.239,178.171.117.81,178.171.117.81,88.214.3.213,136.227.170.191,195.133.209.198,45.82.99.33,161.0.8.31,198.56.7.117,186.179.19.20,167.234.222.222,161.0.12.208,198.190.0.126,112.17.241.235,45.139.193.125,101.32.109.185"}, "stickiness": "default"}]}], "variants": []}, {"name": "BLOCK_TAB_ON_SHOWN_AUTOCOMPLETE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "BLOCK_TAB_ON_SHOWN_AUTOCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "10.0.7"}], "parameters": {"groupId": "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "sessionId"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CASCADE_ADD_TO_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_AUTO_FIX_LINTS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_AUTO_FIX_LINTS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf"]}], "parameters": {"groupId": "CASCADE_AUTO_FIX_LINTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "encourage_giving_up_if_many_lint_steps", "weight": 600, "payload": {"type": "string", "value": "As IDE feedback, the following lint errors may be related to your recent edits up to this point. Consider whether they deserve immediate attention. If worth addressing, clearly comment on them and/or fix them. Be explicit in acknowledging lints and explaining your fix's approach. AVOID unproductive loops; if you detect yourself repeatedly creating/fixing lints in a short period, offer some thoughts but MOVE ON."}, "stickiness": "default"}, {"name": "original_prompt", "weight": 400, "payload": {"type": "string", "value": "As IDE feedback, the following lint errors may be related to your recent edits up to this point. Consider whether they deserve immediate attention. If worth addressing, clearly comment on them and/or fix them. Try to be explicit, whether that's explaining how you'll fix them, noting that you'll revisit them, or acknowledging if you don't know how to resolve them. Do try to fix them if you can, but exercise prudence; try not to create extra errors."}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_BACKGROUND_CASCADE_NUX", "type": "release", "description": "Feature flag for the background local Cascade NUX", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "CASCADE_BACKGROUND_RESEARCH_CONFIG_OVERRIDE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_BACKGROUND_RESEARCH_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4oMini_40K", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"planner_config\": {\n    \"plan_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n  },\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.05\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"40000\",\n    \"enabled\": true\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_BASE_MODEL_ID", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.6"}], "parameters": {"groupId": "CASCADE_BASE_MODEL_ID", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20068", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_BASE_MODEL_ID", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4O_mini", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4O_MINI_2024_07_18"}, "stickiness": "default"}, {"name": "HAIKU_40K", "weight": 0, "payload": {"type": "string", "value": "MODEL_CLAUDE_3_5_HAIKU_20241022"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_DEEPSEEK_R1_ACCESS", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_DEEPSEEK_R1_ACCESS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_DEEPSEEK_V3_ACCESS", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_DEEPSEEK_V3_ACCESS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_DEFAULT_MODEL_OVERRIDE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.4"}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_GPT_4_1_2025_04_14", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4_1_2025_04_14"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.6.121"}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_GPT_4_1_2025_04_14", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4_1_2025_04_14"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "10.0.20250415003630"}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_GPT_4_1_2025_04_14", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4_1_2025_04_14"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_PRIVATE_3", "weight": 1000, "payload": {"type": "string", "value": "MODEL_PRIVATE_3"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.5"}], "parameters": {"groupId": "CASCADE_DEFAULT_MODEL_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_PRIVATE_3", "weight": 1000, "payload": {"type": "string", "value": "MODEL_PRIVATE_3"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_DEPLOYMENTS_TOOLBAR_VISIBILITY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_DEPLOYMENTS_TOOLBAR_VISIBILITY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "type": "experiment", "description": "Whether to give <PERSON> the ability to create, update, and delete its own memories.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["jetbrains"]}, {"contextName": "ideVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["2.1.0", "2.0.0"]}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["jetbrains"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "2.0.0"}], "parameters": {"groupId": "CASCADE_ENABLE_AUTOMATED_MEMORIES", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ENABLE_CUSTOM_RECIPES", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "CASCADE_ENABLE_CUSTOM_RECIPES", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.32.1"}, {"contextName": "ide", "operator": "STR_CONTAINS", "caseInsensitive": true, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "CASCADE_ENABLE_IDE_TERMINAL_EXECUTION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ENABLE_MCP_TOOLS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.3.0"}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["jetbrains"]}, {"contextName": "ideVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["2.1.0", "2.0.0"]}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["jetbrains"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "2.0.0"}], "parameters": {"groupId": "CASCADE_ENABLE_MCP_TOOLS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ENABLE_PROXY_WEB_SERVER", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "CASCADE_ENABLE_PROXY_WEB_SERVER", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ENFORCE_QUOTA", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_ENFORCE_QUOTA", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_ENFORCE_QUOTA", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_FILE_OVERVIEW_POPOVER_USE_IDE_STATE", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_FILE_OVERVIEW_POPOVER_USE_IDE_STATE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_FREE_CONFIG_OVERRIDE", "type": "release", "description": "This is the set of config options we give to non-premium users by default.\n\nYou should never disable this experiment.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_FREE_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "40K_LIMIT", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.1\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"45000\",\n    \"enabled\": true,\n    \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING"]}], "parameters": {"groupId": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "ephemeral", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"planner_config\": {\n        \"include_ephemeral_message\": true,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_ALIAS_CASCADE_BASE", "MODEL_ALIAS_VISTA", "MODEL_ALIAS_SWE_1", "MODEL_ALIAS_SWE_1_LITE", "MODEL_ALIAS_SHAMU"]}], "parameters": {"groupId": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "ephemeral", "weight": 0, "payload": {"type": "json", "value": "{\n    \"memory_config\": {\n        \"add_user_memories_to_system_prompt\": false\n    },\n    \"planner_config\": {\n        \"include_ephemeral_message\": false,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "stickiness": "default"}, {"name": "no_memory", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"memory_config\": {\n        \"add_user_memories_to_system_prompt\": false\n    },\n    \"planner_config\": {\n        \"include_ephemeral_message\": false,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_GLOBAL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 0, "payload": {"type": "json", "value": "{\n    \"planner_config\": {\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "stickiness": "default"}, {"name": "no-ephemeral", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"planner_config\": {\n        \"include_ephemeral_message\": false,\n        \"tool_config\": {\n            \"run_command\": {\n                \"auto_command_config\": {\n                    \"system_allowlist\": [\n                        \"echo\",\n                        \"ls\"\n                    ],\n                    \"system_denylist\": [\n                        \"git\",\n                        \"rm\",\n                        \"pkill\",\n                        \"kubectl delete\",\n                        \"kubectl apply\",\n                        \"terraform\",\n                        \"kill\",\n                        \"del\",\n                        \"rmdir\",\n                        \"psql\",\n                        \"mv\",\n                        \"bash\",\n                        \"zsh\"\n                    ]\n                }\n            }\n        }\n    }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_MEMORY_CONFIG_OVERRIDE", "type": "release", "description": "A release toggle to configure global memory configs", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_MEMORY_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"memory_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CASCADE_MEMORY_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "windsurf-next", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"memory_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "CASCADE_MEMORY_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"memory_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_NEW_MODELS_NUX", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_NEW_MODELS_NUX", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_NEW_WAVE_2_MODELS_NUX", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_NEW_WAVE_2_MODELS_NUX", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ONBOARDING", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CASCADE_ONBOARDING", "rollout": "50", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_ONBOARDING", "rollout": "5", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_ONBOARDING_REVERT", "type": "release", "description": "Flag to hide/show the NUX for revert", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CASCADE_ONBOARDING_REVERT", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_ONBOARDING_REVERT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_OPENAI_O3_MINI_ACCESS", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_OPENAI_O3_MINI_ACCESS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "type": "release", "description": "WARNING: \n\nIt is very easy to set incompatible set of override values that will immediately cause errors. If you change these settings, you must monitor sentry for config validation errors.\n\nKnown issues:\n- If checkpoint_config.TokenThreshold <= 3 * checkpoint_config.MaxOutputTokens\n- Newly added config fields are not safely compatible with older versions. If you added a field, you cannot specify it without also specifying a target version that is >= version which added MaxOutputTokens", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_O4_MINI_HIGH"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "enterprise", "weight": 0, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "stickiness": "default"}, {"name": "saas", "weight": 1000, "payload": {"type": "json", "value": "\n{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "enterprise", "weight": 0, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "stickiness": "default"}, {"name": "saas", "weight": 1000, "payload": {"type": "json", "value": "\n{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"16384\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "enterprise", "weight": 0, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "stickiness": "default"}, {"name": "saas", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO", "MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "Enterprise", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"24576\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_O4_MINI_HIGH"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "Enterprise", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "Enterprise", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"135000\",\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"16384\",\n    \"truncation_threshold_tokens\": \"100000\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_O4_MINI_HIGH"]}], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"32768\",\n    \"truncation_threshold_tokens\": \"100000\"\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_PLAN_BASED_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"max_token_limit\": \"45000\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\",\n    \"truncation_threshold_tokens\": \"100000\"\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_PLUGINS_TAB", "type": "release", "description": "If enabled, shows the plugins tab in Cascade.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_PLUGINS_TAB", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.100"}], "parameters": {"groupId": "CASCADE_PLUGINS_TAB", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.8.0"}], "parameters": {"groupId": "CASCADE_PLUGINS_TAB", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_PREMIUM_CONFIG_OVERRIDE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_PREMIUM_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "40k_haiku", "weight": 0, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.1\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"40000\",\n    \"enabled\": true,\n    \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n  }\n}"}, "stickiness": "default"}, {"name": "40k_limit", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"checkpoint_config\": {\n    \"token_threshold\": \"30000\",\n    \"max_overhead_ratio\": \"0.1\",\n    \"moving_window_size\": \"1\",\n    \"max_token_limit\": \"45000\",\n    \"enabled\": true,\n    \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n  },\n  \"planner_config\": {\n    \"max_output_tokens\": \"8192\"\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_RECIPES_AT_MENTION_VISIBILITY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "CASCADE_RECIPES_AT_MENTION_VISIBILITY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf-insiders", "windsurf"]}], "parameters": {"groupId": "CASCADE_RECIPES_AT_MENTION_VISIBILITY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_RULES_NUX_COPY", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "CASCADE_TOOL_CALL_NUX", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_TOOL_CALL_NUX", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_TOOL_CALL_PRICING_NUX", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "type": "experiment", "description": "Experiment to inject the user memories directly into the system prompt", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "10.0.7"}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "system_prompt", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": true\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_ALIAS_CASCADE_BASE", "MODEL_ALIAS_VISTA"]}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "system_prompt", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": false\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.107"}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "in_system_prompt", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": true\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.3.10"}], "parameters": {"groupId": "CASCADE_USER_MEMORIES_IN_SYS_PROMPT", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "in_system_prompt", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"add_user_memories_to_system_prompt\": true\n}\n"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_USE_EVERGREEN_TOOLBAR", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_USE_EVERGREEN_TOOLBAR", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "CASCADE_USE_EVERGREEN_TOOLBAR", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.3.0"}], "parameters": {"groupId": "CASCADE_USE_EVERGREEN_TOOLBAR", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "type": "experiment", "description": "A flag for online checkpoint experiments", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\"\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "Hai<PERSON>", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"2\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\"\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4o-mini", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_token_limit\": \"100000\",\n  \"token_threshold\": \"85000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "stickiness": "default"}, {"name": "gemini2.0_flash", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_token_limit\": \"100000\",\n  \"token_threshold\": \"85000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_GOOGLE_GEMINI_2_0_FLASH\"\n}"}, "stickiness": "default"}, {"name": "gpt4.1_mini", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_token_limit\": \"100000\",\n  \"token_threshold\": \"85000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4_1_MINI_2025_04_14\"\n}"}, "stickiness": "default"}, {"name": "haiku", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_token_limit\": \"100000\",\n  \"token_threshold\": \"85000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CLAUDE_3_5_HAIKU_20241022\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 800, "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "stickiness": "default"}, {"name": "experiment_handler_mvs2", "weight": 150, "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"2\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}\n"}, "stickiness": "default"}, {"name": "subagent_handler", "weight": 50, "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"1\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\",\n  \"type\": \"CHECKPOINT_TYPE_SUBAGENT\"\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "autoCascadeMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_USE_EXPERIMENT_CHECKPOINTER", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "experiment_handler_mvs2", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"token_threshold\": \"30000\",\n  \"max_overhead_ratio\": \"0.1\",\n  \"moving_window_size\": \"2\",\n  \"enabled\": true,\n  \"checkpoint_model\": \"MODEL_CHAT_GPT_4O_MINI_2024_07_18\",\n  \"type\": \"CHECKPOINT_TYPE_EXPERIMENT\"\n}\n"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "type": "experiment", "description": "Whether to allow Cascade URL web document page reading", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.100"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "apply_patch", "weight": 100, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  },\n  \"tool_variant\": \"REPLACE_TOOL_VARIANT_APPLY_PATCH\"\n}"}, "stickiness": "default"}, {"name": "default", "weight": 900, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.6.0"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "50", "stickiness": "default"}, "variants": [{"name": "fallback", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.34.3"}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "base", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.6.0"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CLAUDE_3_5_SONNET_20241022", "MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "fallback", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}, {"name": "no-fallback", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "autoCascadeMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_ALIAS_SWE_1", "MODEL_ALIAS_SWE_1_LITE"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.9.0"}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "fallback", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"disable_allow_multiple\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}, {"name": "fallback_allow_multiple", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"disable_allow_multiple\": false,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}, {"name": "no_fallback", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",  \n  \"allow_partial_replacement_success\": true,\n  \"disable_allow_multiple\": true\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_ALIAS_CASCADE_BASE"]}], "parameters": {"groupId": "CASCADE_USE_REPLACE_CONTENT_EDIT_TOOL", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "fallback", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_fuzzy_edit_distance_fraction\": \"0.001\",\n  \"allow_partial_replacement_success\": true,\n  \"fast_apply_fallback_config\": {\n    \"enabled\": true,\n    \"prompt_unchanged_threshold\": 5,\n    \"content_view_radius_lines\": 200,\n    \"content_edit_radius_lines\": 5\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_USE_SUBAGENT_CHECKPOINTER", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "type": "experiment", "description": "Whether to allow Cascade URL web document page reading", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_ALIAS_CASCADE_BASE", "MODEL_ALIAS_SWE_1", "MODEL_ALIAS_SWE_1_LITE", "MODEL_ALIAS_SHAMU"]}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_UNSPECIFIED"]}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "outline", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"use_prompt_prefix\": true,\n  \"split_outline_tool\": true\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.6.110"}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "variants": [{"name": "Next", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"use_prompt_prefix\": true,\n  \"split_outline_tool\": true\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.6.0"}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "variants": [{"name": "outline", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"use_prompt_prefix\": true,\n  \"split_outline_tool\": true\n}\n"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.3.1"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next"]}], "parameters": {"groupId": "CASCADE_VIEW_FILE_TOOL_CONFIG_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "outline_with_prompt", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"allow_doc_outline\": true,\n  \"use_line_numbers_for_raw\": true,\n  \"use_prompt_prefix\": true\n}"}, "stickiness": "default"}, {"name": "raw_with_prompt", "weight": 0, "payload": {"type": "json", "value": "{\n  \"allow_doc_outline\": false,\n  \"use_line_numbers_for_raw\": true,\n  \"use_prompt_prefix\": true\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.0"}], "parameters": {"groupId": "CASCADE_WEB_APP_DEPLOYMENTS_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_WEB_SEARCH_ENABLED", "type": "experiment", "description": "Whether to search the web for pages relevant to the user's query.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_WEB_SEARCH_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_WEB_SEARCH_NUX", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CASCADE_WEB_SEARCH_NUX", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CASCADE_WEB_TOOLS_READ_URLS", "type": "experiment", "description": "Whether to allow Cascade URL web document page reading", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CASCADE_WEB_TOOLS_READ_URLS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["false"]}], "parameters": {"groupId": "CASCADE_WEB_TOOLS_READ_URLS", "rollout": "25", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CHAT_CLIENT_UNLEASH_TEST", "type": "release", "description": "A flag for testing the Unleash setup in the chat client.", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "CHAT_COMPLETION_TOKENS_SOFT_LIMIT", "type": "release", "description": "Prompt token soft limit for chat completions (command) prompt construction", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_COMPLETION_TOKENS_SOFT_LIMIT", "rollout": "20", "stickiness": "default"}, "variants": [{"name": "SOFT_LIMIT_12000", "weight": 500, "payload": {"type": "string", "value": "12000"}, "stickiness": "default"}, {"name": "SOFT_LIMIT_8000", "weight": 500, "payload": {"type": "string", "value": "8000"}, "stickiness": "default"}]}], "variants": []}, {"name": "CHAT_MODEL_CONFIG", "type": "operational", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.10.8"}, {"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT", "weight": 1000, "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_70B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.10.8"}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12437", "weight": 0, "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12437\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12491", "weight": 0, "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12491\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_LLAMA_3_1_405B_INSTRUCT", "weight": 0, "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_405B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT", "weight": 1000, "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_70B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.8.51"}, {"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.8.51"}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 950, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12437", "weight": 0, "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12437\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12491", "weight": 50, "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12491\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.8.15"}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_GPT_4", "MODEL_CHAT_GPT_4_1106_PREVIEW"]}], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "chat35_turbo", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_3_5_TURBO\", \"context_check_model_name\":\"MODEL_CHAT_12437\" \n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_MODEL_CONFIG", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\":\"MODEL_CHAT_11121\", \n  \"context_check_model_name\":\"MODEL_CHAT_11121\" \n}"}, "stickiness": "default"}]}], "variants": [{"name": "chat35_turbo", "weight": 1000, "weightType": "variable", "stickiness": "random", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_3_5_TURBO\", \"context_check_model_name\":\"MODEL_CHAT_12437\" \n}"}, "overrides": [{"contextName": "extensionVersion", "values": ["1.8.21", "1.8.20", "1.8.19", "1.8.18", "1.8.17", "1.8.16", "1.8.15", "1.8.14"]}]}, {"name": "gpt_4", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_4_1106_PREVIEW\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "gpt_4o", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_4O_2024_05_13\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "gpt35turbo_1106", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_3_5_TURBO_1106\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "MODEL_CHAT_11121", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "MODEL_CHAT_12437", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12437\",\n\"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "MODEL_CHAT_12491", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{ \n\"model_name\":\"MODEL_CHAT_12491\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "MODEL_CHAT_12968", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12968\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "MODEL_LLAMA_3_1_405B_INSTRUCT", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_405B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": []}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\n \"model_name\":\"MODEL_LLAMA_3_1_70B_INSTRUCT\", \"context_check_model_name\":\"MODEL_CHAT_12437\"\n}"}, "overrides": [{"contextName": "teamsMode", "values": ["true"]}]}]}, {"name": "CHAT_TOKENS_SOFT_LIMIT", "type": "release", "description": "Soft token limit to use for chat prompt construction", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_TOKENS_SOFT_LIMIT", "rollout": "5", "stickiness": "userId"}, "variants": [{"name": "8000", "weight": 1000, "payload": {"type": "string", "value": "8000"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CHAT_TOKENS_SOFT_LIMIT", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "6000", "weight": 1000, "payload": {"type": "string", "value": "6000"}, "stickiness": "userId"}]}], "variants": []}, {"name": "CM_MEMORY_TELEMETRY", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CM_MEMORY_TELEMETRY", "rollout": "10", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "COLLAPSE_ASSISTANT_MESSAGES", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.5.9"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_O3_MINI", "MODEL_GOOGLE_GEMINI_2_5_PRO", "MODEL_XAI_GROK_3", "MODEL_XAI_GROK_3_MINI_REASONING"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.0"}], "parameters": {"groupId": "COLLAPSE_ASSISTANT_MESSAGES", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.5.9"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CASCADE_20065", "MODEL_CASCADE_20066", "MODEL_CASCADE_20068", "MODEL_CASCADE_20069"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.0"}], "parameters": {"groupId": "COLLAPSE_ASSISTANT_MESSAGES", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "COLLECT_ONBOARDING_EVENTS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COLLECT_ONBOARDING_EVENTS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "COMBINED_MODEL_USE_FULL_INSTRUCTION_FOR_RETRIEVAL", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMBINED_MODEL_USE_FULL_INSTRUCTION_FOR_RETRIEVAL", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "COMMAND_BOX_ON_TOP", "type": "experiment", "description": "A flag to determine the command palette vs comment box", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMMAND_BOX_ON_TOP", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "COMMAND_INJECT_USER_MEMORIES", "type": "experiment", "enabled": false, "stale": false, "impressionData": true, "project": "default", "strategies": [], "variants": []}, {"name": "COMMAND_MODEL_CONFIG", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12119", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.22.4"}], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_12119", "weight": 750, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "stickiness": "random"}, {"name": "MODEL_GPT4o", "weight": 250, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_GPT_4O_2024_08_06\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.14.6"}], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12119", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_13566", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_13566\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_14255", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14255\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_14256", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14256\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMMAND_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_11121", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_11121\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_12119", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_12119\"\n}"}, "stickiness": "random"}]}], "variants": []}, {"name": "COMMAND_PROMPT_CACHE_CONFIG", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMMAND_PROMPT_CACHE_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "10files_10minutes", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_tracked_files\":10,\n  \"max_cache_age_seconds\": 600\n}"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "COMMIT_GRAPH", "type": "experiment", "description": "Enables the commit graph as a provider in the context module, and the related files tool in cascade", "enabled": false, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.24.0"}, {"contextName": "prereleaseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "COMMIT_GRAPH", "rollout": "5", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "COMPLETIONS_CCI_REFRESH_TIMEOUT_MS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETIONS_CCI_REFRESH_TIMEOUT_MS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "low_allowance", "weight": 1000, "payload": {"type": "number", "value": "50"}, "stickiness": "default"}]}], "variants": []}, {"name": "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "COMPLETIONS_MAX_VALID_ACTIONS_BEFORE_DISMISS", "weight": 1000, "payload": {"type": "number", "value": "10"}, "stickiness": "userId"}]}], "variants": []}, {"name": "COMPLETIONS_USE_COMBINED_MODEL", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "COMPLETIONS_USE_COMBINED_MODEL", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "COMPLETIONS_USE_COMBINED_MODEL", "rollout": "100", "stickiness": "random"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.2.4"}], "parameters": {"groupId": "COMPLETIONS_USE_COMBINED_MODEL", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_NAVIGATION_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_BLOCK_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "COMPLETION_SPEED_BLOCK_TYPING_TAB_JUMP_ON_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_PREDICTIVE_SUPERCOMPLETE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETION_SPEED_PREDICTIVE_SUPERCOMPLETE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_PREDICTIVE_TAB_JUMP", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETION_SPEED_PREDICTIVE_TAB_JUMP", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.32.1"}], "parameters": {"groupId": "COMPLETION_SPEED_SUPERCOMPLETE_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "COMPLETION_SPEED_TAB_JUMP_CACHE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.32.1"}], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "COMPLETION_SPEED_TAB_JUMP_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CONTEXT_ACTIVE_DOCUMENT_FRACTION", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CONTEXT_ACTIVE_DOCUMENT_FRACTION", "rollout": "66", "stickiness": "random"}, "variants": [{"name": "10", "weight": 0, "payload": {"type": "string", "value": "0.1"}, "stickiness": "random"}, {"name": "20", "weight": 500, "payload": {"type": "string", "value": "0.2"}, "stickiness": "random"}, {"name": "40", "weight": 500, "payload": {"type": "string", "value": "0.4"}, "stickiness": "random"}]}], "variants": []}, {"name": "CONTEXT_COMMAND_TRAJECTORY_PROMPT_CONFIG", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.20.11"}], "parameters": {"groupId": "CONTEXT_COMMAND_TRAJECTORY_PROMPT_CONFIG", "rollout": "50", "stickiness": "random"}, "variants": [{"name": "0.2", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"prompt_fraction\":\"0.2\"\n}"}, "stickiness": "random"}]}], "variants": []}, {"name": "CONTEXT_DOCUMENT_OUTLINE", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.21.11"}], "parameters": {"groupId": "CONTEXT_DOCUMENT_OUTLINE", "rollout": "50", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "CONTEXT_FOR_AUTOCOMPLETE", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.2.53"}], "parameters": {"groupId": "CONTEXT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CONTEXT_FOR_CHAT", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CONTEXT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CONTEXT_FOR_NONGENERIC_CHAT", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CONTEXT_FOR_NONGENERIC_CHAT", "rollout": "50", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "CORTEX_CONFIG", "type": "release", "description": "Settings for cortex", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.46"}], "parameters": {"groupId": "CORTEX_CONFIG", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": [{"name": "gpt4o", "weight": 1000, "weightType": "variable", "stickiness": "default", "payload": {"type": "json", "value": "{\n\t\"code_plan_config\": {\n\t\t\"model_config\": {\n\t\t\t\"model\": 71,\n\t\t\t\"temperature\": 0.15,\n\t\t\t\"max_input_tokens\": 24000,\n\t\t\t\"max_output_tokens\": 2000\n\t\t}\n\t}\n}"}, "overrides": []}]}, {"name": "COUNTRY_KILL_SWITCH", "type": "kill-switch", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "type": "release", "description": "Show download windsurf from the extension", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["1.31.18"]}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "country", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["China", "India", "china", "india", "CH", "IN"]}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "10", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "country", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["us", "US", "United States", "usa", "USA"]}], "parameters": {"groupId": "CROSS_SELL_EXTENSION_DOWNLOAD_WINDSURF", "rollout": "15", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "CUMULATIVE_PROMPT_CONFIG", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "default_16k", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}, {"name": "default_8k", "weight": 0, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "planName", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["Free"]}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "default_16k", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}, {"name": "default_8k", "weight": 0, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "default_16k", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}, {"name": "default_8k", "weight": 0, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "default_16k", "weight": 0, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 512,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}, {"name": "default_8k", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.35,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.525,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.125,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25\n}"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "CUSTOM_LANGUAGE_IMPORT_REGEX", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "CUSTOM_LANGUAGE_IMPORT_REGEX", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "DEBUGGING_EXPERIMENT", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "planName", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["Free"]}], "parameters": {"groupId": "DEBUGGING_EXPERIMENT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "DEFAULT_ENABLE_SEARCH", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "DEFAULT_ENABLE_SEARCH", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "DISABLE_COMPLETIONS_CACHE", "type": "release", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [], "parameters": {}, "variants": []}], "variants": []}, {"name": "DISABLE_GCP_API_SERVER_FOR_PREMIUM_CHAT", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "DISABLE_IDE_COMPLETIONS_DEBOUNCE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf-next", "windsurf"]}], "parameters": {"groupId": "DISABLE_IDE_COMPLETIONS_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "DISABLE_INFERENCE_API_SERVER", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "DISABLE_INFERENCE_API_SERVER", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "EFFICIENT_SUPERCOMPLETE_ACCEPT", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "EFFICIENT_SUPERCOMPLETE_ACCEPT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_AUTOCOMPLETE_DURING_INTELLISENSE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ENABLE_AUTOCOMPLETE_DURING_INTELLISENSE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_BACKGROUND_RESEARCH", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.24.1"}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "ENABLE_BACKGROUND_RESEARCH", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_CASCADE_SEGMENT_ANALYTICS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "ENABLE_CASCADE_SEGMENT_ANALYTICS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ENABLE_CASCADE_SEGMENT_ANALYTICS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_COMMIT_MESSAGE_GENERATION", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "ENABLE_COMMIT_MESSAGE_GENERATION", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "ENABLE_COMMIT_MESSAGE_GENERATION", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "ENABLE_COMMIT_MESSAGE_GENERATION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_CONTENT_FILTER", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "ENABLE_CONTENT_FILTER", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_QUICK_ACTIONS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "ENABLE_QUICK_ACTIONS", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf"]}], "parameters": {"groupId": "ENABLE_QUICK_ACTIONS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_RUN_COMMAND", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.26.1"}], "parameters": {"groupId": "ENABLE_RUN_COMMAND", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_SHELL_COMMAND_TRAJECTORY", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "ENABLE_SHELL_COMMAND_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "ENABLE_SHELL_COMMAND_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.6"}], "parameters": {"groupId": "ENABLE_SHELL_COMMAND_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_SUGGESTED_RESPONSES", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "ENABLE_SUGGESTED_RESPONSES", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.4.0"}], "parameters": {"groupId": "ENABLE_SUGGESTED_RESPONSES", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ENABLE_SUPERCOMPLETE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "ENABLE_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "ESTIMATE_TOKENIZER_KILL_SWITCH", "type": "experiment", "enabled": false, "stale": false, "impressionData": true, "project": "default", "strategies": [], "variants": []}, {"name": "EXAMPLE_WINDSURF_FEATURE_FLAG", "type": "release", "description": "An example feature flag for windsurf", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "EXTERNAL_MODEL_STREAM_THROUGHPUT", "type": "release", "description": "Sampler for token stream throughput for external models", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "EXTERNAL_MODEL_STREAM_THROUGHPUT", "rollout": "50", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "FAST_MULTILINE", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode", "jetbrains", "windsurf", "windsurf-insiders"]}], "parameters": {"groupId": "FAST_MULTILINE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "FAST_SPEED_KILL_SWITCH", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "FAST_SPEED_KILL_SWITCH", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "FIREWORKS_ON_DEMAND_DEPLOYMENT", "type": "experiment", "description": "Switch traffic to on demand compute for fireworks", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "FIREWORKS_ON_DEMAND_DEPLOYMENT", "rollout": "25", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "GENERATE_CODEBASE_CLUSTERS", "type": "experiment", "description": "Whether or not to generate codebase clusters for context awareness.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true", "True", "TRUE"]}], "parameters": {"groupId": "GENERATE_CODEBASE_CLUSTERS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "IMPLICIT_PLAN", "type": "operational", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.12.1"}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "IMPLICIT_PLAN", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.79"}], "parameters": {"groupId": "IMPLICIT_PLAN", "rollout": "35", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "IMPLICIT_TRAJECTORY_USE_CLIPBOARD", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "IMPLICIT_TRAJECTORY_USE_CLIPBOARD", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "IMPLICIT_TRAJECTORY_USE_INTELLISENSE", "type": "release", "enabled": true, "stale": true, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "IMPLICIT_TRAJECTORY_USE_INTELLISENSE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "IMPLICIT_USES_CLIPBOARD", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "IMPLICIT_USES_CLIPBOARD", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "INCLUDE_PROMPT_COMPONENTS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.16.2"}], "parameters": {"groupId": "INCLUDE_PROMPT_COMPONENTS", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "INCREASE_MAX_NUM_TOKENS", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "INCREASE_MAX_NUM_TOKENS", "rollout": "50", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "INCREASE_MAX_NUM_TOKENS_MORE", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "INCREASE_MAX_NUM_TOKENS", "rollout": "33", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "JETBRAINS_ENABLE_AUTOUPDATE", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "JETBRAINS_ENABLE_AUTOUPDATE", "rollout": "75", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "JETBRAINS_ENABLE_ONBOARDING", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "JETBRAINS_ENABLE_ONBOARDING", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "JETBRAINS_USE_COMMAND_DOCSTRING_GENERATION", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "JETBRAINS_USE_COMMAND_DOCSTRING_GENERATION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "JETBRAINS_USE_LEXICAL_EDITOR", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "os", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windows"]}], "parameters": {"groupId": "JETBRAINS_USE_LEXICAL_EDITOR", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "os", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["mac"]}], "parameters": {"groupId": "JETBRAINS_USE_LEXICAL_EDITOR", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "os", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["linux"]}], "parameters": {"groupId": "JETBRAINS_USE_LEXICAL_EDITOR", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "KNOWLEDGE_BASE_PROMPT_FRACTION", "type": "experiment", "description": "% of prompt that's filled with knowledge base items", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "autoCascadeMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "KNOWLEDGE_BASE_PROMPT_FRACTION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "LANGUAGE_SERVER_AUTO_RELOAD", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["1.3.101"]}], "parameters": {}, "variants": []}], "variants": []}, {"name": "LANGUAGE_SERVER_VERSION", "type": "operational", "description": "Experiment to override the language server version", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["1.15.1", "1.14.1"]}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "hotfix", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"becc6dbc\",\n    \"crc32c_linux_arm\": \"47bde85f\",\n    \"crc32c_macos_x64\": \"b7706bc5\",\n    \"crc32c_macos_arm\": \"3c0aca33\",\n    \"crc32c_windows_x64\": \"5a8346c4\",\n    \"sha\": \"54ab3e65ecfa148b8e67b77f0a9fd95973b1ae0a\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["1.15.12"]}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "hotfix", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"0c01ca3f\",\n    \"crc32c_linux_arm\": \"0afb0cd2\",\n    \"crc32c_macos_x64\": \"cebc622e\",\n    \"crc32c_macos_arm\": \"eab6e9c8\",\n    \"crc32c_windows_x64\": \"4f8b5f0d\",\n    \"sha\": \"55ba1c2d8bc6a1527d1b6f06f13a8a16fd42bbb7\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["1.15.7"]}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "hotfix", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"2937712e\",\n    \"crc32c_linux_arm\": \"6a879648\",\n    \"crc32c_macos_x64\": \"9420a79a\",\n    \"crc32c_macos_arm\": \"36cbc8de\",\n    \"crc32c_windows_x64\": \"918d8861\",\n    \"sha\": \"2fec03f5c96bbfddf7da025d62e227cb10f6e8e5\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["1.15.9"]}], "parameters": {"groupId": "LANGUAGE_SERVER_VERSION", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "hotfix", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"crc32c_linux_x64\": \"f44c26d3\",\n    \"crc32c_linux_arm\": \"4340e2c5\",\n    \"crc32c_macos_x64\": \"005a5658\",\n    \"crc32c_macos_arm\": \"ad79f978\",\n    \"crc32c_windows_x64\": \"a8947bbf\",\n    \"sha\": \"2263ab292505638487fa9b6c6a988dcc02ee253e\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "LIMIT_PREFIX", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "LIMIT_PREFIX", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "LLAMA3_405B_KILL_SWITCH", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [], "parameters": {}, "variants": []}], "variants": []}, {"name": "MIDDLE_MODE_TOKEN_VARIANT", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MIDDLE_MODE_TOKEN_VARIANT", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": [{"name": "MIDDLE_MODE_2", "weight": 334, "weightType": "variable", "stickiness": "default", "payload": {"type": "json", "value": "{\"mode_token\": \"<|middle_mode_2|>\"}"}, "overrides": []}, {"name": "MIDDLE_MODE_4", "weight": 333, "weightType": "variable", "stickiness": "default", "payload": {"type": "json", "value": "{\"mode_token\": \"<|middle_mode_4|>\"}"}, "overrides": []}, {"name": "NO_MIDDLE_MODE", "weight": 333, "weightType": "variable", "stickiness": "default", "payload": {"type": "json", "value": "{\"mode_token\": \"\"}"}, "overrides": []}]}, {"name": "MIN_IDE_VERSION", "type": "operational", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MIN_IDE_VERSION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": [{"name": "default", "weight": 1000, "weightType": "variable", "stickiness": "default", "payload": {"type": "string", "value": "codesandbox: \"1.0.0\""}, "overrides": []}]}, {"name": "MODEL_12471_TOKENS", "type": "experiment", "description": "Varying context length for MODEL_12471", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_12471_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": [{"name": "2048_tokens", "weight": 330, "weightType": "fix", "stickiness": "random", "payload": {"type": "string", "value": "2048"}, "overrides": []}, {"name": "3072_tokens", "weight": 330, "weightType": "fix", "stickiness": "random", "payload": {"type": "string", "value": "3072"}, "overrides": []}, {"name": "4096_tokens", "weight": 340, "weightType": "variable", "stickiness": "random", "payload": {"type": "string", "value": "4096"}, "overrides": []}]}, {"name": "MODEL_14602_TOKENS", "type": "experiment", "description": "Varying the context length for MODEL_14602 (3b autocomplete)", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_14602_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "2048_tokens", "weight": 500, "payload": {"type": "string", "value": "2048"}, "stickiness": "random"}, {"name": "3072_tokens", "weight": 0, "payload": {"type": "string", "value": "3072"}, "stickiness": "random"}, {"name": "4096_tokens", "weight": 500, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_15133_TOKENS", "type": "experiment", "description": "varying the context length for MODEL_15133", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "2048_tokens", "weight": 0, "payload": {"type": "string", "value": "2048"}, "stickiness": "default"}, {"name": "4096_tokens", "weight": 1000, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4096_tokens", "weight": 1000, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.31.14"}], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "2048", "weight": 1000, "payload": {"type": "string", "value": "2048"}, "stickiness": "default"}, {"name": "3072", "weight": 0, "payload": {"type": "string", "value": "3072"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15133_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "2048", "weight": 950, "payload": {"type": "string", "value": "2048"}, "stickiness": "default"}, {"name": "4096", "weight": 50, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_15133_VARIANTS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "MODEL_15133_VARIANTS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "default", "weight": 0, "stickiness": "random"}, {"name": "pro", "weight": 1000, "payload": {"type": "string", "value": "pro"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "MODEL_15133_VARIANTS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "default", "weight": 0, "stickiness": "random"}, {"name": "pro", "weight": 1000, "payload": {"type": "string", "value": "pro"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15133_VARIANTS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "default", "weight": 1000, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_15302_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15302_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "2048_tokens", "weight": 334, "payload": {"type": "string", "value": "2048"}, "stickiness": "default"}, {"name": "4096_tokens", "weight": 333, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}, {"name": "6144_tokens", "weight": 333, "payload": {"type": "string", "value": "6144"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_15335_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15335_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_TOKEN", "weight": 1000, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_15931_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_15931_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "2048_tokens", "weight": 500, "payload": {"type": "string", "value": "2048"}, "stickiness": "default"}, {"name": "4096_tokens", "weight": 500, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_8341_VARIANTS", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_8341_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": [{"name": "crusoe", "weight": 1000, "weightType": "fix", "stickiness": "random", "payload": {"type": "string", "value": "crusoe"}, "overrides": []}, {"name": "default", "weight": 0, "weightType": "variable", "stickiness": "random", "overrides": []}]}, {"name": "MODEL_CASCADE_20064_VARIANTS", "type": "experiment", "description": "route traffic between variants of MODEL_CASCADE_20064", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "MODEL_CASCADE_20064_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "stickiness": "default"}, {"name": "iceland", "weight": 0, "payload": {"type": "string", "value": "iceland"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CASCADE_20064_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "crusoe-iceland", "weight": 1000, "payload": {"type": "string", "value": "iceland"}, "stickiness": "default"}, {"name": "crusoe-sc", "weight": 0, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CASCADE_20065_VARIANTS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CASCADE_20065_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 0, "stickiness": "default"}, {"name": "iceland", "weight": 1000, "payload": {"type": "string", "value": "iceland"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CHAT_11121_VARIANTS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_11121_VARIANTS", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": [{"name": "crusoe", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "string", "value": "crusoe"}, "overrides": []}, {"name": "default", "weight": 1000, "weightType": "variable", "stickiness": "random", "overrides": []}]}, {"name": "MODEL_CHAT_12119_VARIANTS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_12119_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 0, "stickiness": "default"}, {"name": "iceland", "weight": 1000, "payload": {"type": "string", "value": "iceland"}, "stickiness": "default"}]}], "variants": [{"name": "default", "weight": 1000, "weightType": "variable", "stickiness": "default", "overrides": []}, {"name": "speculative-13175", "weight": 0, "weightType": "fix", "stickiness": "default", "payload": {"type": "string", "value": "speculative-13175"}, "overrides": []}]}, {"name": "MODEL_CHAT_15305_TOKENS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15305_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_tokens", "weight": 1000, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_15305_VARIANTS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15305_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CHAT_15476_TOKENS", "type": "experiment", "description": "Varying the autocomplete context length for MODEL_CHAT_15476 (experimental 10b combined autocomplete and supercomplete)", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15476_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "2048_tokens", "weight": 1000, "payload": {"type": "string", "value": "2048"}, "stickiness": "random"}, {"name": "2560_tokens", "weight": 0, "payload": {"type": "string", "value": "2560"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_15600_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15600_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4096_tokens", "weight": 1000, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}, {"name": "6144_tokens", "weight": 0, "payload": {"type": "string", "value": "6144"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CHAT_15729_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_15729_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4096_tokens", "weight": 0, "payload": {"type": "string", "value": "4096"}, "stickiness": "default"}, {"name": "5120_tokens", "weight": 1000, "payload": {"type": "string", "value": "5120"}, "stickiness": "default"}, {"name": "6144_tokens", "weight": 0, "payload": {"type": "string", "value": "6144"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CHAT_16579_CRUSOE_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_16579_CRUSOE_TOKENS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "2000_tokens", "weight": 0, "stickiness": "default"}, {"name": "4000_tokens", "weight": 1000, "payload": {"type": "string", "value": "4000"}, "stickiness": "default"}, {"name": "6000_tokens", "weight": 0, "payload": {"type": "string", "value": "6000"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CHAT_16579_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "MODEL_CHAT_16579_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_tokens", "weight": 0, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}, {"name": "5120_tokens", "weight": 1000, "payload": {"type": "string", "value": "5120"}, "stickiness": "random"}, {"name": "6144_tokens", "weight": 0, "payload": {"type": "string", "value": "6144"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_16579_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_tokens", "weight": 0, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}, {"name": "5120_tokens", "weight": 1000, "payload": {"type": "string", "value": "5120"}, "stickiness": "random"}, {"name": "6144_tokens", "weight": 0, "payload": {"type": "string", "value": "6144"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_16801_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_16801_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_tokens", "weight": 1000, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_18468_TOKENS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_18468_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_tokens", "weight": 0, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}, {"name": "6144_tokens", "weight": 1000, "payload": {"type": "string", "value": "6144"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_18805_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_18805_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "5120_tokens", "weight": 500, "payload": {"type": "string", "value": "5120"}, "stickiness": "random"}, {"name": "6144_tokens", "weight": 500, "payload": {"type": "string", "value": "6144"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_19040_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19040_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "6144_tokens", "weight": 1000, "payload": {"type": "string", "value": "6144"}, "stickiness": "sessionId"}, {"name": "8192_tokens", "weight": 0, "payload": {"type": "string", "value": "8192"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "MODEL_CHAT_19484_TOKENS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19484_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "4096_tokens", "weight": 0, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}, {"name": "6144_tokens", "weight": 1000, "payload": {"type": "string", "value": "6144"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_19820_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19820_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "16384_tokens", "weight": 1000, "payload": {"type": "string", "value": "16384"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "MODEL_CHAT_19821_TOKENS", "type": "release", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "12288_tokens", "weight": 0, "payload": {"type": "string", "value": "12288"}, "stickiness": "sessionId"}, {"name": "14336_tokens", "weight": 0, "payload": {"type": "string", "value": "14336"}, "stickiness": "sessionId"}, {"name": "16384_tokens", "weight": 1000, "payload": {"type": "string", "value": "16384"}, "stickiness": "sessionId"}, {"name": "18432_tokens", "weight": 0, "payload": {"type": "string", "value": "18432"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "planName", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["Free"]}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "14336_tokens", "weight": 0, "payload": {"type": "string", "value": "14336"}, "stickiness": "sessionId"}, {"name": "16384_tokens", "weight": 1000, "payload": {"type": "string", "value": "16384"}, "stickiness": "sessionId"}, {"name": "18432_tokens", "weight": 0, "payload": {"type": "string", "value": "18432"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "12288_tokens", "weight": 0, "payload": {"type": "string", "value": "12288"}, "stickiness": "sessionId"}, {"name": "14336_tokens", "weight": 0, "payload": {"type": "string", "value": "14336"}, "stickiness": "sessionId"}, {"name": "16384_tokens", "weight": 1000, "payload": {"type": "string", "value": "16384"}, "stickiness": "sessionId"}, {"name": "18432_tokens", "weight": 0, "payload": {"type": "string", "value": "18432"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "MODEL_CHAT_19821_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "12288_tokens", "weight": 1000, "payload": {"type": "string", "value": "12288"}, "stickiness": "sessionId"}, {"name": "14336_tokens", "weight": 0, "payload": {"type": "string", "value": "14336"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "MODEL_CHAT_19821_VARIANTS", "type": "experiment", "enabled": false, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "highprio", "weight": 1000, "payload": {"type": "string", "value": "highprio"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "highprio", "weight": 1000, "payload": {"type": "string", "value": "highprio"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "country", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["india", "china"]}, {"contextName": "loadScore", "operator": "NUM_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "95"}, {"contextName": "planName", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["Pro"]}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "string", "value": "default"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "planName", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["Free"]}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "highprio", "weight": 1000, "payload": {"type": "string", "value": "highprio"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "continent", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["North America", "Europe"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.40.2"}], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "highprio", "weight": 1000, "payload": {"type": "string", "value": "highprio"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19821_VARIANTS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "default", "weight": 1000, "stickiness": "sessionId"}]}], "variants": []}, {"name": "MODEL_CHAT_19822_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_19822_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "16384_tokens", "weight": 1000, "payload": {"type": "string", "value": "16384"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "MODEL_CHAT_20706_TOKENS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_20706_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "10240_tokens", "weight": 1000, "payload": {"type": "string", "value": "10240"}, "stickiness": "random"}, {"name": "4096_tokens", "weight": 0, "payload": {"type": "string", "value": "4096"}, "stickiness": "random"}, {"name": "6144_tokens", "weight": 0, "payload": {"type": "string", "value": "6144"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_21779-cumulative-prompt-config", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_21779-cumulative-prompt-config", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "default_16k", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.33,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_document_suffix_frac\": 0.5,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.07,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25,\n    \"ephemeral_document_suffix_frac\": 0.5\n}"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_21779_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_21779_TOKENS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "18432_tokens", "weight": 1000, "payload": {"type": "string", "value": "18432"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_CHAT_22798-cumulative-prompt-config", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_22798-cumulative-prompt-config", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default_16k", "weight": 1000, "payload": {"type": "json", "value": "{\n    \"persistent_context_multiplier\": 0.25,\n    \"persistent_active_document_multiplier\": 0.5,\n    \"persistent_open_docs_multiplier\": 0.25,\n    \"persistent_max_tokens_per_open_doc\": 2048,\n    \"persistent_document_suffix_frac\": 0.5,\n    \"persistent_max_ccis_considered\": 25,\n    \"trajectory_context_multiplier\": 0.6,\n    \"trajectory_refresh_threshold_multiplier\": 0.9,\n    \"trajectory_truncation_multiplier\": 0.5,\n    \"ephemeral_context_multiplier\": 0.15,\n    \"intent_reservation_tokens\": 256,\n    \"ephemeral_active_document_multiplier\": 0.5,\n    \"ephemeral_max_ccis_considered\": 25,\n    \"ephemeral_document_suffix_frac\": 0.5\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_CHAT_23310_TOKENS", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_CHAT_23310_TOKENS", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "16384_tokens", "weight": 1000, "payload": {"type": "string", "value": "16384"}, "stickiness": "sessionId"}]}], "variants": []}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT_VARIANTS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_LLAMA_3_1_70B_INSTRUCT_LONG_CONTEXT_VARIANTS", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "default", "weight": 1000, "stickiness": "random"}, {"name": "llama-3-1-crusoe-sc", "weight": 0, "payload": {"type": "string", "value": "llama-3-1-crusoe-sc"}, "stickiness": "random"}, {"name": "llama-3-1-iceland", "weight": 0, "payload": {"type": "string", "value": "llama-3-1"}, "stickiness": "random"}, {"name": "llama-3-3", "weight": 0, "payload": {"type": "string", "value": "llama-3-3"}, "stickiness": "random"}]}], "variants": []}, {"name": "MODEL_LLAMA_3_1_70B_INSTRUCT_VARIANTS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_LLAMA_3_1_70B_INSTRUCT_VARIANTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 0, "stickiness": "default"}, {"name": "iceland", "weight": 1000, "payload": {"type": "string", "value": "iceland"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_NOTIFICATIONS", "type": "release", "description": "It's used to display a warning icon next to a model name in the model dropdown with a notification message on hover. To add a model and message update the affected_models variant payload. This flag should only be changed by on-call engineers.\n\nPayload format:\n{\n    \"model_notifications\": [\n            {\n                 \"model\": \"MODEL_CHAT_O1_MINI\",\n                 \"message\": \"Degraded performance\"\n            }\n    ]\n}", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "MODEL_NOTIFICATIONS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "insiders", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_notifications\": [\n  ]\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "MODEL_SELECTOR_NUX_COPY", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MODEL_SELECTOR_NUX_COPY", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt-4.1", "weight": 1000, "payload": {"type": "string", "value": "GPT-4.1 is available for no credit cost between April 14 and April 28."}, "stickiness": "default"}]}], "variants": []}, {"name": "MQUERY_SCORER_WITH_FALLBACK", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.12.1"}, {"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "MQUERY_SCORER_WITH_FALLBACK", "rollout": "50", "stickiness": "random"}, "variants": [{"name": "1s", "weight": 1000, "payload": {"type": "string", "value": "1000"}, "stickiness": "random"}]}], "variants": []}, {"name": "MULTILINE_MODEL_THRESHOLD", "type": "release", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "MULTILINE_MODEL_THRESHOLD", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": [{"name": "THRESHOLD_04", "weight": 0, "weightType": "fix", "stickiness": "default", "payload": {"type": "json", "value": "{\"threshold\": 0.4}"}, "overrides": []}, {"name": "THRESHOLD_045", "weight": 0, "weightType": "fix", "stickiness": "default", "payload": {"type": "json", "value": "{\"threshold\": 0.45}"}, "overrides": []}, {"name": "THRESHOLD_05", "weight": 1000, "weightType": "fix", "stickiness": "default", "payload": {"type": "json", "value": "{\"threshold\": 0.5}"}, "overrides": []}, {"name": "THRESHOLD_055", "weight": 0, "weightType": "variable", "stickiness": "default", "payload": {"type": "json", "value": "{\"threshold\": 0.55}"}, "overrides": []}, {"name": "THRESHOLD_06", "weight": 0, "weightType": "fix", "stickiness": "default", "payload": {"type": "json", "value": "{\"threshold\": 0.6}"}, "overrides": []}]}, {"name": "NON_TEAMS_KILL_SWITCH", "type": "release", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [{"contextName": "teamsMode", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {}, "variants": []}], "variants": []}, {"name": "NO_SAMPLER_EARLY_STOP", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "NO_SAMPLER_EARLY_STOP", "rollout": "50", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "ONLY_MULTILINE", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "OPEN_UNIVERSITY_ON_STARTUP", "type": "release", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["true", "True", "1"]}], "parameters": {"groupId": "OPEN_UNIVERSITY_ON_STARTUP", "rollout": "50", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "OTHER_DOCUMENTS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.16.1"}], "parameters": {"groupId": "OTHER_DOCUMENTS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "PERSIST_CODE_TRACKER", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [], "parameters": {}, "variants": []}], "variants": []}, {"name": "PIN_RECENT_FILES", "type": "experiment", "description": "Force pinning the active document and open documents to the context for retrieval", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.20.9"}], "parameters": {"groupId": "PIN_RECENT_FILES", "rollout": "20", "stickiness": "default"}, "variants": [{"name": "1", "weight": 500, "payload": {"type": "string", "value": "1"}, "stickiness": "default"}, {"name": "5", "weight": 500, "payload": {"type": "string", "value": "5"}, "stickiness": "default"}]}], "variants": []}, {"name": "POST_APPLY_DECORATION_AUTOCOMPLETE", "type": "release", "description": "Green highlighting after all accepts", "enabled": true, "stale": true, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "POST_APPLY_DECORATION_AUTOCOMPLETE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "PROFILING_TELEMETRY_SAMPLE_RATE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "PROFILING_TELEMETRY_SAMPLE_RATE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "WindsurfLS", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"memory_usage_to_sample_rate\": {\n    \"0.1\": 0.0005,\n    \"0.5\": 0.005,\n    \"1\": 0.05,\n    \"10\": 0.5\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "PRUNE_BAD_INLINE_FIM", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [], "parameters": {}, "variants": []}], "variants": []}, {"name": "QUICK_ACTIONS_WHITELIST_REGEX", "type": "release", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "QUICK_ACTIONS_WHITELIST_REGEX", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "imports", "weight": 1000, "payload": {"type": "string", "value": ".*import.*"}, "stickiness": "default"}]}], "variants": []}, {"name": "R2_LANGUAGE_SERVER_DOWNLOAD", "type": "experiment", "description": "Whether to download from Cloudfare R2 or Github Releases", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.16.11"}], "parameters": {"groupId": "R2_LANGUAGE_SERVER_DOWNLOAD", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "RANGE_TRACKING", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "RANGE_TRACKING", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "REORDER_CONTEXT_PROMPT", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "REORDER_CONTEXT_PROMPT", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "RUN_RESEARCH_STATE_PROVIDER", "type": "operational", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.63"}], "parameters": {"groupId": "RUN_RESEARCH_STATE_PROVIDER", "rollout": "5", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "SEMANTIC_CLEANUP_DIFF_KILL_SWITCH", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SEMANTIC_CLEANUP_DIFF_KILL_SWITCH", "rollout": "0", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "SENTRY", "type": "operational", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["https://<EMAIL>/4507613429563392"]}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "experimental_always", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.001,\n  \"procedure_to_sample_rate\": {\n    \"/exa.api_server_pb.ApiServerService/RecordCortexTrajectoryStep\": 0.00001,\n    \"/exa.api_server_pb.ApiServerService/RecordCortexTrajectory\": 0.00001,\n    \"/exa.api_server_pb.ApiServerService/CaptureCode\": 0.00001,\n    \"/exa.api_server_pb.ApiServerService/GetEmbeddings\": 0.0001,\n    \"/exa.auth_pb.AuthService/GetUserJwt\": 0.0001,\n    \"/exa.api_server_pb.ApiServerService/GetChatMessage\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/ValidateWindsurfJSAppProjectName\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/DeployWindsurfJSApp\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/DeleteWindsurfJSApp\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeploymentClaimStatus\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeploymentsByProjectId\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSApps\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetDeploymentProviderProjectNameByProjectId\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeploymentStatusesByProjectId\": 1.0,\n    \"/exa.api_server_pb.ApiServerService/GetWindsurfJSAppDeployment\": 1.0,\n    \"/exa.seat_management_pb.SeatManagementService.GetUserStatus\": 1.0\n  },\n  \"error_match_to_sample_rate\": {\n    \"is full, dropping record\": 0.00001,\n    \"rate limit exceeded for model\": 0.01,\n    \"missing user JWT for inference API server\": 0.0001,\n    \"user is disabled by team\": 0.00001,\n    \"team subscription is not active\": 0.00001,\n    \"user is not approved by team admin\": 0.00001,\n    \"edit summary is empty\": 0.0001,\n    \"user not authorized\": 0.00001,\n    \"empty property in unleash context\": 0.001,\n    \"invalid api key\": 0.001,\n    \"429 Too Many Requests\": 0.001,\n    \"you have been banned\": 0.001,\n    \"model API client for API_PROVIDER_OPENAI\": 0.001,\n    \"metadata.api_key: value length must be at least 1 characters\": 0.0001\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["https://<EMAIL>/4507789198753792", "https://<EMAIL>/4507463146012672", "https://<EMAIL>/4507579865366528"]}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "always", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.000001,\n  \"error_match_to_sample_rate\": {\n    \"edit summary is empty\": 0.0,\n    \"failed to check terminal shell support\": 1.0\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["https://<EMAIL>/4508406039183360"]}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "always", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.0001,\n  \"error_match_to_sample_rate\": {\n    \"edit summary is empty\": 0.0,\n    \"INTERNAL_ERROR; received from peer\": 1.0,\n    \"protocol error\": 1.0,\n    \"cascade executor error\": 0.1\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["https://<EMAIL>/4509121596358656"]}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "experimental_always", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"sample_rate\": 1.0,\n  \"procedure_to_sample_rate\": {\n    \n  },\n  \"error_match_to_sample_rate\": {\n   \n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "sentry<PERSON>n", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["https://<EMAIL>/4507737596690432"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "SENTRY", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "always", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"sample_rate\": 0.9\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "SHOULD_SHOW_DEBUG_INFO_WIDGET", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SHOULD_SHOW_DEBUG_INFO_WIDGET", "rollout": "0", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SINGLE_COMPLETION", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SINGLE_COMPLETION", "rollout": "50", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "SKIP_CONSISTENCY_MANAGER", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}, {"contextName": "prereleaseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["false"]}], "parameters": {"groupId": "SKIP_CONSISTENCY_MANAGER", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "type": "release", "description": "WARNING: \n\nIt is very easy to set incompatible set of override values that will immediately cause errors. If you change these settings, you must monitor sentry for config validation errors.\n\n\nThe correct format looks like a dict from string:string. The first string is a CORTEX_TRAJECTORY_TYPE enum name, and the second string is a serialized json corresponding to a cortex_pb.SnapshotToStepOptions proto message for that trajectory type. Be very, very careful with escaping the right characters here.\n\nThe proto set here will be merged with whatever the base proto is using default proto.Merge logic. Please check the proto definition and understand which fields are marked optional to be sure you understand the behavior. \n\nMost notably, step_type_allow_list is append only, and the \"zero-value\" will only overwrite the base config if the field is marked \"optional\".\n\n{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\"\n}\n", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "more_view_file_telemetry", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.6"}], "parameters": {"groupId": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "more_content_telemetry", "weight": 800, "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\"\n}"}, "stickiness": "default"}, {"name": "more_view_file_steps", "weight": 200, "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":3}, \\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "SNAPSHOT_TO_STEP_OPTIONS_OVERRIDE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "more_content_telemetry", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"CORTEX_TRAJECTORY_TYPE_USER_MAINLINE\": \"{\\\"code_step_creation_options\\\":{\\\"include_original_content\\\":true},\\\"view_file_step_creation_options\\\":{\\\"include_raw_content\\\":true}}\",\n  \"CORTEX_TRAJECTORY_TYPE_CASCADE\": \"{\\\"viewed_file_tracker_config\\\":{\\\"max_steps_per_checkpoint\\\":1}, \\\"step_type_allow_list\\\":[\\\"CORTEX_STEP_TYPE_VIEW_FILE\\\"]}\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "SORT_EOM_FIRST", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SORT_EOM_FIRST", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "SPLIT_MODEL", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SPLIT_MODEL", "rollout": "10", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "STREAMING_COMPLETIONS", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "STREAMING_COMPLETIONS", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "STREAMING_EXTERNAL_COMMAND", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.22.4"}], "parameters": {"groupId": "STREAMING_EXTERNAL_COMMAND", "rollout": "100", "stickiness": "random"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "STREAMING_EXTERNAL_COMMAND", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "STREAM_USER_SHELL_COMMANDS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "STREAM_USER_SHELL_COMMANDS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "k_is_5", "weight": 1000, "payload": {"type": "string", "value": "5"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.4.3"}], "parameters": {"groupId": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "k_is_5", "weight": 1000, "payload": {"type": "string", "value": "5"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.107"}], "parameters": {"groupId": "SUPERCOMPLETE_CODE_DIAGNOSTICS_TOP_K", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "k_is_5", "weight": 1000, "payload": {"type": "string", "value": "5"}, "stickiness": "random"}]}], "variants": []}, {"name": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "all_diagnostics", "weight": 1000, "payload": {"type": "string", "value": "4"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.31.20"}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "all_diagnostics", "weight": 1000, "payload": {"type": "number", "value": "4"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.4.3"}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "all_diagnostics", "weight": 1000, "payload": {"type": "number", "value": "4"}, "stickiness": "random"}, {"name": "error_only", "weight": 0, "payload": {"type": "number", "value": "0"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.107"}], "parameters": {"groupId": "SUPERCOMPLETE_DIAGNOSTIC_SEVERITY_THRESHOLD", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "all_diagnostics", "weight": 1000, "payload": {"type": "number", "value": "4"}, "stickiness": "random"}, {"name": "error_only", "weight": 0, "payload": {"type": "number", "value": "0"}, "stickiness": "random"}]}], "variants": []}, {"name": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.32.1"}], "parameters": {"groupId": "SUPERCOMPLETE_DISABLE_TYPING_CACHE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_DONT_FILTER_MID_STREAMED", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_DONT_FILTER_MID_STREAMED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FAST_DEBOUNCE", "type": "release", "description": "Extension side debounce for supercomplete", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_FAST_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "none", "weight": 1000, "payload": {"type": "number", "value": "0"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf"]}], "parameters": {"groupId": "SUPERCOMPLETE_FAST_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "verysmall", "weight": 1000, "payload": {"type": "string", "value": "20"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_DELETION_CAP", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_DELETION_CAP", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_INSERTION_CAP", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_INSERTION_CAP", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_NO_OP", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_NO_OP", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_PREFIX_MATCH", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_PREFIX_MATCH", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_PREVIOUSLY_SHOWN", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_PREVIOUSLY_SHOWN", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_REVERT", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_REVERT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_REVERT_AUTOCOMPLETE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next", "windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_REVERT_AUTOCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_SCORE_THRESHOLD", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_SCORE_THRESHOLD", "rollout": "0", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_FILTER_WHITESPACE_ONLY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.30.0"}], "parameters": {"groupId": "SUPERCOMPLETE_FILTER_WHITESPACE_ONLY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_INLINE_PURE_DELETE", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "SUPERCOMPLETE_INLINE_PURE_DELETE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_INLINE_RICH_GHOST_TEXT_INSERTIONS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_INLINE_RICH_GHOST_TEXT_INSERTIONS", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_LINE_RADIUS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "small_for_tab_jump", "weight": 1000, "payload": {"type": "number", "value": "5"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_LINE_RADIUS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "large", "weight": 0, "payload": {"type": "number", "value": "7"}, "stickiness": "userId"}, {"name": "medium", "weight": 1000, "payload": {"type": "number", "value": "5"}, "stickiness": "userId"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "3"}, "stickiness": "userId"}, {"name": "xl", "weight": 0, "payload": {"type": "number", "value": "15"}, "stickiness": "userId"}]}], "variants": []}, {"name": "SUPERCOMPLETE_MAX_CONCURRENT_REQUESTS", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_MAX_CONCURRENT_REQUESTS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4", "weight": 1000, "payload": {"type": "number", "value": "1"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_MAX_DELETIONS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MAX_DELETIONS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "md", "weight": 1000, "payload": {"type": "number", "value": "10"}, "stickiness": "default"}, {"name": "xl", "weight": 0, "payload": {"type": "number", "value": "30"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_MAX_INSERTIONS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MAX_INSERTIONS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "large", "weight": 0, "payload": {"type": "number", "value": "10"}, "stickiness": "default"}, {"name": "medium", "weight": 0, "payload": {"type": "number", "value": "5"}, "stickiness": "default"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "3"}, "stickiness": "default"}, {"name": "xlarge", "weight": 1000, "payload": {"type": "number", "value": "30"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.30.0"}], "parameters": {"groupId": "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "10_step", "weight": 0, "payload": {"type": "number", "value": "10"}, "stickiness": "userId"}, {"name": "15_step", "weight": 0, "payload": {"type": "number", "value": "15"}, "stickiness": "userId"}, {"name": "7_steps", "weight": 1000, "payload": {"type": "number", "value": "7"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MAX_TRAJECTORY_STEPS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "extra-large", "weight": 0, "payload": {"type": "number", "value": "10"}, "stickiness": "userId"}, {"name": "large", "weight": 1000, "payload": {"type": "number", "value": "7"}, "stickiness": "userId"}, {"name": "medium", "weight": 0, "payload": {"type": "number", "value": "5"}, "stickiness": "userId"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "2"}, "stickiness": "userId"}]}], "variants": []}, {"name": "SUPERCOMPLETE_MAX_TRAJECTORY_STEP_SIZE", "type": "experiment", "enabled": false, "stale": false, "impressionData": true, "project": "default", "strategies": [], "variants": []}, {"name": "SUPERCOMPLETE_MIN_SCORE", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_MIN_SCORE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "heavy", "weight": 0, "payload": {"type": "number", "value": "-1"}, "stickiness": "default"}, {"name": "light", "weight": 1000, "payload": {"type": "number", "value": "-3"}, "stickiness": "default"}, {"name": "medium", "weight": 0, "payload": {"type": "number", "value": "-2"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_MODEL_CONFIG", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_19820", "weight": 500, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19820\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_23310", "weight": 500, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.7.101"}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19820", "weight": 500, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19820\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19821", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19822", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19822\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_23310", "weight": 500, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.107"}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19821", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.7.2"}, {"contextName": "planName", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["Enterprise", "Teams", "Teams Ultimate", "Hybrid"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_19820", "weight": 500, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19820\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19822", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19822\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_23310", "weight": 500, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23310\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.7.0"}, {"contextName": "planName", "operator": "NOT_IN", "caseInsensitive": false, "inverted": false, "values": ["Enterprise", "Teams", "Teams Ultimate", "Hybrid"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_19821", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19822", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19822\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.4.3"}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19821", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19821\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.32.2"}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-insiders", "windsurf-next"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.35.2"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15600", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_16801", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_16801\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_18805", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18805\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.29.2"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15600", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_16801", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_16801\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.25.6"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15600", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15729\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.25.1"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15600", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15600\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.21.8"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_13930", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_13930\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_14942", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14942\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "SUPERCOMPLETE_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "GPT_40_MINI", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_GPT_4O_MINI_2024_07_18\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_13930", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_13930\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_14942", "weight": 0, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_14942\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n\"model_name\":\"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}]}], "variants": []}, {"name": "SUPERCOMPLETE_NO_ACTIVE_NODE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_NO_ACTIVE_NODE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_NO_CONTEXT", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_NO_CONTEXT", "rollout": "0", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_ON_ACCEPT_ONLY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_ON_ACCEPT_ONLY", "rollout": "10", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_ON_TAB", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_ON_TAB", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_POST_APPLY_DECORATION", "type": "release", "description": "Highlight inserted text upon accepted completion", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_POST_APPLY_DECORATION", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_PRUNE_MAX_INSERT_DELETE_LINE_DELTA", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_PRUNE_MAX_INSERT_DELETE_LINE_DELTA", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "med", "weight": 0, "payload": {"type": "number", "value": "3"}, "stickiness": "default"}, {"name": "small", "weight": 1000, "payload": {"type": "number", "value": "1"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_PRUNE_RESPONSE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "SUPERCOMPLETE_RECENT_STEPS_DURATION", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.30.0"}], "parameters": {"groupId": "SUPERCOMPLETE_RECENT_STEPS_DURATION", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "extra-large", "weight": 0, "payload": {"type": "number", "value": "100"}, "stickiness": "userId"}, {"name": "large", "weight": 1000, "payload": {"type": "number", "value": "30"}, "stickiness": "userId"}, {"name": "medium", "weight": 0, "payload": {"type": "number", "value": "15"}, "stickiness": "userId"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "10"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.30.0"}], "parameters": {"groupId": "SUPERCOMPLETE_RECENT_STEPS_DURATION", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "5min", "weight": 0, "payload": {"type": "number", "value": "300"}, "stickiness": "userId"}, {"name": "60min", "weight": 1000, "payload": {"type": "number", "value": "3600"}, "stickiness": "userId"}]}], "variants": []}, {"name": "SUPERCOMPLETE_REGULAR_DEBOUNCE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "SUPERCOMPLETE_REGULAR_DEBOUNCE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "slow", "weight": 1000, "payload": {"type": "string", "value": "200"}, "stickiness": "default"}]}], "variants": []}, {"name": "SUPERCOMPLETE_TEMPERATURE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_TEMPERATURE", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "TEMP_001", "weight": 0, "payload": {"type": "number", "value": "0.01"}, "stickiness": "random"}, {"name": "TEMP_01", "weight": 1000, "payload": {"type": "number", "value": "0.1"}, "stickiness": "random"}, {"name": "TEMP_04", "weight": 0, "payload": {"type": "number", "value": "0.4"}, "stickiness": "random"}, {"name": "TEMP_07", "weight": 0, "payload": {"type": "number", "value": "0.7"}, "stickiness": "random"}]}], "variants": []}, {"name": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "rollout": "100", "stickiness": "random"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.4.3"}], "parameters": {"groupId": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "rollout": "100", "stickiness": "random"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.107"}], "parameters": {"groupId": "SUPERCOMPLETE_USE_CODE_DIAGNOSTICS", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "SUPERCOMPLETE_USE_INTELLISENSE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "SUPERCOMPLETE_USE_INTELLISENSE", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_ACCEPT_ENABLED", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "prereleaseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.26.4"}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.23.6"}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "TAB_JUMP_ACCEPT_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_AS_CACHED_SUPERCOMPLETE", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "TAB_JUMP_AS_CACHED_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "TAB_JUMP_AS_CACHED_SUPERCOMPLETE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_CUMULATIVE_PROMPT_CONFIG", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_JUMP_CUMULATIVE_PROMPT_CONFIG", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "8k_default", "weight": 1000, "payload": {"type": "json", "value": "{\"persistent_context_multiplier\": 0.25, \"persistent_active_document_multiplier\": 0.45, \"persistent_open_docs_multiplier\": 0.25, \"persistent_max_tokens_per_open_doc\": 2048, \"persistent_max_ccis_considered\": 25, \"trajectory_context_multiplier\": 0.5, \"trajectory_refresh_threshold_multiplier\": 0.9, \"trajectory_truncation_multiplier\": 0.5, \"ephemeral_context_multiplier\": 0.25, \"intent_reservation_tokens\": 512, \"ephemeral_active_document_multiplier\": 1.0, \"ephemeral_max_ccis_considered\": 0}"}, "stickiness": "default"}]}], "variants": []}, {"name": "TAB_JUMP_ENABLED", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.32.1"}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "prereleaseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.26.4"}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.23.6"}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "TAB_JUMP_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_FILTER_IN_SELECTION", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "TAB_JUMP_FILTER_IN_SELECTION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_LINE_RADIUS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "large", "weight": 1000, "payload": {"type": "number", "value": "60"}, "stickiness": "userId"}, {"name": "medium", "weight": 0, "payload": {"type": "number", "value": "40"}, "stickiness": "userId"}, {"name": "medium-large", "weight": 0, "payload": {"type": "number", "value": "50"}, "stickiness": "userId"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "30"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "large", "weight": 1000, "payload": {"type": "number", "value": "60"}, "stickiness": "default"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "30"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "large", "weight": 1000, "payload": {"type": "number", "value": "60"}, "stickiness": "default"}, {"name": "small", "weight": 0, "payload": {"type": "number", "value": "30"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_JUMP_LINE_RADIUS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "small", "weight": 1000, "payload": {"type": "number", "value": "30"}, "stickiness": "default"}]}], "variants": []}, {"name": "TAB_JUMP_MIN_FILTER_RADIUS", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_JUMP_MIN_FILTER_RADIUS", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "large", "weight": 0, "payload": {"type": "number", "value": "7"}, "stickiness": "userId"}, {"name": "medium", "weight": 0, "payload": {"type": "number", "value": "5"}, "stickiness": "userId"}, {"name": "small", "weight": 1000, "payload": {"type": "number", "value": "2"}, "stickiness": "userId"}, {"name": "xl", "weight": 0, "payload": {"type": "number", "value": "0"}, "stickiness": "userId"}]}], "variants": []}, {"name": "TAB_JUMP_MODEL_CONFIG", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.6"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "model_chat_20706", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.6.113"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_20706", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_21779", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_21779\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_22798", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_22798\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.5.107"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_19484", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19484\"\n}"}, "stickiness": "default"}, {"name": "MODEL_CHAT_20706", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "default"}, {"name": "MODEL_CHAT_18468", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18468\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "sessionId"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_18468", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18468\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_19484", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_19484\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_20706", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_20706\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_21779", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_21779\"\n}"}, "stickiness": "sessionId"}, {"name": "MODEL_CHAT_22798", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_22798\"\n}"}, "stickiness": "sessionId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 0, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_18468", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_18468\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.33.2"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.29.2"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.25.6"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.25.1"}], "parameters": {"groupId": "TAB_JUMP_MODEL_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15305", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_15305\"\n}"}, "stickiness": "random"}]}], "variants": []}, {"name": "TAB_JUMP_ON_ACCEPT_ONLY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.40.2"}], "parameters": {"groupId": "TAB_JUMP_ON_ACCEPT_ONLY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_PRINT_LINE_RANGE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "TAB_JUMP_PRINT_LINE_RANGE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "TAB_JUMP_PRINT_LINE_RANGE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_PRUNE_RESPONSE", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.32.1"}], "parameters": {"groupId": "TAB_JUMP_PRUNE_RESPONSE", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "TAB_JUMP_PRUNE_RESPONSE", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_JUMP_STOP_TOKEN_MIDSTREAM", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.1"}], "parameters": {"groupId": "TAB_JUMP_STOP_TOKEN_MIDSTREAM", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.115"}], "parameters": {"groupId": "TAB_JUMP_STOP_TOKEN_MIDSTREAM", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "TAB_REPORTING_KILL_SWITCH", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "TAB_REPORTING_KILL_SWITCH", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "USE_ANTHROPIC_TOKEN_EFFICIENT_TOOLS_BETA", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_ANTHROPIC_TOKEN_EFFICIENT_TOOLS_BETA", "rollout": "10", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_ATTRIBUTION_FOR_INDIVIDUAL_TIER", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_ATTRIBUTION_FOR_INDIVIDUAL_TIER", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "USE_AUTOCOMPLETE_MODEL", "type": "operational", "description": "", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "prereleaseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_14602", "weight": 0, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_14602\"}"}, "stickiness": "random"}, {"name": "MODEL_15133", "weight": 0, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "stickiness": "random"}, {"name": "MODEL_8341", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_8341\"}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_14602", "weight": 0, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_14602\"}"}, "stickiness": "random"}, {"name": "MODEL_15133", "weight": 500, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "stickiness": "random"}, {"name": "MODEL_8341", "weight": 500, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_8341\"}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_14602", "weight": 0, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_14602\"}"}, "stickiness": "random"}, {"name": "MODEL_8341", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_8341\"}"}, "stickiness": "random"}]}], "variants": [{"name": "MODEL_8341", "weight": 1000, "weightType": "variable", "stickiness": "random", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_8341\"}"}, "overrides": []}]}, {"name": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "type": "experiment", "description": "Autocomplete model used by server side model reselection.", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_CHAT_15729", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_CHAT_15729\"}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.32.2"}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_15133", "weight": 0, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_CHAT_15729\"}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next", "windsurf-insiders"]}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_15133", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.35.2"}, {"contextName": "prereleaseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_15133", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "stickiness": "random"}, {"name": "MODEL_CHAT_15729", "weight": 0, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_CHAT_15729\"}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_AUTOCOMPLETE_MODEL_SERVER_SIDE", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "MODEL_15133", "weight": 1000, "payload": {"type": "json", "value": "{\"model_name\": \"MODEL_15133\"}"}, "stickiness": "random"}]}], "variants": []}, {"name": "USE_CHAT_INSTRUCT_COMPLETION", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["false"]}], "parameters": {"groupId": "USE_CHAT_INSTRUCT_COMPLETION", "rollout": "50", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_COMMAND_DOCSTRING_GENERATION", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_COMMAND_DOCSTRING_GENERATION", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_CONTEXT_TOKEN", "type": "release", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.15"}], "parameters": {"groupId": "USE_CONTEXT_TOKEN", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_CUSTOM_CHARACTER_DIFF", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "USE_CUSTOM_CHARACTER_DIFF", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "USE_CUSTOM_CHARACTER_DIFF", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "USE_CUSTOM_CHARACTER_DIFF", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_GCP_API_SERVER_FOR_PREMIUM_CHAT", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_GCP_API_SERVER_FOR_PREMIUM_CHAT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_GPT_4_COMMAND", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["false"]}], "parameters": {"groupId": "USE_GPT_4_COMMAND", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "USE_GPT_4_TURBO", "type": "release", "description": "Switches GPT4 to GPT4-turbo", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [], "variants": []}, {"name": "USE_IMPLICIT_TRAJECTORY", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.12.1"}, {"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.14.15"}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "USE_IMPLICIT_TRAJECTORY", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_INFERENCE_API_SERVER", "type": "operational", "description": "PLEASE UPDATE https://exafunction.pagerduty.com/rules/rulesets/_default IF YOU TURN THIS OFF/ON", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_INFERENCE_API_SERVER", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "USE_INTERNAL_CHAT_MODEL", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.3.104"}, {"contextName": "teamsMode", "operator": "STR_CONTAINS", "caseInsensitive": true, "inverted": false, "values": ["false"]}], "parameters": {"groupId": "USE_INTERNAL_CHAT_MODEL", "rollout": "50", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_MODEL_8341", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_MODEL_8341", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "USE_MODEL_8684", "type": "release", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_MODEL_8684", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_MQUERY_SCORER", "type": "operational", "description": "", "enabled": false, "stale": true, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.8.53"}], "parameters": {"groupId": "USE_MQUERY_SCORER", "rollout": "15", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "USE_MULTILINE_MODEL", "type": "release", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_MULTILINE_MODEL", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_OPENAI_INTERFACE_CLIENT", "type": "release", "description": "Use openai v2 client", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_OPENAI_INTERFACE_CLIENT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_OPENAI_OFFICIAL_CLIENT", "type": "experiment", "description": "Use openai_client_official.go rather than openai_client_v2 for all requests", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_OPENAI_OFFICIAL_CLIENT", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "USE_QUANTIZED_FAISS_INDEX", "type": "experiment", "description": "Enables the quantized FAISS index for local indexing", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "default", "constraints": [], "parameters": {}, "variants": []}], "variants": []}, {"name": "USE_REMOTE_EMBEDDING", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "USE_REMOTE_EMBEDDING", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "USE_SPECIAL_EDIT_CODE_BLOCK", "type": "experiment", "description": "Whether a chat model should use a special \"edit\" code block to signify edits that can be fast applied.", "enabled": false, "stale": false, "impressionData": true, "project": "default", "strategies": [], "variants": []}, {"name": "USE_SUPERCOMPLETE_MODEL", "type": "experiment", "description": "", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.8.12"}], "parameters": {"groupId": "USE_SUPERCOMPLETE_MODEL", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": [{"name": "MODEL_CHAT_11121", "weight": 1000, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_CHAT_10546\"}"}, "overrides": []}, {"name": "MODEL_CHAT_3_5_TURBO", "weight": 0, "weightType": "fix", "stickiness": "random", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_CHAT_3_5_TURBO\"}"}, "overrides": []}, {"name": "UNSPECIFIED", "weight": 0, "weightType": "variable", "stickiness": "random", "payload": {"type": "json", "value": "{\"model_name\":\"MODEL_UNSPECIFIED\"}"}, "overrides": []}]}, {"name": "VIEWED_FILE_TRACKER_CONFIG", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "0_steps", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 0\n}"}, "stickiness": "default"}, {"name": "2_steps", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 2\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "0_steps", "weight": 0, "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 0\n}"}, "stickiness": "default"}, {"name": "2_steps", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 2\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "2_steps", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"max_steps_per_checkpoint\": 2\n}"}, "stickiness": "random"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.40.2"}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode"]}], "parameters": {"groupId": "VIEWED_FILE_TRACKER_CONFIG", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "2_steps_0_included", "weight": 475, "payload": {"type": "json", "value": "{   \n  \"max_steps_per_checkpoint\": 2, \n  \"max_files_in_prompt\": 0,   \n  \"max_lines_per_file_in_prompt\": 0\n}"}, "stickiness": "random"}, {"name": "2_steps_2_included", "weight": 475, "payload": {"type": "json", "value": "{   \n  \"max_steps_per_checkpoint\": 2, \n  \"max_files_in_prompt\": 2,   \n  \"max_lines_per_file_in_prompt\": 100 \n}"}, "stickiness": "random"}, {"name": "2_steps_4_included", "weight": 50, "payload": {"type": "json", "value": "{   \n  \"max_steps_per_checkpoint\": 2, \n  \"max_files_in_prompt\": 4,   \n  \"max_lines_per_file_in_prompt\": 100 \n}"}, "stickiness": "random"}]}], "variants": []}, {"name": "VSCODE_USE_GRPC_PROTOCOL", "type": "experiment", "description": "", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "VSCODE_USE_GRPC_PROTOCOL", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "WAVE_8_KNOWLEDGE_ENABLED", "type": "release", "description": "This flag gates whether or not the Knowledge category is visible in the @ mention menu.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["jetbrains"]}], "parameters": {"groupId": "WAVE_8_KNOWLEDGE_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf", "windsurf-next"]}], "parameters": {"groupId": "WAVE_8_KNOWLEDGE_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "WAVE_8_RULES_ENABLED", "type": "release", "description": "This flag gates the backend for rules and whether or not the Rules category is visible in the @ mention menu. Everything is default off and is only enabled if the user has the dev extension or this flag is enabled.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "WAVE_8_RULES_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf", "windsurf-dev"]}], "parameters": {"groupId": "WAVE_8_RULES_ENABLED", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "WINDSURF_SENTRY_SAMPLE_RATE", "type": "release", "description": "Unleash flag to specify Sentry sample rate for Windsurf extension; Float in range [0.0, 1.0]", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "WINDSURF_SENTRY_SAMPLE_RATE", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "sample_rate", "weight": 1000, "payload": {"type": "number", "value": "0.01"}, "stickiness": "default"}]}], "variants": []}, {"name": "XML_TOOL_PARSING_MODELS", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.5.9"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_O3_MINI", "MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "XML_TOOL_PARSING_MODELS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "xmlmodels", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"models\": [\n      \"MODEL_CHAT_O3_MINI\",\n      \"MODEL_GOOGLE_GEMINI_2_5_PRO\"\n  ]\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_XAI_GROK_3", "MODEL_XAI_GROK_3_MINI_REASONING"]}], "parameters": {"groupId": "XML_TOOL_PARSING_MODELS", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "grok3", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"models\": [\n    \"MODEL_XAI_GROK_3\",\n    \"MODEL_XAI_GROK_3_MINI_REASONING\"\n  ]\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "api-provider-routing-config", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "api-provider-routing-config", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_map\": {\n    \"MODEL_CLAUDE_3_5_SONNET_20241022\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    },\n    \"MODEL_CLAUDE_3_7_SONNET_20250219\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC\": {\n          \"weight\": 0,\n          \"cache_ttl_minutes\": 0\n        },\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 100,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    }\n  }\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "devMode", "operator": "IN", "caseInsensitive": false, "inverted": true, "values": ["true"]}], "parameters": {"groupId": "api-provider-routing-config", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_map\": {\n    \"MODEL_CLAUDE_3_5_SONNET_20241022\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 1,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    },\n    \"MODEL_CLAUDE_3_7_SONNET_20250219\": {\n      \"provider_map\": {\n        \"API_PROVIDER_ANTHROPIC\": {\n          \"weight\": 392,\n          \"cache_ttl_minutes\": 5\n        },\n        \"API_PROVIDER_ANTHROPIC_BEDROCK\": {\n          \"weight\": 0,\n          \"cache_ttl_minutes\": 5\n        }\n      }\n    }\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-additional-instructions-section-content", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-additional-instructions-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 334, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_UNSPECIFIED\", \"content\": \"\" \n}"}, "stickiness": "default"}, {"name": "gpt4.1_devin", "weight": 333, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"Do not guess, generalize, or infer behavior from names, comments, or patterns. Every claim must be supported by code you have examined. Use your research tools to thoroughly investigate and directly inspect relevant code before responding. Prioritize accuracy and completeness over speed; take the time to fully understand the codebase before making recommendations or changes. Continue researching until you have complete, code-backed confidence, and clearly state if you cannot find direct evidence.\" \n}"}, "stickiness": "default"}, {"name": "gpt4.1_julian", "weight": 333, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved. Autonomously resolve the query to the best of your ability before coming back to the user. \\n\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer. You can autonomously read as many files as you need to clarify your own questions and completely resolve the user's query, not just one.\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-additional-instructions-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 334, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_UNSPECIFIED\", \"content\": \"\" \n}"}, "stickiness": "default"}, {"name": "gpt4.1_devin", "weight": 333, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"Do not guess, generalize, or infer behavior from names, comments, or patterns. Every claim must be supported by code you have examined. Use your research tools to thoroughly investigate and directly inspect relevant code before responding. Prioritize accuracy and completeness over speed; take the time to fully understand the codebase before making recommendations or changes. Continue researching until you have complete, code-backed confidence, and clearly state if you cannot find direct evidence.\" \n}"}, "stickiness": "default"}, {"name": "gpt4.1_julian", "weight": 333, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved. Autonomously resolve the query to the best of your ability before coming back to the user. \\n\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer. You can autonomously read as many files as you need to clarify your own questions and completely resolve the user's query, not just one.\" }"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf", "windsurf-insiders"]}], "parameters": {"groupId": "cascade-additional-instructions-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 0, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_UNSPECIFIED\", \"content\": \"\" \n}"}, "stickiness": "default"}, {"name": "gpt4.1", "weight": 1000, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"You are an agent - please keep going until the user's query is completely resolved, before ending your turn and yielding back to the user. Only terminate your turn when you are sure that the problem is solved.\\nIf you are not sure about file content or codebase structure pertaining to the user's request, use your tools to read files and gather the relevant information: do NOT guess or make up an answer.\\nYou MUST plan extensively before each function call, and reflect extensively on the outcomes of the previous function calls. DO NOT do this entire process by making function calls only, as this can impair your ability to solve the problem and think insightfully.\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-api-server-experiment-keys", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-api-server-experiment-keys", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "all", "weight": 1000, "payload": {"type": "string", "value": "XML_TOOL_PARSING_MODELS"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-code-changes-section-content", "type": "experiment", "description": "Override of the making_code_changes portion of the system prompt", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-code-changes-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 1000, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_APPEND\", \"content\": \"IMPORTANT: When using any code edit tool, such as replace_file_content, ALWAYS generate the TargetFile argument first.\" \n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-code-changes-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 1000, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_APPEND\", \"content\": \"IMPORTANT: When using any code edit tool, such as replace_file_content, ALWAYS generate the TargetFile argument first.\" \n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-code-research-section-content", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-code-research-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1_research", "weight": 500, "payload": {"type": "string", "value": "If you are not sure about file content or codebase structure pertaining to the user's request, proactively use your tools to search the codebase, read files and gather relevant information: NEVER guess or make up an answer. Your answer must be rooted in your research, so be thorough in your understanding of the code before answering or making code edits.\\nYou do not need to ask user permission to research the codebase; proactively call research tools when needed."}, "stickiness": "default"}, {"name": "gpt4o_alpha", "weight": 500, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-code-research-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 500, "stickiness": "default"}, {"name": "gpt4.1_research", "weight": 500, "payload": {"type": "string", "value": "If you are not sure about file content or codebase structure pertaining to the user's request, proactively use your tools to search the codebase, read files and gather relevant information: NEVER guess or make up an answer. Your answer must be rooted in your research, so be thorough in your understanding of the code before answering or making code edits.\\nYou do not need to ask user permission to research the codebase; proactively call research tools when needed."}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-command-status-tool-config-override", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-command-status-tool-config-override", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "delta", "weight": 1000, "payload": {"type": "json", "value": "{ \"use_delta\": true }"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-communication-section-content", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-communication-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1_max_proactive", "weight": 330, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. As an agent you should autonomously and proactively solve the user’s task. When the next steps are clear and obvious, proactively use your tools to execute them without waiting for user guidance. Ask for user guidance only when there is ambiguity as to what the next steps should be. \\n4. If you have made mistakes or broken code in your previous actions, you must correct them before proceeding; there is no need to ask for user permission when correcting your mistakes.\"\n}"}, "stickiness": "default"}, {"name": "gpt4.1_more_proactive", "weight": 330, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. Ask for user guidance only when there is ambiguity as to what the next steps should be. When the next steps are clear and obvious, proactively use your tools to execute them.\\n4. If you have made mistakes or broken code in your previous actions, proactively correct them; there is no need to ask for user permission.\"\n}"}, "stickiness": "default"}, {"name": "gpt4o_alpha", "weight": 340, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n 2. Format your responses in markdown. Use backticks to format file, directory, function, and class names. If providing a URL to the user, format this in markdown as well.\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-communication-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 340, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, and class names. If providing a URL to the user, format this in markdown as well.\"\n}"}, "stickiness": "default"}, {"name": "gpt4.1_max_proactive", "weight": 330, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. As an agent you should autonomously and proactively solve the user’s task. When the next steps are clear and obvious, proactively use your tools to execute them without waiting for user guidance. Ask for user guidance only when there is ambiguity as to what the next steps should be. \\n4. If you have made mistakes or broken code in your previous actions, you must correct them before proceeding; there is no need to ask for user permission when correcting your mistakes.\"\n}"}, "stickiness": "default"}, {"name": "gpt4.1_more_proactive", "weight": 330, "payload": {"type": "json", "value": "{ \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\", \"content\": \"1. Refer to the USER in the second person and yourself in the first person.\\n2. Format your responses in markdown. Use backticks to format file, directory, function, class names, as well as tables. If providing a URL, format it as a markdown link.\\n3. Ask for user guidance only when there is ambiguity as to what the next steps should be. When the next steps are clear and obvious, proactively use your tools to execute them.\\n4. If you have made mistakes or broken code in your previous actions, proactively correct them; there is no need to ask for user permission.\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-enable-find-all-references", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-enable-find-all-references", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "cascade-enable-go-to-definition", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "cascade-enable-go-to-definition", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "cascade-enable-search-in-file-tool", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "cascade-grep-tool-config-override", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "cascade-grep-tool-config-override", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{ \n  \"include_cci_in_result\": true,\n  \"num_full_source_ccis\": 5,\n  \"max_bytes_per_cci\": 2048\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{ \n  \"include_cci_in_result\": true,\n  \"num_full_source_ccis\": 5,\n  \"max_bytes_per_cci\": 2048\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "jetbrains"]}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "50", "stickiness": "default"}, "variants": [{"name": "with_cci", "weight": 1000, "payload": {"type": "json", "value": "{ \n  \"include_cci_in_result\": true,\n  \"num_full_source_ccis\": 5,\n  \"max_bytes_per_cci\": 2048\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-group-planner-response-tools", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "cascade-group-planner-response-tools", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.8.0"}, {"contextName": "requestedModelId", "operator": "STR_CONTAINS", "caseInsensitive": false, "inverted": true, "values": ["MODEL_CLAUDE"]}], "parameters": {"groupId": "cascade-group-planner-response-tools", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "cascade-include-ephemeral-message", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 500, "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "stickiness": "default"}, {"name": "heuristics_on", "weight": 500, "payload": {"type": "json", "value": "{\n  \"enabled\": true,\n  \"num_steps\": 10,\n  \"heuristic_prompts\": [\n    {\n        \"heuristic\": \"incremental-view-file\",\n        \"prompt\": \"You are scanning through a file using the view_file tool, which is slow and expensive. Instead, use a more efficient research tool to find what you are looking for.\"\n    },\n    {\n        \"heuristic\": \"repeat-file-edits\",\n        \"prompt\": \"You have edited the same file multiple times in consecutive tool calls. This is inefficient. If you have any remaining edits, make them all in a single tool call.\"\n    },\n    {\n        \"heuristic\": \"repeat-same-edits\",\n        \"prompt\": \"You have repeatedly proposed the same diff and gotten the same result. Stop calling the tool and just show the diff directly to the user instead.\"\n    }\n  ]\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CLAUDE_3_7_SONNET_20250219", "MODEL_CLAUDE_3_7_SONNET_20250219_THINKING", "MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "base-enabled", "weight": 850, "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "stickiness": "default"}, {"name": "heuristics", "weight": 150, "payload": {"type": "json", "value": "{\n  \"enabled\": true,\n  \"num_steps\": 10,\n  \"heuristic_prompts\": [\n    {\n        \"heuristic\": \"incremental-view-file\",\n        \"prompt\": \"You are scanning through a file using the view_file tool, which is slow and expensive. Instead, use a more efficient research tool to find what you are looking for.\"\n    },\n    {\n        \"heuristic\": \"repeat-file-edits\",\n        \"prompt\": \"You have edited the same file multiple times in consecutive tool calls. This is inefficient. If you have any remaining edits, make them all in a single tool call.\"\n    },\n    {\n        \"heuristic\": \"repeat-same-edits\",\n        \"prompt\": \"You have repeatedly proposed the same diff and gotten the same result. Stop calling the tool and just show the diff directly to the user instead.\"\n    }\n  ]\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeModelAlias", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_ALIAS_SWE_1"]}], "parameters": {"groupId": "cascade-include-ephemeral-message", "rollout": "50", "stickiness": "default"}, "variants": [{"name": "enabled", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"enabled\": true\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-tool-calling-section-content", "type": "experiment", "description": "Replace the default tool calling section of the system prompt with a custom prompt", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-tool-calling-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 500, "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_PREPEND\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user.\"\n}"}, "stickiness": "default"}, {"name": "gpt4.1_use_tools", "weight": 500, "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user. Follow these rules: \\n1. IMPORTANT: If you state that you will use a tool, immediately call that tool as your next action.\\n2. Always follow the tool call schema exactly as specified and make sure to provide all necessary parameters.\\n3. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided in your system prompt. \\n4. Before calling each tool, first explain why you are calling it.\\n5. Some tools run asynchronously, so you may not see their output immediately. If you need to see the output of previous tool calls before continuing, simply stop making new tool calls.\\n\\nHere are examples of good tool call behavior:\\n<example>\\nUSER: What is int64?\\nASSISTANT: [No tool calls, since the query is general] int64 is a 64-bit signed integer.\\n</example>\\n<example>\\nUSER: What does function foo do?\\nASSISTANT: Let me find foo and view its contents. [Call grep_search to find instances of the phrase 'foo']\\nTOOL: [result: foo is found on line 7 of bar.py]\\nASSISTANT: [Call view_code_item to see the contents of bar.foo]\\nTOOL: [result: contents of bar.foo]\\nASSISTANT: foo does the following ...\\n</example>\\n<example>\\nUSER: Add a new func baz to qux.py\\nASSISTANT: Let's find qux.py and see where to add baz. [Call find_by_name to see if qux.py exists]\\nTOOL: [result: a valid path to qux.py]\\nASSISTANT: [Call view_file to see the contents of qux.py]\\nTOOL: [result: contents of qux.py]\\nASSISTANT: [Call a code edit tool to write baz to qux.py]\\n</example>\"\n}"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "cascade-tool-calling-section-content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4.1", "weight": 500, "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_PREPEND\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user.\"\n}"}, "stickiness": "default"}, {"name": "gpt4.1_use_tools", "weight": 500, "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_OVERRIDE\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user. Follow these rules: \\n1. IMPORTANT: If you state that you will use a tool, immediately call that tool as your next action.\\n2. Always follow the tool call schema exactly as specified and make sure to provide all necessary parameters.\\n3. The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided in your system prompt. \\n4. Before calling each tool, first explain why you are calling it.\\n5. Some tools run asynchronously, so you may not see their output immediately. If you need to see the output of previous tool calls before continuing, simply stop making new tool calls.\\n\\nHere are examples of good tool call behavior:\\n<example>\\nUSER: What is int64?\\nASSISTANT: [No tool calls, since the query is general] int64 is a 64-bit signed integer.\\n</example>\\n<example>\\nUSER: What does function foo do?\\nASSISTANT: Let me find foo and view its contents. [Call grep_search to find instances of the phrase 'foo']\\nTOOL: [result: foo is found on line 7 of bar.py]\\nASSISTANT: [Call view_code_item to see the contents of bar.foo]\\nTOOL: [result: contents of bar.foo]\\nASSISTANT: foo does the following ...\\n</example>\\n<example>\\nUSER: Add a new func baz to qux.py\\nASSISTANT: Let's find qux.py and see where to add baz. [Call find_by_name to see if qux.py exists]\\nTOOL: [result: a valid path to qux.py]\\nASSISTANT: [Call view_file to see the contents of qux.py]\\nTOOL: [result: contents of qux.py]\\nASSISTANT: [Call a code edit tool to write baz to qux.py]\\n</example>\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "cascade-view-code-item-tool-config-override", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next"]}], "parameters": {"groupId": "cascade-research-03-26", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "multi", "weight": 500, "payload": {"type": "json", "value": "{\n  \"max_num_items\": 5\n}\n"}, "stickiness": "default"}, {"name": "single", "weight": 500, "payload": {"type": "json", "value": "{\n  \"max_num_items\": 1\n}\n"}, "stickiness": "default"}]}], "variants": []}, {"name": "chat-request-id", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_GT", "caseInsensitive": false, "inverted": false, "values": [], "value": "1.6.0"}], "parameters": {"groupId": "chat-request-id", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "chat-request-id", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "commit-message-gen-with-user-memories", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "commit-message-gen-with-user-memories", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "component-sharing-enabled", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "component-sharing-enabled", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "dsv-alt", "type": "release", "enabled": false, "stale": true, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "dsv-alt", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "force-old-split-prompt", "type": "experiment", "enabled": true, "stale": false, "impressionData": true, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_19821", "MODEL_CHAT_20706", "MODEL_CHAT_21779"]}], "parameters": {"groupId": "force-old-split-prompt", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "gemini-25-pro-vertex", "type": "release", "description": "whether to use the vertex client instead of the openai-compatible one", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "gemini-25-pro-vertex", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "cascadeTeamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "gemini-25-pro-vertex", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "gemini-25-pro-vertex", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "gemini-25-pro-vertex", "rollout": "0", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_GOOGLE_GEMINI_2_5_PRO"]}], "parameters": {"groupId": "gemini-25-pro-vertex", "rollout": "0", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "implicit-include-running", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "implicit-include-running", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "implicit-uses-intentional-reject", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "implicit-uses-intentional-reject", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "implicit-uses-lint-diff", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "implicit-uses-lint-diff", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "implicit-uses-user-grep", "type": "release", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders", "windsurf-next", "windsurf"]}], "parameters": {"groupId": "implicit-uses-user-grep", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "inference-server-name", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "inference-server-name", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "sv7", "weight": 1000, "payload": {"type": "string", "value": "https://inference.codeium.com"}, "stickiness": "default"}]}], "variants": []}, {"name": "inference-server-url", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode", "jetbrains"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.35.2"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_8341", "MODEL_15133"]}, {"contextName": "teamsMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "crusoe_sc", "weight": 0, "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "stickiness": "userId"}, {"name": "crusoe_sc_lb", "weight": 0, "payload": {"type": "string", "value": "https://southcentral-lb.codeium.com"}, "stickiness": "userId"}, {"name": "sv7", "weight": 1000, "payload": {"type": "string", "value": "https://inference.codeium.com"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["vscode", "jetbrains"]}, {"contextName": "extensionVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.35.2"}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_8341", "MODEL_15133"]}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "crusoe_sc", "weight": 0, "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "stickiness": "userId"}, {"name": "crusoe_sc_lb", "weight": 0, "payload": {"type": "string", "value": "https://southcentral-lb.codeium.com"}, "stickiness": "userId"}, {"name": "sv7", "weight": 1000, "payload": {"type": "string", "value": "https://inference.codeium.com"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_23310", "MODEL_CHAT_19822", "MODEL_CHAT_19820"]}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "crusoe_sc", "weight": 0, "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "stickiness": "default"}, {"name": "crusoe_sc_lb", "weight": 1000, "payload": {"type": "string", "value": "https://southcentral-lb.codeium.com"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_CHAT_19821"]}, {"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf"]}, {"contextName": "ideVersion", "operator": "SEMVER_LT", "caseInsensitive": false, "inverted": true, "values": [], "value": "1.7.0"}], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "crusoe_sc", "weight": 0, "payload": {"type": "string", "value": "https://southcentral.codeium.com:30099"}, "stickiness": "userId"}, {"name": "sv7", "weight": 1000, "payload": {"type": "string", "value": "https://inference.codeium.com"}, "stickiness": "userId"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "inference-server-url", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gcp", "weight": 0, "payload": {"type": "string", "value": "https://server.codeium.com"}, "stickiness": "default"}, {"name": "sv7", "weight": 1000, "payload": {"type": "string", "value": "https://inference.codeium.com"}, "stickiness": "default"}]}], "variants": []}, {"name": "inference-server-url-prefix", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "inference-server-url-prefix", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "inference", "weight": 1000, "payload": {"type": "string", "value": "inference"}, "stickiness": "default"}, {"name": "server", "weight": 0, "payload": {"type": "string", "value": "server"}, "stickiness": "default"}]}], "variants": []}, {"name": "internal-content-filter", "type": "operational", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "internal-content-filter", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "jwt-refresh-interval", "type": "release", "description": "How frequent (in minutes) to refresh the JWT from the language server", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "jwt-refresh-interval", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "<PERSON><PERSON><PERSON>", "weight": 1000, "payload": {"type": "number", "value": "5"}, "stickiness": "default"}]}], "variants": []}, {"name": "min-required-lint-duration", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "min-required-lint-duration", "rollout": "100", "stickiness": "userId"}, "variants": [{"name": "min-required-lint-duration", "weight": 1000, "payload": {"type": "number", "value": "3000"}, "stickiness": "userId"}]}], "variants": []}, {"name": "model-req-202503250000", "type": "release", "description": "See <PERSON> for details on this experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "model-req-202503250000", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "default", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"configs\": {\n    \"1\": {\n      \"probability\": 1,\n      \"trial_only\": false\n    },\n    \"2\": {\n      \"probability\": 1,\n      \"trial_only\": false\n    },\n    \"3\": {\n      \"probability\": 0,\n      \"trial_only\": true\n    }\n  }\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "mrr-kill-switch", "type": "kill-switch", "enabled": false, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "mrr-kill-switch", "rollout": "50", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "plus-address-filter", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "plus-address-filter", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "prompt-disabled-step-types", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "prompt-disabled-step-types", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "prompt-disabled-step-types", "weight": 1000, "payload": {"type": "json", "value": "[\"CORTEX_STEP_TYPE_LINT_DIFF\"]"}, "stickiness": "default"}]}], "variants": []}, {"name": "shamu-model-id", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "shamu-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20070", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}]}], "variants": []}, {"name": "supercomplete-prompt-include-intellisense", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "supercomplete-prompt-include-intellisense", "rollout": "100", "stickiness": "userId"}, "variants": []}], "variants": []}, {"name": "swe-1-lite-model-id", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20068", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf"]}, {"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20068", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf", "windsurf-next"]}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20068", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["jetbrains"]}], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20068", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CASCADE_20068"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "swe-1-lite-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "4O_mini", "weight": 1000, "payload": {"type": "string", "value": "MODEL_CHAT_GPT_4O_MINI_2024_07_18"}, "stickiness": "default"}, {"name": "HAIKU_40K", "weight": 0, "payload": {"type": "string", "value": "MODEL_CLAUDE_3_5_HAIKU_20241022"}, "stickiness": "default"}]}], "variants": []}, {"name": "swe-1-model-id", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20070", "weight": 0, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20071", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20072", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-next", "windsurf"]}, {"contextName": "cascadeEnterpriseMode", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["true"]}], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20070", "weight": 0, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20071", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20072", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["jetbrains"]}], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20070", "weight": 0, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20071", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20072", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "stickiness": "default"}]}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "swe-1-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20070", "weight": 0, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20071", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20071"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20072", "weight": 500, "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "stickiness": "default"}]}], "variants": []}, {"name": "synchronous-content-filter", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "synchronous-content-filter", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "tab-jump-replacement-boundary-ratio", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "tab-jump-replacement-boundary-ratio", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "50_percent_match", "weight": 1000, "payload": {"type": "string", "value": "0.5"}, "stickiness": "default"}]}], "variants": []}, {"name": "tab-prompt-disabled-step-types", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "tab-prompt-disabled-step-types", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "tab-prompt-disabled-step-types", "weight": 1000, "payload": {"type": "json", "value": "[\"CORTEX_STEP_TYPE_LINT_DIFF\"]"}, "stickiness": "default"}]}], "variants": []}, {"name": "telemetry-kill-switch", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "telemetry-kill-switch", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "terminal-suggestion-model-config", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "terminal-suggestion-model-config", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CHAT_19821", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"model_name\": \"MODEL_CHAT_23151\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "tool_calling_section_content", "type": "experiment", "description": "Replace the default tool calling section of the system prompt with a custom prompt", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}, {"contextName": "requestedModelId", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["MODEL_PRIVATE_3", "MODEL_CHAT_GPT_4_1_2025_04_14"]}], "parameters": {"groupId": "tool_calling_section_content", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "gpt4o_alpha", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"mode\": \"SECTION_OVERRIDE_MODE_PREPEND\",\n  \"content\": \"You are an agent - please keep working, using tools where needed, until the user’s query is completely resolved, before ending your turn and yielding control back to the user.\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "unicode", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "unicode", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "<PERSON><PERSON><PERSON>", "weight": 1000, "payload": {"type": "json", "value": "{\n  \"unicode_limit\": \"300\",\n  \"url_limit\": \"1000\"\n}"}, "stickiness": "default"}]}], "variants": []}, {"name": "use-genai-api-client", "type": "release", "description": "flag to use google's genai sdk client", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "use-genai-api-client", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "use-midterm-output-processor", "type": "experiment", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "use-midterm-output-processor", "rollout": "100", "stickiness": "default"}, "variants": []}, {"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "use-midterm-output-processor", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "v3-rate-protection-switch", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "v3-rate-protection-switch", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}, {"name": "vista-model-id", "type": "release", "description": "This defines the model that we map to \"CASCADE_FREE\" model option in the extension.\n\nYou should never disable this experiment.", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "IN", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "vista-model-id", "rollout": "100", "stickiness": "default"}, "variants": [{"name": "MODEL_CASCADE_20066", "weight": 0, "payload": {"type": "string", "value": "MODEL_CASCADE_20069"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20070", "weight": 335, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20071", "weight": 330, "payload": {"type": "string", "value": "MODEL_CASCADE_20070"}, "stickiness": "default"}, {"name": "MODEL_CASCADE_20072", "weight": 335, "payload": {"type": "string", "value": "MODEL_CASCADE_20072"}, "stickiness": "default"}, {"name": "MODEL_CLAUDE_3_5_HAIKU_20241022", "weight": 0, "payload": {"type": "string", "value": "MODEL_CLAUDE_3_5_HAIKU_20241022"}, "stickiness": "default"}]}], "variants": []}, {"name": "web-search-logging", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "STR_CONTAINS", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "web-search-logging", "rollout": "100", "stickiness": "random"}, "variants": []}], "variants": []}, {"name": "web-search-provider", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [{"contextName": "ide", "operator": "STR_CONTAINS", "caseInsensitive": false, "inverted": false, "values": ["windsurf-insiders"]}], "parameters": {"groupId": "web-search-provider", "rollout": "100", "stickiness": "random"}, "variants": [{"name": "<PERSON>", "weight": 500, "payload": {"type": "string", "value": "BING"}, "stickiness": "random"}, {"name": "Brave", "weight": 500, "payload": {"type": "string", "value": "BRAVE"}, "stickiness": "random"}, {"name": "Exa", "weight": 0, "payload": {"type": "string", "value": "EXA"}, "stickiness": "random"}, {"name": "You", "weight": 0, "payload": {"type": "string", "value": "YOU"}, "stickiness": "random"}]}], "variants": []}, {"name": "workos-authentication-check", "type": "release", "enabled": true, "stale": false, "impressionData": false, "project": "default", "strategies": [{"name": "flexibleRollout", "constraints": [], "parameters": {"groupId": "workos-authentication-check", "rollout": "100", "stickiness": "default"}, "variants": []}], "variants": []}], "segments": [], "query": {"projects": ["*"], "environment": "production", "inlineSegmentConstraints": false}}