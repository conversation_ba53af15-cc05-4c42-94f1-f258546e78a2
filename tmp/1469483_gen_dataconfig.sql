define outfile='20250515-00-PENFTTHAP-AP_ART-DML-WPSOC-1195-dataconfig.sql'
define spoolfile='20250515-00-PENFTTHAP-AP_ART-DML-WPSOC-1195-dataconfig.log'
define commit='commit'



/* 
	genera  un file contenente i comandi sql 
	per il trasporto dei dati delle tabelle di configurazione da un database ad un altro 
	
	eseguirlo con: 
		sqlplus <stringa_connessione> @<nome_di_questo_file>
	
	viene richiesto il nome di file di output (Es: outfile.sql)
	
	che verra' eseguito con:
		sqlplus <stringa_connessione_altro_database> 
	       		SQL> @outfile.sql
               	SQL> commit
               	SQL> quit
               	
    verra prodotto un log chiamato @outfile.log
               		
*/



SPOOL &outfile


-- scrivi commit o roolback &commit';

SET line 2000
SET feedback off
SET heading off
SET autoprint off
SET echo off
SET headsep off
SET newpage none
SET serverout off
SET showmode off
SET sqlnumber off
SET termout off
SET trimspool on
SET verify off


CREATE OR REPLACE PACKAGE porting
AS
   TYPE columns_seq_type IS VARRAY (20) OF VARCHAR (50);

   FUNCTION isnumber (
      x   VARCHAR
   )
      RETURN CHAR;

   FUNCTION quote (
      x   VARCHAR
   )
      RETURN VARCHAR;

   FUNCTION quoten (
      x   NUMBER
   )
      RETURN VARCHAR;

   FUNCTION quotedate (
      x   DATE
   )
      RETURN VARCHAR;

   FUNCTION quotews (
      x   VARCHAR
   )
      RETURN VARCHAR;

   FUNCTION quotenws (
      x   NUMBER
   )
      RETURN VARCHAR;

   FUNCTION quotedatews (
      x   DATE
   )
      RETURN VARCHAR;

   FUNCTION cmpws (
      x   VARCHAR
   )
      RETURN VARCHAR;

   FUNCTION gen_insert (
      table_name   VARCHAR
     ,wherecond    VARCHAR DEFAULT ' 1=1'
   )
      RETURN VARCHAR;

   FUNCTION gen_update (
      table_name   VARCHAR
     ,seq          columns_seq_type DEFAULT columns_seq_type ()
     ,wherecond    VARCHAR DEFAULT ' 1=1'
   )
      RETURN VARCHAR;
END porting;
/

CREATE OR REPLACE PACKAGE BODY porting
AS
   FUNCTION isnumber (
      x   VARCHAR
   )
      RETURN CHAR
   IS
      n   NUMBER;
   BEGIN
      n := TO_NUMBER (x);
      RETURN 'T';
   EXCEPTION
      WHEN OTHERS
      THEN
         RETURN 'F';
   END;

   FUNCTION quote (
      x   VARCHAR
   )
      RETURN VARCHAR
   IS
      i   NUMBER;
      y   VARCHAR (32767 CHAR) := '';
      c   CHAR (1 CHAR);
   BEGIN
      IF x IS NULL
      THEN
         RETURN 'NULL';
      END IF;

      FOR i IN 1 .. LENGTH (x)
      LOOP
         c := SUBSTR (x
                     ,i
                     ,1
                     );

         IF c = ''''
         THEN
            y := y || c;
         END IF;

         y := y || c;
      END LOOP;

      RETURN '''' || y || '''';
   END;

   FUNCTION quoten (
      x   NUMBER
   )
      RETURN VARCHAR
   IS
   BEGIN
      IF x IS NULL
      THEN
         RETURN 'NULL';
      END IF;

      RETURN TO_CHAR (x);
   END;

   FUNCTION quotedate (
      x   DATE
   )
      RETURN VARCHAR
   IS
   BEGIN
      IF x IS NULL
      THEN
         RETURN 'NULL';
      END IF;

      RETURN 'to_date(''' || TO_CHAR (x, 'YYYYMMDDHH24MISS''') || ',''YYYYMMDDHH24MISS'')';
   END;

   FUNCTION quotews (
      x   VARCHAR
   )
      RETURN VARCHAR
   IS
   BEGIN
      RETURN ',' || quote (x);
   END;

   FUNCTION quotenws (
      x   NUMBER
   )
      RETURN VARCHAR
   IS
   BEGIN
      RETURN ',' || quoten (x);
   END;

   FUNCTION quotedatews (
      x   DATE
   )
      RETURN VARCHAR
   IS
   BEGIN
      RETURN ',' || quotedate (x);
   END;

   FUNCTION cmpws (
      x   VARCHAR
   )
      RETURN VARCHAR
   IS
   BEGIN
      IF x IS NULL
      THEN
         RETURN ' is NULL';
      END IF;

      RETURN '=' || quote (x);
   END;

   FUNCTION gen_insert (
      table_name   VARCHAR
     ,wherecond    VARCHAR
   )
      RETURN VARCHAR
   IS
      s        VARCHAR (32767 CHAR) := ' select ''insert into ' || table_name || ' values (''';

      TYPE empcurtyp IS REF CURSOR;

      emp_cv   empcurtyp;
      r        cols%ROWTYPE;
   BEGIN
      OPEN emp_cv FOR 'select * from cols where table_name=''' || table_name || ''' order by column_id';

      LOOP
         FETCH emp_cv
         INTO  r;

         EXIT WHEN emp_cv%NOTFOUND;

         IF r.data_type IN ('CHAR', 'NVARCHAR2', 'VARCHAR2')
         THEN
            IF r.column_id = 1
            THEN
               s := s || ' || porting.quote(' || r.column_name || ')';
            ELSE
               s := s || ' || porting.quotews(' || r.column_name || ')';
            END IF;
         ELSIF r.data_type = 'NUMBER'
         THEN
            IF r.column_id = 1
            THEN
               s := s || ' || porting.quoten(' || r.column_name || ')';
            ELSE
               s := s || ' || porting.quotenws(' || r.column_name || ')';
            END IF;
         ELSIF r.data_type = 'DATE'
         THEN
            IF r.column_id = 1
            THEN
               s := s || ' || porting.quotedate(' || r.column_name || ')';
            ELSE
               s := s || ' || porting.quotedatews(' || r.column_name || ')';
            END IF;
         ELSE
            s := s || ' ' || table_name || ':' || r.column_name || ':' || r.data_type || ': tipo non implementato ';
         -- when 'DATE' then
         END IF;
      END LOOP;

      CLOSE emp_cv;

      s := s || ' || '');''' || ' from ' || table_name || ' where ' || wherecond;
      RETURN s;
   END gen_insert;

   FUNCTION incollection (
      el    VARCHAR
     ,seq   columns_seq_type
   )
      RETURN BOOLEAN
   IS
   BEGIN
      FOR i IN 1 .. seq.COUNT
      LOOP
         IF seq (i) = el
         THEN
            RETURN TRUE;
         END IF;
      END LOOP;

      RETURN FALSE;
   END incollection;

   FUNCTION colname_exist (
      colname      VARCHAR
     ,table_name   VARCHAR
   )
      RETURN BOOLEAN
   IS
      TYPE empcurtyp IS REF CURSOR;

      r        NUMBER;
      emp_cv   empcurtyp;
   BEGIN
      OPEN emp_cv FOR 'select count(*) from cols where table_name=''' || table_name || ''' and column_name=''' || colname
                      || '''';

      FETCH emp_cv
      INTO  r;

      CLOSE emp_cv;

      IF r = 0
      THEN
         RETURN FALSE;
      ELSE
         RETURN TRUE;
      END IF;
   END colname_exist;

   FUNCTION gen_update (
      table_name   VARCHAR
     ,seq          columns_seq_type
     ,wherecond    VARCHAR
   )
      RETURN VARCHAR
   IS
      s                VARCHAR (32767 CHAR)                    := '';
      q                VARCHAR (32767 CHAR);

      TYPE empcurtyp IS REF CURSOR;

      emp_cv           empcurtyp;
      r                cols%ROWTYPE;
      colname          user_ind_columns.column_name%TYPE;
      colpos           user_ind_columns.column_position%TYPE;
      data_type        cols.data_type%TYPE;
      no_such_column   EXCEPTION;
   BEGIN
      FOR i IN 1 .. seq.COUNT
      LOOP
         IF NOT colname_exist (seq (i), table_name)
         THEN
            RETURN 'select ''colonna ' || seq (i) || '  non in tabella ' || table_name || ''' from dual';
         END IF;
      END LOOP;

      OPEN emp_cv FOR 'select * from cols where table_name=''' || table_name || ''' order by column_id';

      LOOP
         FETCH emp_cv
         INTO  r;

         EXIT WHEN emp_cv%NOTFOUND;

         IF NOT incollection (r.column_name, seq)
         THEN
            IF LENGTH (s) > 0
            THEN
               s := s || ',';
            END IF;

            s := s || r.column_name || '=' || '''';

            IF r.data_type IN ('CHAR', 'NVARCHAR2', 'VARCHAR2')
            THEN
               s := s || ' || porting.quote(' || r.column_name || ')';
            ELSIF r.data_type = 'NUMBER'
            THEN
               s := s || ' || porting.quoten(' || r.column_name || ')';
            ELSIF r.data_type = 'DATE'
            THEN
               s := s || ' || porting.quotedate(' || r.column_name || ')';
            ELSE
               s := s || ' ' || table_name || ':' || r.column_name || ':' || r.data_type || ': tipo non implementato ';
            END IF;

            s := s || ' || ''';
         END IF;
      END LOOP;

      CLOSE emp_cv;

      s := 'select ''UPDATE ' || table_name || ' set ' || s || ' where ';
      q :=
         '
			select i.column_name      colname
				  ,i.column_position  colpos
				  ,cols.data_type     data_type
			from
				user_ind_columns i
				,cols
				,(
				select table_name,index_name from (
						select x.table_name,x.index_name
						from
							user_indexes x
							,user_constraints c
						 where
							x.UNIQUENESS=''UNIQUE''
							and x.table_name = :name
							and x.table_name = c.TABLE_NAME (+)
							and x.INDEX_NAME= c.INDEX_NAME (+)
						 order by
							nvl(c.CONSTRAINT_TYPE,'' '')
				)
				where rownum = 1
			) t
			where
				i.table_name = cols.table_name
				and i.column_name = cols.column_name
				and i.TABLE_NAME = t.TABLE_NAME
				and i.INDEX_NAME = t.INDEX_NAME
			order by
				i.COLUMN_POSITION
			';

      OPEN emp_cv FOR q USING table_name;

      LOOP
         FETCH emp_cv
         INTO  colname
              ,colpos
              ,data_type;

         EXIT WHEN emp_cv%NOTFOUND;

         IF colpos > 1
         THEN
            s := s || ' and ';
         END IF;

         s := s || colname || '= ''';

         IF data_type IN ('CHAR', 'NVARCHAR2', 'VARCHAR2')
         THEN
            s := s || ' || porting.quote(' || colname || ')';
         ELSIF data_type = 'NUMBER'
         THEN
            s := s || ' || porting.quoten(' || colname || ')';
         ELSIF data_type = 'DATE'
         THEN
            s := s || ' || porting.quotedate(' || colname || ')';
         ELSE
            s := s || ' ' || table_name || ':' || colname || ':' || data_type || ': tipo non implementato ';
         END IF;

         s := s || ' || ''';
      END LOOP;

      CLOSE emp_cv;

      s := s || ';''' || ' from ' || table_name || ' where ' || wherecond;
      RETURN s;
   END gen_update;
END porting;
/

-- show errors

DEFINE tipi_sistema="(select NOME_TIPO_SISTEMA from TIPI_SISTEMA)"
DEFINE tipi_attivita="(select NOME_TIPO_ATTIVITA from TIPI_ATTIVITA where id_tipo_attivita in (select distinct id_tipo_attivita from ATTIVITA_PER_TIPI_SISTEMA))"

SELECT ' '
FROM   DUAL;
SELECT '-- INIZIO GLOBALE   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;
SELECT '-- Non modificare manualmente ma usare gen_dataconfig.sql'
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'set echo on '
FROM   DUAL;
SELECT 'set sqlblankline on '
FROM   DUAL;
SELECT 'spool &spoolfile '
FROM   DUAL;




select 'set define off' from dual;

SET AUTOPRINT ON
VARIABLE cur REFCURSOR

SELECT ' '
FROM   DUAL;
SELECT '-- VERIFICO ESISTENZA COLONNA NUOVE FUNZIONALITA '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'DECLARE' from dual;
select ' C NUMBER := 0;' from dual;
select ' q VARCHAR2(400) := ''ALTER TABLE ACTION ADD interfaccia VARCHAR2(4000)'';' from dual;
select ' BEGIN' from dual; 
select ' SELECT NVL(' from dual;
select ' (SELECT 1  FROM USER_TAB_COLUMNS UTC WHERE UTC.TABLE_NAME = ''ACTION'' AND UTC.COLUMN_NAME = ''INTERFACCIA''),0)' from dual;
select ' INTO C from dual;' from dual;
select ' IF (C=0) THEN' from dual;
select ' 	execute immediate q;' from dual;
select ' end if;' from dual;
select ' end;' from dual;
select '/' from dual;


SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_SISTEMA '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO TIPI_SISTEMA values(seq_tipi_sis.nextval'
         || porting.quotews (a.descrizione)
         || ',' || NVL2 (a.manutenibile, '''' || a.manutenibile || '''','NULL') 
		 || ',' || '''' || a.nome_tipo_sistema || ''''
         || ',' || NVL2 (a.PREFISSO_IMPORT_AUTOMATICO, '''' || a.PREFISSO_IMPORT_AUTOMATICO || '''','NULL') 
         || ',' || NVL2 (a.totale, '''' || a.totale || '''','NULL') 
         || ');'
FROM     tipi_sistema a
WHERE    a.nome_tipo_sistema IN &tipi_sistema
ORDER BY a.nome_tipo_sistema;


SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('TIPI_SISTEMA', porting.columns_seq_type ('ID_TIPO_SISTEMA'));
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_DATI_TECNICI '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT      'INSERT INTO TIPI_DATI_TECNICI values(seq_tipi_dati_tecnici.nextval'
         || porting.quotews (a.descrizione)
         || ','
         || NVL2 (a.form_sequence
                 , '''' || a.form_sequence || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.form_group
                 , '''' || a.form_group || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.secret_value
                 , '''' || a.secret_value || ''''
                 ,'''N'''
                 )
         || ','
         || NVL2 (a.multiplex_value
                 , '''' || a.multiplex_value || ''''
                 ,'''N'''
                 )
         || ','
         || NVL2 (a.url
                 , '''' || a.url || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.morto
                 , '''' || a.morto || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.telnet
                 , '''' || a.telnet || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.nome
                 , '''' || a.nome || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.flagfile
                 , '''' || a.flagfile || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.non_modificabile
                 , '''' || a.non_modificabile || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.flag
                 , '''' || a.flag || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.flag_valore_vincolato
                 , '''' || a.flag_valore_vincolato || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.lista_oggetti
                 , '''' || a.lista_oggetti || ''''
                 ,'NULL'
                 )
         || ','
         || '''' || a.tipo || ''''
         || ','
         || NVL2 (a.um
                 , '''' || a.um || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.misura
                 , '''' || a.misura || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.contatore
                 , '''' || a.contatore || ''''
                 ,'NULL'
                 )
         ||
         ');'
FROM     tipi_dati_tecnici a
ORDER BY a.nome;


SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('TIPI_DATI_TECNICI'
                                    ,porting.columns_seq_type ('ID_TIPO_DATO_TECNICO'
                                                              ,'NOME'
                                                              ));
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- AGING_CALENDARS '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO AGING_CALENDARS values(seq_aging_calendars.nextval'
         || ','
         || porting.quote(a.label)
         || ','
         || porting.quote(a.description)
         || ','
         || '''' || a.weekend || ''''
         || ');'
FROM     AGING_CALENDARS a
ORDER BY a.id_calendar;

SELECT ' '
FROM   DUAL;

SELECT      'update AGING_CALENDARS
  set description = '||porting.quote(a.description)||'
    , weekend = '''||a.weekend||'''
  where id_calendar = (select id_calendar from aging_calendars where label = '||porting.quote(a.label)||');'
FROM     AGING_CALENDARS a
ORDER BY a.id_calendar;


SELECT ' '
FROM   DUAL;
SELECT '-- AGING_WORKING_HOURS '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO AGING_WORKING_HOURS values((select id_calendar from aging_calendars where label = '||porting.quote((select a2.label from aging_calendars a2 where a2.id_calendar = a.id_calendar))||')'
         || ','
         || '''' || a.day || ''''
         || ','
         || '''' || a.start_time || ''''
         || ','
         || '''' || a.end_time || ''''
         || ');'
FROM     AGING_WORKING_HOURS a
ORDER BY a.id_calendar;

SELECT ' '
FROM   DUAL;

SELECT      'update AGING_WORKING_HOURS
  set day = '''||a.day||'''
    , start_time = '''||a.start_time||'''
    , end_time = '''||a.end_time||'''
  where id_calendar = (select id_calendar from aging_calendars where label = '||porting.quote((select a2.label from aging_calendars a2 where a2.id_calendar = a.id_calendar))||');'
FROM     AGING_WORKING_HOURS a
ORDER BY a.id_calendar;


SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_ATTIVITA '
FROM   DUAL;
SELECT ' '
FROM   DUAL;



SELECT      'INSERT INTO TIPI_ATTIVITA values (seq_tipi_att.nextval'
         || porting.quotews (a.descrizione)
         || ','
         || ''''
         || a.nome_tipo_attivita
         || ''''
         || ','
         || NVL2 (a.autoparse
                 , '''' || a.autoparse || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.id_calendar
                 , '(select id_calendar from aging_calendars where label = '||porting.quote((select label from aging_calendars where id_calendar = a.id_calendar))||')'
                 , 'NULL'
                 )
         || ','
         || porting.quote(a.ui_route)
         || ');'
FROM     tipi_attivita a
WHERE    a.nome_tipo_attivita IN &tipi_attivita
ORDER BY a.nome_tipo_attivita;

SELECT ' '
FROM   DUAL;

SELECT      'UPDATE TIPI_ATTIVITA set DESCRIZIONE='||porting.quote(a.descrizione)||', AUTOPARSE='||NVL2 (a.autoparse, '''' || a.autoparse || '''','NULL')||',ID_CALENDAR='||NVL2 (a.id_calendar, '(select id_calendar from aging_calendars where label = '||porting.quote((select label from aging_calendars where id_calendar = a.id_calendar))||')', 'NULL')||', UI_ROUTE='||porting.quote(a.ui_route)||' where NOME_TIPO_ATTIVITA= '''||a.nome_tipo_attivita||''';'
FROM     tipi_attivita a
WHERE    a.nome_tipo_attivita IN &tipi_attivita
ORDER BY a.nome_tipo_attivita;

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_ATTIVITA_SLA '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'delete tipi_attivita_sla;' from dual;
SELECT      'INSERT INTO TIPI_ATTIVITA_SLA values((select id_tipo_attivita from TIPI_ATTIVITA where nome_Tipo_Attivita = '||porting.quote((select a2.nome_tipo_Attivita from TIPI_ATTIVITA a2 where a2.id_tipo_Attivita = a.id_tipo_attivita))||')'
         || ','
         || a.target_from
         || ','
         || nvl2(a.target_to,''''||a.target_to||'''', 'null')
         || ','
         || porting.quote(a.target_label)
         || ','
         || porting.quote(a.target_level)
         || ','
         || 'to_date('''||to_char(a.insert_date,'yyyymmddhh24miss')||''',''yyyymmddhh24miss'')'
         || ','
         || 'to_date('''||to_char(a.disable_date,'yyyymmddhh24miss')||''',''yyyymmddhh24miss'')'
         || ');'
FROM     TIPI_ATTIVITA_SLA a
ORDER BY a.id_tipo_attivita, target_from;

SELECT ' '
FROM   DUAL;
SELECT '-- VERIFICO ESISTENZA TABELLA TIPI_ATTIVITA_COMP '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

DECLARE
 function f (
     tab_type      VARCHAR
   )
      RETURN VARCHAR
   IS
      s        VARCHAR (32767 CHAR) := 'select ''-- Inizio check Property Table --'' from dual';
      c		   NUMBER;
   BEGIN
	 
	 select count (*) into c from user_tables where table_name in (tab_type);
		
     if (c = 1) then
     		--return s;
	 		s := '
	 			select ''delete '||tab_type||';'' from dual
	 			union all
	 			SELECT      ''INSERT INTO '||tab_type||' values (seq_tipi_attivita_comp.nextval,''
				         || ''(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = ''''''||(SELECT NOME_TIPO_ATTIVITA FROM TIPI_ATTIVITA WHERE ID_TIPO_ATTIVITA = A.ID_TIPO_ATTIVITA)||'''''')''
				         || '',''
				         || nvl2(a.abilitato,''''''''||a.abilitato||'''''''',''null'')
				         || '',''
				         || nvl2(a.vedi_dt,''''''''||a.vedi_dt||'''''''',''null'')
				         || '',''
				         || nvl2(a.chiusura_figli,''''''''||a.chiusura_figli||'''''''',''null'')
				         || '');''
				FROM     '||tab_type||' a
	 		';
	 else
	 	s := 'select ''-- '||tab_type||' non presente --'' from dual';
	 end if;

      RETURN s;
   END f;
 begin
   	 OPEN :cur FOR f('TIPI_ATTIVITA_COMP') ;
 end;
/

SELECT ' '
FROM   DUAL;
SELECT '-- Gestione Behaviours'
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'delete tipi_attivita_behaviour;' from dual;
select 'delete tipi_sistema_behaviour;' from dual;

SELECT ' '
FROM   DUAL;
SELECT '-- BEHAVIOUR_EVENT_LOOKUP '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'delete BEHAVIOUR_EVENT_LOOKUP;' from dual
union all
SELECT      'INSERT INTO BEHAVIOUR_EVENT_LOOKUP values ('||a.id
	         || porting.quotews(a.label)
	         || porting.quotews(a.description)
	         || ');'
	FROM     BEHAVIOUR_EVENT_LOOKUP a
;

SELECT ' '
FROM   DUAL;
SELECT '-- BEHAVIOUR_LOOKUP '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'delete BEHAVIOUR_LOOKUP;' from dual
union all
SELECT      'INSERT INTO BEHAVIOUR_LOOKUP values ('||a.id
	         || porting.quotews(a.label)
	         || porting.quotews(a.description)
	         || ');'
	FROM     BEHAVIOUR_LOOKUP a
;

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_ATTIVITA_BEHAVIOUR '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO TIPI_ATTIVITA_BEHAVIOUR values ('||a.id||','
         || '(select id_tipo_attivita from tipi_attivita where nome_Tipo_attivita = '''||(SELECT NOME_TIPO_ATTIVITA FROM TIPI_ATTIVITA WHERE ID_TIPO_ATTIVITA = A.ID_TIPO_ATTIVITA)||''')'
         || ','
         || '(select id from BEHAVIOUR_EVENT_LOOKUP where label = '''||(SELECT label FROM BEHAVIOUR_EVENT_LOOKUP WHERE id = A.event_id)||''')'
         || ','
         || '(select id from BEHAVIOUR_LOOKUP where label = '''||(SELECT label FROM BEHAVIOUR_LOOKUP WHERE id = A.behaviour_id)||''')'
         || porting.quotews(a.behaviour_class)
         || porting.quotenws(a.execution_order)
         || ','
         || 'to_date('''||to_char(a.disable_date,'yyyymmddhh24miss')||''',''yyyymmddhh24miss'')'
         || ');'
FROM     TIPI_ATTIVITA_BEHAVIOUR a;

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_SISTEMA_BEHAVIOUR '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO TIPI_SISTEMA_BEHAVIOUR values ('||a.id||','
         || '(select id_tipo_sistema from tipi_sistema where nome_Tipo_sistema = '''||(SELECT NOME_TIPO_SISTEMA FROM TIPI_SISTEMA WHERE ID_TIPO_SISTEMA = A.ID_TIPO_SISTEMA)||''')'
         || ','
         || '(select id from BEHAVIOUR_EVENT_LOOKUP where label = '''||(SELECT label FROM BEHAVIOUR_EVENT_LOOKUP WHERE id = A.event_id)||''')'
         || ','
         || '(select id from BEHAVIOUR_LOOKUP where label = '''||(SELECT label FROM BEHAVIOUR_LOOKUP WHERE id = A.behaviour_id)||''')'
         || porting.quotews(a.behaviour_class)
         || porting.quotenws(a.execution_order)
         || ','
         || 'to_date('''||to_char(a.disable_date,'yyyymmddhh24miss')||''',''yyyymmddhh24miss'')'
         || ');'
FROM     TIPI_SISTEMA_BEHAVIOUR a;

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_DATI_TECNICI_ATTIVITA '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT      'INSERT INTO TIPI_DATI_TECNICI_ATTIVITA values(seq_tipi_dati_tecnici_att.nextval'
         || ','
         || NVL2 (a.id_tipo_Attivita
                 , '''' || a.id_tipo_Attivita || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.id_tipo_sottoattivita
                 , '''' || a.id_tipo_sottoattivita || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.id_action
                 , '''' || a.id_action || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.tipo_ui
                 , '''' || a.tipo_ui || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.valori
                 , porting.quote(a.valori)
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.valore_default
                 , porting.quote(a.valore_default)
                 ,'NULL'
                 )
         || porting.quotews (a.descrizione)
         || ','
         || NVL2 (a.obbligatorio
                 , '''' || a.obbligatorio || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.morto
                 , '''' || a.morto || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.etichetta
                 , porting.quote(a.etichetta)
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.suggerimento
                 , porting.quote(a.suggerimento)
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.valore_predefinito
                 , porting.quote(a.valore_predefinito)
                 ,'NULL'
                 )
         || ');'
FROM     tipi_dati_tecnici_attivita a
ORDER BY a.descrizione;


SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('TIPI_DATI_TECNICI_ATTIVITA'
                                    ,porting.columns_seq_type ('ID_TIPO_DATO_TECNICO_ATTIVITA'
                                                              ,'ID_TIPO_ATTIVITA'
                                                              ,'ID_TIPO_SOTTOATTIVITA'
                                                              ,'ID_ACTION'
                                                              ));
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- STATI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


merge 


SELECT      'INSERT INTO STATI values('
         || DECODE (porting.isnumber (a.id_stato)
                   ,'F', porting.quote (a.id_stato)
                   ,'to_char(seq_stati.nextval)'
                   )
         || porting.quotews (a.descrizione)
         || porting.quotews (a.flag_stato_partenza)
         || porting.quotews (a.priorita)
         || porting.quotews (a.morto)
         || porting.quotews (a.genera_mail)
         || porting.quotews (a.nome)
         || porting.quotews (a.genera_mail_mittente)
         || porting.quotews (a.interfaccia)
         || porting.quotews (a.id_tipo_attivita)
         || ');'
FROM     stati a
ORDER BY a.nome;


SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('STATI', porting.columns_seq_type ('ID_STATO'));
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- ACTION  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO ACTION values ('
         || DECODE (porting.isnumber (a.id_action)
                   ,'F', porting.quote (a.id_action)
                   ,'to_char(seq_action.nextval)'
                   )
         || porting.quotews (a.descrizione)
         || porting.quotews (a.flag_action_partenza)
         || porting.quotews (a.morto)
         || porting.quotews (a.nome)
         || porting.quotews (a.interfaccia)
         || ');'
FROM     action a
ORDER BY a.nome;


SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('ACTION', porting.columns_seq_type ('ID_ACTION'));
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_DATI_TECNICI_ATT_ACTION  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete tipi_dati_tecnici_att_action;'
FROM   DUAL;

SELECT      'INSERT INTO tipi_dati_tecnici_att_action values('
         || '(select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='''
         || (SELECT nome_tipo_attivita
             FROM   tipi_attivita t
             WHERE  t.id_tipo_attivita = a.id_tipo_attivita)
         || ''')'
         || ','
         || DECODE (porting.isnumber (a.id_action)
                   ,'F', '''' || a.id_action || ''''
                   , '(select id_action from action where nome=''' || (SELECT nome
                                                                       FROM   action t
                                                                       WHERE  t.id_action = a.id_action) || ''')'
                   )
         || ',(select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='''
         || (SELECT descrizione
             FROM   tipi_dati_tecnici_attivita t
             WHERE  t.id_tipo_dato_tecnico_attivita = a.id_tipo_dato_tecnico_attivita)
         || ''')'
         || ','
         || NVL (TO_CHAR (a.posizione), 'NULL')
         || ','
         || NVL2 (a.obbligatorio
                 , '''' || a.obbligatorio || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.solo_lettura
                 , '''' || a.solo_lettura || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.nascosto
                 , '''' || a.nascosto || ''''
                 ,'NULL'
                 )
         || ','
         || NVL2 (a.prepopola
                 , '''' || a.prepopola || ''''
                 ,'NULL'
                 )
         || ');'
FROM     tipi_dati_tecnici_att_action a
WHERE    id_tipo_attivita IN (SELECT id_tipo_attivita
                              FROM   tipi_attivita
                              WHERE  nome_tipo_attivita IN &tipi_attivita)
ORDER BY a.id_tipo_attivita
        ,a.id_action
        ,a.id_tipo_dato_tecnico_attivita;


SELECT ' '
FROM   DUAL;
SELECT '-- TDTA_TAGS  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete TDTA_TAGS;'
FROM   DUAL;

SELECT      'INSERT INTO TDTA_TAGS values(
            (select ID_TIPO_DATO_TECNICO_ATTIVITA from tipi_dati_tecnici_attivita where descrizione='''
         || (SELECT descrizione
             FROM   tipi_dati_tecnici_attivita t
             WHERE  t.id_tipo_dato_tecnico_attivita = a.id_tipo_dato_tecnico_attivita)
         || ''')'
         || ','
         || '(select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='''
         || (SELECT nome_tipo_attivita
             FROM   tipi_attivita t
             WHERE  t.id_tipo_attivita = a.id_tipo_attivita)||''')'
         || porting.quotews(a.tag)
         || ');'
FROM     TDTA_TAGS a
WHERE    id_tipo_attivita IN (SELECT id_tipo_attivita
                              FROM   tipi_attivita
                              WHERE  nome_tipo_attivita IN &tipi_attivita)
ORDER BY a.id_tipo_attivita
        ,a.id_tipo_dato_tecnico_attivita
;


SELECT ' '
FROM   DUAL;
SELECT '-- ACTIVITY_PROPERTIES_GROUP (multitables)  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete AP;' FROM   DUAL;
SELECT 'delete AT_AP_GROUPS;' FROM   DUAL;
SELECT 'delete AP_GROUPS;' FROM   DUAL;

SELECT      'INSERT INTO AP_GROUPS VALUES (SEQ_AP_GROUPS.NEXTVAL'
  ||' ,'''||DESCRIZIONE||''''
  ||' ,'||nvl2(disabilitato,''''||disabilitato||'''', 'null')||'  );'
FROM AP_GROUPS
order by descrizione;

SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO AT_AP_GROUPS VALUES ('
  ||' (select id from ap_groups where descrizione = '''||ap.descrizione||''')'
  ||' , (select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = '''||ta.nome_tipo_Attivita||''')'
  ||' , '||aag.ordine
  ||' ,'||nvl2(aag.disabilitato,''''||aag.disabilitato||'''', 'null')||'  );'
FROM AT_AP_GROUPS aag
  join ap_groups ap on ap.id = aag.id_raggruppamento
  join tipi_attivita ta on ta.id_tipo_attivita = aag.id_Tipo_attivita
order by aag.id_raggruppamento, aag.id_tipo_attivita, aag.ordine;

SELECT ' '
FROM   DUAL;

select 'insert into ap values ('
 ||' seq_ap.nextval'
 ||' ,(select id_tipo_attivita from tipi_attivita where nome_tipo_attivita = '''||ta.nome_tipo_Attivita||''')'
 ||' ,(select id_tipo_dato_tecnico_attivita from tipi_dati_tecnici_attivita where descrizione = '''||tdta.descrizione||''')'
 ||' ,'||ap.ORDINE
 ||' ,(select id from ap_groups where descrizione = '''||ag.descrizione||''')'
 ||' ,'||nvl2(ap.ORDINE_RAGGRUPPAMENTO,''||ap.ORDINE_RAGGRUPPAMENTO||'', 'null')
 ||' ,'||nvl2(ap.SOLO_LETTURA,''''||ap.SOLO_LETTURA||'''', 'null')
 ||' ,'||nvl2(ap.DISABILITATO,''''||ap.DISABILITATO||'''', 'null')
 ||' ,'||nvl2(ap.ANNULLABILE,''''||ap.ANNULLABILE||'''', 'null')
 ||');'
from ap ap
  join tipi_attivita ta on ta.id_tipo_attivita = ap.id_Tipo_attivita
  join tipi_dati_Tecnici_attivita tdta on tdta.id_tipo_dato_Tecnico_attivita = ap.id_Tipo_dato_tecnico_attivita
  left join ap_groups ag on ag.id = ap.id_raggruppamento
order by ap.id, ap.id_tipo_attivita, ap.ordine
  ;

SELECT ' '
FROM   DUAL;
SELECT '-- SYSTEM_PROPERTIES_GROUP (multitables)  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete SP;' FROM   DUAL;
SELECT 'delete ST_SP_GROUPS;' FROM   DUAL;
SELECT 'delete SP_GROUPS;' FROM   DUAL;

SELECT      'INSERT INTO SP_GROUPS VALUES (SEQ_SP_GROUPS.NEXTVAL'
  ||' ,'''||DESCRIZIONE||''''
  ||' ,'||nvl2(disabilitato,''''||disabilitato||'''', 'null')||'  );'
FROM SP_GROUPS
order by descrizione;

SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO ST_SP_GROUPS VALUES ('
  ||' (select id from sp_groups where descrizione = '''||ap.descrizione||''')'
  ||' , (select id_tipo_sistema from tipi_Sistema where nome_tipo_sistema = '''||ta.nome_tipo_sistema||''')'
  ||' , '||aag.ordine
  ||' ,'||nvl2(aag.disabilitato,''''||aag.disabilitato||'''', 'null')||'  );'
FROM ST_SP_GROUPS aag
  join sp_groups ap on ap.id = aag.id_raggruppamento
  join tipi_sistema ta on ta.id_tipo_sistema = aag.id_Tipo_sistema
order by aag.id_raggruppamento, aag.id_tipo_sistema, aag.ordine;

SELECT ' '
FROM   DUAL;

select 'insert into sp values ('
 ||' seq_sp.nextval'
 ||' ,(select id_tipo_sistema from tipi_sistema where nome_tipo_sistema = '''||ta.nome_tipo_sistema||''')'
 ||' ,(select id_tipo_dato_tecnico from tipi_dati_tecnici where nome = '''||tdta.nome||''')'
 ||' ,'||ap.ORDINE
 ||' ,(select id from sp_groups where descrizione = '''||ag.descrizione||''')'
 ||' ,'||nvl2(ap.ORDINE_RAGGRUPPAMENTO,''||ap.ORDINE_RAGGRUPPAMENTO||'', 'null')
 ||' ,'||nvl2(ap.SOLO_LETTURA,''''||ap.SOLO_LETTURA||'''', 'null')
 ||' ,'||nvl2(ap.DISABILITATO,''''||ap.DISABILITATO||'''', 'null')
 ||' ,'||nvl2(ap.ANNULLABILE,''''||ap.ANNULLABILE||'''', 'null')
 ||');'
from sp ap
  join tipi_sistema ta on ta.id_tipo_sistema = ap.id_Tipo_sistema
  join tipi_dati_Tecnici tdta on tdta.id_tipo_dato_Tecnico = ap.id_Tipo_dato_tecnico
  left join sp_groups ag on ag.id = ap.id_raggruppamento
order by ap.id, ap.id_tipo_sistema, ap.ordine
  ;

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_ATTIVITA_TIPI_ALLEGATI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'INSERT INTO TIPI_ATTIVITA_TIPI_ALLEGATI VALUES (SEQ_TIPI_ATTIVITA_TIPI_ALL.nextval'
  || ','
  || '(select ta.id_tipo_attivita from tipi_attivita ta where ta.nome_tipo_attivita = '||porting.quote((select nome_tipo_attivita from tipi_attivita where id_tipo_attivita = tata.id_tipo_attivita))||')'
  || ','
  || porting.quote(tata.id_tipo_documento)
  || ','
  || porting.quote(tata.descrizione)
  || ','
  || '(select tdta.id_tipo_dato_Tecnico_attivita from tipi_dati_Tecnici_attivita tdta where tdta.descrizione = '||porting.quote((select descrizione from tipi_dati_tecnici_attivita where id_tipo_dato_tecnico_attivita = tata.id_tipo_dato_tecnico_attivita))||')'
  || ','
  || porting.quote(tata.ordine)
  ||');'
FROM TIPI_ATTIVITA_TIPI_ALLEGATI tata
order by id;

select 'update tipi_attivita_tipi_allegati set descrizione = '||porting.quote(t.descrizione)||', ordine = '||porting.quote(t.ordine)||' where id_tipo_attivita = (Select ta.id_tipo_attivita from tipi_Attivita ta where ta.nome_tipo_attivita = '||porting.quote(ta.nome_tipo_attivita)||') and id_tipo_documento = '||porting.quote(t.id_tipo_documento)||';'
from tipi_Attivita_tipi_Allegati t
    join tipi_attivita ta on ta.id_tipo_attivita = t.id_tipo_Attivita
order by t.id_Tipo_attivita, t.id_tipo_documento;

SELECT ' '
FROM   DUAL;
SELECT '-- GRUPPI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT      'INSERT INTO GRUPPI values(seq_gruppi.nextval'
         || porting.quotews (a.descrizione)
         || porting.quotews (a.amministratore)
         || porting.quotews (a.root)
         || porting.quotews (a.morto)
         || porting.quotews (a.nome)
		 || porting.quotews (a.privato)
		 || porting.quotews (a.autogenerato)
         || ');'
FROM     gruppi a
where a.autogenerato is null -- i gruppi autogenerati non devono mai essere portati dal dataconfig
ORDER BY a.nome;

SELECT ' '
FROM   DUAL;

SELECT      'update GRUPPI set DESCRIZIONE='||porting.quote (a.descrizione)||',AMMINISTRATORE='||porting.quote (a.amministratore)||',ROOT='||porting.quote (a.root)||',MORTO='||porting.quote (a.morto)||',NOME='||porting.quote (a.nome)||',PRIVATO='||porting.quote (a.privato)||',AUTOGENERATO='||porting.quote (a.autogenerato)||' where NOME= '||porting.quote (a.nome)||';'
FROM     gruppi a
where a.autogenerato is null -- i gruppi autogenerati non devono mai essere portati dal dataconfig
ORDER BY a.nome;

SELECT ' '
FROM   DUAL;

select 'delete gruppi_comp;' from dual;

SELECT ' '
FROM   DUAL;

select 'insert into gruppi_comp values ((select g.id_gruppo from gruppi g where g.nome = '''||(select g2.nome from gruppi g2 where g2.id_gruppo = a.id_gruppo)||'''),(select c.id_comportamento from gruppi_comp_lookup c where c.label = '''||(select c2.label from gruppi_comp_lookup c2 where c2.id_comportamento = a.id_comportamento)||'''),to_date('''||to_char(a.data_creazione,'yyyymmddhh24miss')||''', ''yyyymmddhh24miss''),to_date('''||to_char(a.data_cancellazione,'yyyymmddhh24miss')||''', ''yyyymmddhh24miss''));'
from gruppi_comp a;

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_ATTIVITA_AP_GRUPPI_PERM  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'delete TIPI_ATTIVITA_AP_GRUPPI_PERM;' from dual;

SELECT      'INSERT INTO TIPI_ATTIVITA_AP_GRUPPI_PERM values(
         (select id_tipo_attivita from tipi_attivita where nome_Tipo_Attivita = '''||ta.nome_tipo_attivita||'''),
         (select id_gruppo from gruppi where nome = '''||g.nome||''')
         );'
FROM     TIPI_ATTIVITA_AP_GRUPPI_PERM a
   join tipi_attivita ta on ta.id_tipo_attivita = a.id_tipo_attivita
   join gruppi g on g.id_gruppo = a.id_gruppo
ORDER BY ta.nome_tipo_attivita, g.nome;

SELECT ' '
FROM   DUAL;
SELECT '-- RPT_GRUPPI'
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete RPT_PERMESSI;'
FROM   DUAL;
SELECT 'delete RPT;'
FROM   DUAL;
SELECT 'delete RPT_GRUPPI;'
FROM   DUAL;

select 'insert into rpt_gruppi values ('||r.id_gruppo||','||porting.quote(r.label)||','||porting.quote(r.descrizione)||');'
From rpt_gruppi r;

SELECT ' '
FROM   DUAL;
SELECT '-- RPT'
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'insert into rpt values ('||r.id_report||','||porting.quote(r.label)||','||porting.quote(r.descrizione)||','||porting.quote(r.id_gruppo)||','||porting.quote(r.ordine)||','||porting.quote(r.query)||','||porting.quote(r.tipo_filtro)||');'
from rpt r;

SELECT ' '
FROM   DUAL;
SELECT '-- RPT_PERMESSI'
FROM   DUAL;
SELECT ' '
FROM   DUAL;

select 'insert into rpt_permessi values ('||rp.id_report||',(select id_gruppo from gruppi where nome = '||porting.quote((select g.nome from gruppi g where g.id_gruppo = rp.id_gruppo))||'));'
from rpt_permessi rp;

SELECT ' '
FROM   DUAL;
SELECT '-- PERMISSION_ACTION  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT 'delete permission_action;'
FROM   DUAL;

SELECT      'INSERT INTO permission_action values('
         || '(select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='''
         || (SELECT nome_tipo_attivita
             FROM   tipi_attivita t
             WHERE  t.id_tipo_attivita = a.id_tipo_attivita)
         || ''')'
         || ','
         || DECODE (porting.isnumber (a.id_stato_iniziale)
                   ,'F', '''' || a.id_stato_iniziale || ''''
                   , '(select ID_STATO from stati where nome=''' || (SELECT nome
                                                                     FROM   stati t
                                                                     WHERE  t.id_stato = a.id_stato_iniziale) || ''')'
                   )
         || ','
         || DECODE (porting.isnumber (a.id_action_permessa)
                   ,'F', '''' || a.id_action_permessa || ''''
                   , '(select id_action from action where nome=''' || (SELECT nome
                                                                       FROM   action t
                                                                       WHERE  t.id_action = a.id_action_permessa) || ''')'
                   )
         || ','
         || DECODE (porting.isnumber (a.id_stato_finale)
                   ,'F', '''' || a.id_stato_finale || ''''
                   , '(select ID_STATO from stati where nome=''' || (SELECT nome
                                                                     FROM   stati t
                                                                     WHERE  t.id_stato = a.id_stato_finale) || ''')'
                   )
         || ',(select id_gruppo from gruppi where NOME='''
         || (SELECT nome
             FROM   gruppi t
             WHERE  t.id_gruppo = a.id_gruppo_abilitato)
         || ''')'
         || ');'
FROM     permission_action a
WHERE    id_tipo_attivita IN (SELECT id_tipo_attivita
                              FROM   tipi_attivita
                              WHERE  nome_tipo_attivita IN &tipi_attivita)
ORDER BY a.id_tipo_attivita
        ,a.id_stato_iniziale
        ,a.id_action_permessa
        ,a.id_stato_finale
        ,a.id_gruppo_abilitato;

SELECT ' '
FROM   DUAL;
SELECT '-- TDTA_UI_URL '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete TDTA_UI_URL;'
FROM   DUAL;

SELECT 'insert into tdta_ui_url values ((select TDTA.ID_TIPO_DATO_TECNICO_ATTIVITA From tipi_Dati_Tecnici_attivita TDTA WHERE TDTA.DESCRIZIONE = '''||(select TDTA2.DESCRIZIONE From tipi_Dati_Tecnici_attivita TDTA2 WHERE TDTA2.ID_TIPO_DATO_tECNICO_ATTIVITA = T.ID_TIPO_DATO_TECNICO_ATTIVITA)||'''),'||porting.quote(t.baseurl)||','||porting.quote(t.params)||');' From TDTA_UI_URL t;

SELECT ' '
FROM   DUAL;
SELECT '-- SERIALIZZAZIONE_ENTITA  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete SERIALIZZAZIONE_ENTITA;' FROM   DUAL;

SELECT      'INSERT INTO SERIALIZZAZIONE_ENTITA VALUES ('
   || porting.quote (TIPO_OGGETTO)
   || porting.quotews (NOME_OGGETTO)
   || porting.quotews (ETICHETTA)
   || porting.quotews (PERCORSO)
   || porting.quotews (DESCRIZIONE)
   || porting.quotews (NASCOSTO)
   || porting.quotews (ESCLUSO)
   || porting.quotews (FORMATO)
   || porting.quotews (POSIZIONE)
   || porting.quotews (DISABILITATO)
   ||');'
FROM SERIALIZZAZIONE_ENTITA;

SELECT ' '
FROM   DUAL;
SELECT '-- MODULI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('MODULI');
END;
/

SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('MODULI', porting.columns_seq_type ());
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- MODULI_DETTAGLIO  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT 'delete permission_moduli;'
FROM   DUAL;

SELECT 'delete moduli_dettaglio;'
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('MODULI_DETTAGLIO', 'sottovoce_di is null');
END;
/

BEGIN
   OPEN :cur FOR porting.gen_insert ('MODULI_DETTAGLIO', 'sottovoce_di is not null');
END;
/


SELECT ' '
FROM   DUAL;
SELECT '-- PERMISSION_MODULI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT      'insert into permission_moduli values('
         || a.id_dettaglio
         || porting.quotews (a.tabella_riferimento)
         || ', '
         || (   '(select ID_GRUPPO'
             || ' from     GRUPPI g '
             || ' where '
             || '       g.NOME = '
             || porting.quote ((SELECT g.nome
                                FROM   gruppi g
                                WHERE  g.id_gruppo = a.id_abilitato))
             || ')'
            )
         || ');'
FROM     permission_moduli a
WHERE    a.tabella_riferimento = 'GRUPPI'
ORDER BY a.tabella_riferimento
        ,a.id_dettaglio
        ,a.id_abilitato;


SELECT ' '
FROM   DUAL;
SELECT '-- ATTIVITA_PER_TIPI_SISTEMA  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT 'delete ATTIVITA_PER_TIPI_SISTEMA;'
FROM   DUAL;

SELECT      'INSERT INTO ATTIVITA_PER_TIPI_SISTEMA values('
         || '(select id_tipo_attivita from tipi_attivita where NOME_TIPO_ATTIVITA='''
         || (SELECT nome_tipo_attivita
             FROM   tipi_attivita t
             WHERE  t.id_tipo_attivita = a.id_tipo_attivita)
         || ''')'
         || ',(select id_tipo_sistema from tipi_sistema where NOME_TIPO_SISTEMA='''
         || (SELECT nome_tipo_sistema
             FROM   tipi_sistema t
             WHERE  t.id_tipo_sistema = a.id_tipo_sistema)
         || ''')'
         || ',NULL'
         || ');'
FROM     attivita_per_tipi_sistema a
WHERE    id_tipo_attivita IN (SELECT id_tipo_attivita
                              FROM   tipi_attivita
                              WHERE  nome_tipo_attivita IN &tipi_attivita)
         AND id_tipo_sistema IN (SELECT id_tipo_sistema
                                 FROM   tipi_sistema
                                 WHERE  nome_tipo_sistema IN &tipi_sistema)
ORDER BY a.id_tipo_attivita
        ,a.id_tipo_sistema
        ,a.id_tipo_sottoattivita;


SELECT ' '
FROM   DUAL;
SELECT '-- ICONE_AZIONI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


BEGIN
   OPEN :cur FOR porting.gen_insert ('ICONE_AZIONI');
END;
/

SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('ICONE_AZIONI');
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- PERMISSION_ICONE  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT 'delete PERMISSION_ICONE;'
FROM   DUAL;

SELECT 
'insert into PERMISSION_icone values ('
||PORTING.QUOTE (PI.TIPO_AZIONE)
||PORTING.QUOTEWS (PI.TIPO_ABILITATO)
||','
|| CASE WHEN PI.TIPO_ABILITATO IS NULL
    THEN PORTING.QUOTE (PI.ID_ABILITATO)
   WHEN PI.TIPO_ABILITATO = 'GRUPPO'
    THEN (   '(select ID_GRUPPO'
             || ' from     GRUPPI g '
             || ' where '
             || '       g.NOME = '
             || porting.quote ((SELECT g.nome
                                FROM   GRUPPI G
                                WHERE  g.id_gruppo = pi.id_abilitato))
             || ')'
            )
   ELSE (   '(select ID_operatore'
             || ' from     operatori op '
             || ' where '
             || '       op.login_operatore = '
             || porting.quote ((SELECT op2.login_operatore
                                FROM   OPERATORI OP2
                                WHERE  op2.id_operatore = pi.id_abilitato))
             || ')'
            )
  END
||');'
FROM PERMISSION_ICONE PI
;

SELECT ' '
FROM   DUAL;
SELECT '-- OPERATORI  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;


SELECT    'insert into operatori values(1,''ROOT'',''f7b2993185f755d2212840328001bb2f'',(select id_gruppo from gruppi where nome=''ROOT'')'
       || ',''ROOT'',NULL,NULL,NULL,NULL,''ROOT'',NULL,NULL,-1,NULL,NULL,''helvetica'',0,0,NULL,NULL,NULL);'
FROM   DUAL;

-- la password e' pippo123

SELECT ' '
FROM   DUAL;
SELECT '-- QUERY_MODULI   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('QUERY_MODULI');
END;
/

SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_update ('QUERY_MODULI');
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- PERMISSION_QUERY   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;



SELECT    'INSERT INTO PERMISSION_QUERY values('
       || id_query
       || ','
       || ' (select id_gruppo from gruppi where nome='''
       || (SELECT nome
           FROM   gruppi x
           WHERE  x.id_gruppo = a.id_abilitato)
       || '''),'
       || porting.quote (a.tabella_riferimento)
       || ');'
FROM   permission_query a
WHERE  a.tabella_riferimento = 'GRUPPI';


SELECT ' '
FROM   DUAL;


SELECT ' '
FROM   DUAL;
SELECT '-- PASSWORD   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('PASSWORD');
END;
/



SELECT ' '
FROM   DUAL;
SELECT '-- CONFIG_ART   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('CONFIG_ART');
END;
/


SELECT ' '
FROM   DUAL;
SELECT '-- CLASSI_SISTEMA   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('CLASSI_SISTEMA');
END;
/


SELECT ' '
FROM   DUAL;
SELECT '-- CATEGORIE_SISTEMA   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('CATEGORIE_SISTEMA');
END;
/

SELECT ' '
FROM   DUAL;
SELECT '-- TIPI_OGGETTO   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

BEGIN
   OPEN :cur FOR porting.gen_insert ('TIPI_OGGETTO');
END;
/

-- aggiunti per leads 

SELECT ' '
FROM   DUAL;
SELECT '-- sistemi   '
FROM   DUAL;
SELECT ' '
FROM   DUAL;



select '/*  da fare ' from dual;
select 'BEGIN ' from dual;
select '   OPEN :cur FOR porting.gen_insert (''SISTEMI''); ' from dual; 
select 'END;  ' from dual;
select '/' from dual;

select '--	operatori_gruppi - il gruppo di root deve essere 1 ' from dual;

select '*/' from dual;

SELECT ' ' FROM   DUAL;
SELECT '-- TIPI_SISTEMA_TIPI_DATI_TECNICI  ' FROM   DUAL;
SELECT ' ' FROM   DUAL;

SELECT 'delete TIPI_SISTEMA_TIPI_DATI_TECNICI;' FROM   DUAL;


SELECT      'INSERT INTO TIPI_SISTEMA_TIPI_DATI_TECNICI values('
         || '(select id_tipo_sistema from tipi_sistema where nome_tipo_sistema='''
         || (select nome_tipo_sistema from tipi_sistema t where t.id_tipo_sistema=a.id_tipo_sistema)
         || ''')'
         || ','
         || '(select id_tipo_dato_tecnico from tipi_dati_tecnici where NOME='''
         || (SELECT nome FROM   tipi_dati_tecnici t WHERE  t.id_tipo_dato_tecnico = a.id_tipo_dato_tecnico)
         || '''));'
FROM     TIPI_SISTEMA_TIPI_DATI_TECNICI a
order by id_tipo_sistema,id_tipo_dato_tecnico
;



SELECT '-- FINE GLOBALE - ESEGUO &commit  '
FROM   DUAL;
SELECT ' '
FROM   DUAL;

SELECT '&commit;'
FROM   DUAL;
SELECT 'spool off'
FROM   DUAL;
SELECT 'quit '
FROM   DUAL;

SPOOL off
QUIT
