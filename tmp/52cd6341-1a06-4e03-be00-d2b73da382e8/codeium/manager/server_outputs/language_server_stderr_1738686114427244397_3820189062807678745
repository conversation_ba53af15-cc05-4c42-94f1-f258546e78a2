2025/02/04 17:21:54 maxprocs: Leaving GOMAXPROCS=4: CPU quota undefined
I0204 17:21:54.579207 1456809 main.go:568] Setting GOMAXPROCS to 4
I0204 17:21:54.583871 1456809 main.go:780] Starting language server process with pid 1456809
I0204 17:21:54.583932 1456809 server.go:220] Language server will attempt to listen on host 127.0.0.1
I0204 17:21:54.584140 1456809 server.go:227] Language server listening on random port at 43915
2025/02/04 17:21:54 [proxy.Provider.readSystemEnvProxy]: "no_proxy"="127.0.0.1,localhost,.sirti.net", targetUrl=https://server.codeium.com, bypass=false
I0204 17:21:55.209620 1456809 unleash.go:92] Initializing Unleash
I0204 17:21:55.859788 1456809 unleash.go:112] Successfully initialized Unleash
I0204 17:21:55.860038 1456809 server.go:297] Extension server port not specified; skipping extension server client creation
I0204 17:21:55.878393 1456809 web_server.go:258] Chat Web Server listening at ws://127.0.0.1:38935
E0204 17:21:55.903523 1456809 client_manager.go:275] Metadata is nil when trying to refresh user JWT
I0204 17:21:55.904330 1456809 server.go:528] Successfully created API server client
I0204 17:21:55.970799 1456809 server.go:546] Successfully initialized tokenizer
I0204 17:21:56.551471 1456809 server.go:721] Local search is enabled, will index local files.
I0204 17:21:56.551509 1456809 server.go:725] Using 1 indexer workers
I0204 17:21:56.617752 1456809 sqlite_faiss_db_client.go:64] Successfully created embedding search database in 66ms
I0204 17:21:56.618173 1456809 indexer.go:213] Using 4 embed workers
I0204 17:21:56.618231 1456809 search_provider.go:274] Successfully created and started indexer
I0204 17:21:56.618282 1456809 search_provider.go:304] Successfully created embedding search provider
I0204 17:21:57.872270 1456809 server.go:813] Successfully created completion provider
I0204 17:21:57.872324 1456809 server.go:862] Child process attempting to acquire lock file /home/<USER>/WPSOAP/tmp/52cd6341-1a06-4e03-be00-d2b73da382e8/codeium/manager/locks/child_lock_1738686114427245835_8700551045802880648
I0204 17:21:57.872470 1456809 server.go:870] Child process acquired lock file /home/<USER>/WPSOAP/tmp/52cd6341-1a06-4e03-be00-d2b73da382e8/codeium/manager/locks/child_lock_1738686114427245835_8700551045802880648
I0204 17:21:57.890201 1456809 server.go:356] LSP listening on random port at 35943
I0204 17:21:58.026175 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/COM
I0204 17:21:58.506426 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/COM
I0204 17:22:00.326735 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:08.529061 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/SD
I0204 17:23:10.091577 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/SD
I0204 17:23:10.365863 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:22.790944 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/SPHERE
I0204 17:23:22.903402 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/SPHERE
I0204 17:23:22.994138 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:25.063158 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/WPSOCORE
I0204 17:23:25.538226 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/WPSOCORE
I0204 17:23:26.370303 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:45.438322 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/WPSOAP
I0204 17:23:45.518110 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/WPSOAP
I0204 17:23:45.657733 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:50.468760 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/wpsoui
I0204 17:23:50.732632 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/wpsoui
I0204 17:23:50.966675 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:57.708960 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/ngx-restart-demo
I0204 17:23:57.727142 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/ngx-restart-demo
I0204 17:23:57.779749 1456809 lazy_model.go:31] Successfully created embedding store
I0204 17:23:58.270039 1456809 utils.go:228] Analyzing workspace files for /home/<USER>/js-ng-restart
I0204 17:23:58.286564 1456809 utils.go:282] Done analyzing workspace files for /home/<USER>/js-ng-restart
I0204 17:23:58.313272 1456809 lazy_model.go:31] Successfully created embedding store
E0204 21:17:55.346511 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 502, backing off (1.000000 times our interval)
W0204 21:17:55.861998 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 502
E0204 21:19:55.841696 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (2.000000 times our interval)
W0204 21:19:55.859459 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 503
E0204 21:22:57.211535 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0204 21:22:57.249938 1456809 unleash.go:62] Unleash error: Post "https://unleash.codeium.com/api/client/metrics": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0204 21:23:55.612598 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (3.000000 times our interval)
E0204 22:00:57.211630 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0204 22:11:57.211151 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 02:15:56.485852 1456809 api_server_client.go:337] GetUnleashContextFields error: unary response has zero messages
E0205 04:35:55.343712 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (1.000000 times our interval)
E0205 04:37:55.612691 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (2.000000 times our interval)
W0205 04:37:55.649966 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 503
W0205 04:39:55.857425 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 503
E0205 04:54:55.342747 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (3.000000 times our interval)
W0205 04:57:55.654854 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 503
E0205 04:58:55.633367 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (4.000000 times our interval)
W0205 05:02:55.653622 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 503
E0205 05:03:55.613166 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (5.000000 times our interval)
W0205 05:04:55.659248 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 503
E0205 05:23:55.344193 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (2.000000 times our interval)
E0205 05:41:57.212004 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 05:42:57.212329 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 05:42:57.250644 1456809 unleash.go:62] Unleash error: Post "https://unleash.codeium.com/api/client/metrics": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 05:43:55.816801 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (2.000000 times our interval)
E0205 05:49:55.341835 1456809 unleash.go:62] Unleash error: GET https://unleash.codeium.com/api/client/features returned status code 503, backing off (2.000000 times our interval)
E0205 08:04:56.185319 1456809 client_manager.go:294] Error getting user JWT: api server wire error: failed to connect to `user=exafunction database=postgres`:
	127.0.0.1:5432 (localhost): server error: FATAL: remaining connection slots are reserved for non-replication superuser connections (SQLSTATE 53300)
	[::1]:5432 (localhost): dial error: dial tcp [::1]:5432: connect: cannot assign requested address
E0205 08:06:02.383392 1456809 client_manager.go:294] Error getting user JWT: 502 Bad Gateway
W0205 10:07:56.945539 1456809 unleash.go:67] Unleash warning: https://unleash.codeium.com/api/client/metrics return 502
E0205 10:07:57.212267 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 10:08:57.211967 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 10:13:34.656380 1456809 api_server_client.go:240] Ping error: unary response has zero messages
E0205 10:51:19.778017 1456809 completion.go:216] Completion 131174 from source PROVIDER_SOURCE_AUTOCOMPLETE error: first response is not of type codeium_common_pb.StreamingCompletionResponse_CompletionInfo
E0205 10:51:19.778093 1456809 completions_state_manager.go:834] Error streaming completions: first response is not of type codeium_common_pb.StreamingCompletionResponse_CompletionInfo
E0205 12:32:43.041615 1456809 client_manager.go:294] Error getting user JWT: context deadline exceeded
E0205 12:33:50.995920 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 12:36:05.238607 1456809 unleash.go:62] Unleash error: Get "https://unleash.codeium.com/api/client/features": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
E0205 12:36:18.654687 1456809 unleash.go:62] Unleash error: Post "https://unleash.codeium.com/api/client/metrics": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
