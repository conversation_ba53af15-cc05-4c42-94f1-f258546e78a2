#
#file di configurazione di start_stop_daemon
#
# @users_management:log:$LOG_PATH/UsersManagement/users_management.log.$CURDATE
# @users_management:log:$LOG_PATH/UsersManagement/users_management.full.log.$CURDATE
users_management              arg   users_management
users_management              arg   --api-user=${WPSOAP_ADMIN_USER}
users_management              arg   --api-password=${WPSOAP_ADMIN_PASSWORD}
users_management              arg   --log-config=${COM}/etc/users_management.log4perl.conf
users_management              arg   --driver=API::ART::Driver::Users::Baseline
users_management              arg   --oBaselineConnectString=${SQLID_WPSOCORE}
users_management              arg   --manage=C
users_management              arg   --manage=U
users_management              arg   --manage=D
users_management              arg   --notify-email=${MAILADDR_BG}
users_management              arg   --daemon=60
users_management              arg   --transaction-mode=c
#
# @project_cities_group_creazione:log:$LOG_PATH/ProjectCitiesGroupCreazione/consumer.log.$CURDATE
# @project_cities_group_creazione:$LOG_PATH/ProjectCitiesGroupCreazione/ramq.log.$CURDATE
# @project_cities_group_creazione:log:$LOG_PATH/ProjectCitiesGroupCreazione/ramq_full.log.$CURDATE
project_cities_group_creazione          arg   ramq
project_cities_group_creazione          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
project_cities_group_creazione          arg   --daemon=300
project_cities_group_creazione          arg   --transaction-mode=c
project_cities_group_creazione          arg   ${ETC}/ramq/ProjectCitiesGroupCreazione.conf
#
# @ENEL_FTTH_crea_nuovi_buildings:log:$LOG_PATH/ENEL_FTTH_crea_nuovi_buildings.log.$CURDATE
# @ENEL_FTTH_crea_nuovi_buildings:log:$LOG_PATH/ENEL_FTTH_crea_nuovi_buildings.full.log.$CURDATE
ENEL_FTTH_crea_nuovi_buildings              arg   crea_nuovi_buildings
ENEL_FTTH_crea_nuovi_buildings              arg   --commit-after-every-building
ENEL_FTTH_crea_nuovi_buildings              arg   --daemon=900
ENEL_FTTH_crea_nuovi_buildings              arg   --transaction-mode=c
ENEL_FTTH_crea_nuovi_buildings              arg   ENEL
ENEL_FTTH_crea_nuovi_buildings              arg   FTTH
#
# @permesso_lavori_aggiorna_area_permessi_stato:log:$LOG_PATH/PermessoLavoriSiNFOUpdateAreaPermessiStato/consumer.log.$CURDATE
# @permesso_lavori_aggiorna_area_permessi_stato:log:$LOG_PATH/PermessoLavoriSiNFOUpdateAreaPermessiStato/ramq.log.$CURDATE
# @permesso_lavori_aggiorna_area_permessi_stato:log:$LOG_PATH/PermessoLavoriSiNFOUpdateAreaPermessiStato/ramq_full.log.$CURDATE
permesso_lavori_aggiorna_area_permessi_stato          arg   ramq
permesso_lavori_aggiorna_area_permessi_stato          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
permesso_lavori_aggiorna_area_permessi_stato          arg   --daemon=10
permesso_lavori_aggiorna_area_permessi_stato          arg   --transaction-mode=c
permesso_lavori_aggiorna_area_permessi_stato          arg   ${ETC}/ramq/PERMESSO_LAVORI/PermessoLavoriSiNFOUpdateAreaPermessiStato.conf
#
# @gestione_tt:log:$LOG_PATH/GestioneTT/consumer.log.$CURDATE
# @gestione_tt:log:$LOG_PATH/GestioneTT/ramq.log.$CURDATE
# @gestione_tt:log:$LOG_PATH/GestioneTT/ramq_full.log.$CURDATE
gestione_tt          arg   ramq
gestione_tt          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
gestione_tt          arg   --daemon=30
gestione_tt          arg   --transaction-mode=c
gestione_tt          arg   ${ETC}/ramq/AP/GestioneTT.conf
#
# @fiber_construction_sync:log:$LOG_PATH/fiber_construction_sync/consumer.log.$CURDATE
# @fiber_construction_sync:log:$LOG_PATH/fiber_construction_sync/ramq.log.$CURDATE
# @fiber_construction_sync:log:$LOG_PATH/fiber_construction_sync/ramq_full.log.$CURDATE
fiber_construction_sync          arg   ramq
fiber_construction_sync          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
fiber_construction_sync          arg   --daemon=300
fiber_construction_sync          arg   --transaction-mode=c
fiber_construction_sync          arg   ${ETC}/ramq/FiberConstructionSync.conf
#
# @service_management:log:$LOG_PATH/service_management/consumer.log.$CURDATE
# @service_management:log:$LOG_PATH/service_management/ramq.log.$CURDATE
# @service_management:log:$LOG_PATH/service_management/ramq_full.log.$CURDATE
service_management          arg   ramq
service_management          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
service_management          arg   --daemon=300
service_management          arg   --transaction-mode=c
service_management          arg   ${ETC}/ramq/ServiceManagement.conf
#
# @api_art_activity_source_permesso_building:log:$LOG_PATH/api-art-activity-source-PERMESSO_BUILDING.log.$CURDATE
# @api_art_activity_source_permesso_building:log:$LOG_PATH/api-art-activity-source-PERMESSO_BUILDING_full.log.$CURDATE
api_art_activity_source_permesso_building   arg   api-art-activity-source
api_art_activity_source_permesso_building   arg   --daemon=1
api_art_activity_source_permesso_building   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_permesso_building   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_source_permesso_building   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_source_permesso_building   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_source_permesso_building   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_permesso_building   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_permesso_building   arg   --log-idle=100
api_art_activity_source_permesso_building   arg   --log-level=INFO
api_art_activity_source_permesso_building   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_permesso_building   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_permesso_building   arg   --activity-type-name=PERMESSO_BUILDING
#
# @api_art_activity_source_permesso_lavori:log:$LOG_PATH/api-art-activity-source-PERMESSO_LAVORI.log.$CURDATE
# @api_art_activity_source_permesso_lavori:log:$LOG_PATH/api-art-activity-source-PERMESSO_LAVORI_full.log.$CURDATE
api_art_activity_source_permesso_lavori   arg   api-art-activity-source
api_art_activity_source_permesso_lavori   arg   --daemon=1
api_art_activity_source_permesso_lavori   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_permesso_lavori   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_source_permesso_lavori   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_source_permesso_lavori   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_source_permesso_lavori   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_permesso_lavori   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_permesso_lavori   arg   --log-idle=100
api_art_activity_source_permesso_lavori   arg   --log-level=INFO
api_art_activity_source_permesso_lavori   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_permesso_lavori   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_permesso_lavori   arg   --activity-type-name=PERMESSO_LAVORI
#
# @api_art_activity_source_lc_customer_project:log:$LOG_PATH/api-art-activity-source-LC_CUSTOMER_PROJECT.log.$CURDATE
# @api_art_activity_source_lc_customer_project:log:$LOG_PATH/api-art-activity-source-LC_CUSTOMER_PROJECT_full.log.$CURDATE
api_art_activity_source_lc_customer_project   arg   api-art-activity-source
api_art_activity_source_lc_customer_project   arg   --daemon=1
api_art_activity_source_lc_customer_project   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc_customer_project   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_source_lc_customer_project   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_source_lc_customer_project   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_source_lc_customer_project   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc_customer_project   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc_customer_project   arg   --log-idle=100
api_art_activity_source_lc_customer_project   arg   --log-level=INFO
api_art_activity_source_lc_customer_project   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc_customer_project   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc_customer_project   arg   --activity-type-name=LC_CUSTOMER_PROJECT
#
# @api_art_activity_source_lc06:log:$LOG_PATH/api-art-activity-source-LC06.log.$CURDATE
# @api_art_activity_source_lc06:log:$LOG_PATH/api-art-activity-source-LC06_full.log.$CURDATE
api_art_activity_source_lc06   arg   api-art-activity-source
api_art_activity_source_lc06   arg   --daemon=1
api_art_activity_source_lc06   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_source_lc06   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_source_lc06   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_source_lc06   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_source_lc06   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_source_lc06   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_source_lc06   arg   --log-idle=100
api_art_activity_source_lc06   arg   --log-level=INFO
api_art_activity_source_lc06   arg   --config=${ETC}/restART-config.ELK.yml
api_art_activity_source_lc06   arg   --client-id=${ART_INSTANCE_NAME}-global
api_art_activity_source_lc06   arg   --activity-type-name=LC06
#
# @api_art_activity_source_shrink:log:$LOG_PATH/api-art-activity-source-shrink.log.$CURDATE
# @api_art_activity_source_shrink:log:$LOG_PATH/api-art-activity-source-shrink_full.log.$CURDATE
api_art_activity_source_shrink   arg   api-art-activity-source-shrink
api_art_activity_source_shrink   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_source_shrink   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_source_shrink   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_source_shrink   arg   --log-level=INFO
api_art_activity_source_shrink   arg   --daemon=3600
api_art_activity_source_shrink   arg   --transaction-mode=c
api_art_activity_source_shrink   arg   --max-rows=1000000
#
# @api_art_activity_stream_permesso_building:log:$LOG_PATH/api-art-activity-stream-permesso-building.log.$CURDATE
# @api_art_activity_stream_permesso_building:log:$LOG_PATH/api-art-activity-stream-permesso-building_full.log.$CURDATE
api_art_activity_stream_permesso_building   arg   api-art-activity-stream-permesso-building
api_art_activity_stream_permesso_building   arg   --daemon=1
api_art_activity_stream_permesso_building   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_permesso_building   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_stream_permesso_building   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_stream_permesso_building   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_stream_permesso_building   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_permesso_building   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_permesso_building   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_permesso_building   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_permesso_building   arg   --exclude-history
api_art_activity_stream_permesso_building   arg   --log-idle=100
api_art_activity_stream_permesso_building   arg   --log-level=INFO
api_art_activity_stream_permesso_building   arg   --log-config=${ETC}/api-art-activity-stream-permesso-building.log4perl.conf
api_art_activity_stream_permesso_building   arg   --client-id=${ART_INSTANCE_NAME}-permesso-building
api_art_activity_stream_permesso_building   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-PERMESSO_BUILDING
api_art_activity_stream_permesso_building   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_permesso_lavori:log:$LOG_PATH/api-art-activity-stream-permesso-lavori.log.$CURDATE
# @api_art_activity_stream_permesso_lavori:log:$LOG_PATH/api-art-activity-stream-permesso-lavori_full.log.$CURDATE
api_art_activity_stream_permesso_lavori   arg   api-art-activity-stream-permesso-lavori
api_art_activity_stream_permesso_lavori   arg   --daemon=1
api_art_activity_stream_permesso_lavori   arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_permesso_lavori   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_stream_permesso_lavori   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_stream_permesso_lavori   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_stream_permesso_lavori   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_permesso_lavori   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_permesso_lavori   arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_permesso_lavori   arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_permesso_lavori   arg   --exclude-history
api_art_activity_stream_permesso_lavori   arg   --log-idle=100
api_art_activity_stream_permesso_lavori   arg   --log-level=INFO
api_art_activity_stream_permesso_lavori   arg   --log-config=${ETC}/api-art-activity-stream-permesso-lavori.log4perl.conf
api_art_activity_stream_permesso_lavori   arg   --client-id=${ART_INSTANCE_NAME}-permesso-lavori
api_art_activity_stream_permesso_lavori   arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-PERMESSO_LAVORI
api_art_activity_stream_permesso_lavori   arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc_customer_project:log:$LOG_PATH/api-art-activity-stream-lc-customer-project.log.$CURDATE
# @api_art_activity_stream_lc_customer_project:log:$LOG_PATH/api-art-activity-stream-lc-customer-project_full.log.$CURDATE
api_art_activity_stream_lc_customer_project  arg   api-art-activity-stream-lc-customer-project
api_art_activity_stream_lc_customer_project  arg   --daemon=1
api_art_activity_stream_lc_customer_project  arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc_customer_project  arg   --art-id=${WPSOAP_ARTID}
api_art_activity_stream_lc_customer_project  arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_stream_lc_customer_project  arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_stream_lc_customer_project  arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc_customer_project  arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc_customer_project  arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc_customer_project  arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc_customer_project  arg   --exclude-history
api_art_activity_stream_lc_customer_project  arg   --log-idle=100
api_art_activity_stream_lc_customer_project  arg   --log-level=INFO
api_art_activity_stream_lc_customer_project  arg   --log-config=${ETC}/api-art-activity-stream-lc-customer-project.log4perl.conf
api_art_activity_stream_lc_customer_project  arg   --client-id=${ART_INSTANCE_NAME}-lc-customer-project
api_art_activity_stream_lc_customer_project  arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC_CUSTOMER_PROJECT
api_art_activity_stream_lc_customer_project  arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_stream_lc06:log:$LOG_PATH/api-art-activity-stream-lc06.log.$CURDATE
# @api_art_activity_stream_lc06:log:$LOG_PATH/api-art-activity-stream-lc06_full.log.$CURDATE
api_art_activity_stream_lc06  arg   api-art-activity-stream-lc06
api_art_activity_stream_lc06  arg   --daemon=1
api_art_activity_stream_lc06  arg   --art-instance-name=${ART_INSTANCE_NAME}
api_art_activity_stream_lc06  arg   --art-id=${WPSOAP_ARTID}
api_art_activity_stream_lc06  arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_stream_lc06  arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_stream_lc06  arg   --kafka-host=${KAFKA_HOST}
api_art_activity_stream_lc06  arg   --kafka-port=${KAFKA_PORT}
api_art_activity_stream_lc06  arg   --schema-registry-host=${KAFKA_SCHEMA_REGISTRY_HOST}
api_art_activity_stream_lc06  arg   --schema-registry-port=${KAFKA_SCHEMA_REGISTRY_PORT}
api_art_activity_stream_lc06  arg   --exclude-history
api_art_activity_stream_lc06  arg   --log-idle=100
api_art_activity_stream_lc06  arg   --log-level=INFO
api_art_activity_stream_lc06  arg   --log-config=${ETC}/api-art-activity-stream-lc06.log4perl.conf
api_art_activity_stream_lc06  arg   --client-id=${ART_INSTANCE_NAME}-lc06
api_art_activity_stream_lc06  arg   --activity-topic=api-art-activity-${ART_INSTANCE_NAME}-source-LC06
api_art_activity_stream_lc06  arg   --offset-group=${ART_INSTANCE_NAME}
#
# @api_art_activity_attachments_source:log:$LOG_PATH/api-art-activity-attachments-source.log.$CURDATE
# @api_art_activity_attachments_source:log:$LOG_PATH/api-art-activity-attachments-source_full.log.$CURDATE
api_art_activity_attachments_source   arg   api-art-activity-attachments-source
api_art_activity_attachments_source   arg   --art-id=${WPSOAP_ARTID}
api_art_activity_attachments_source   arg   --art-user-name=${WPSOAP_SCRIPT_USER}
api_art_activity_attachments_source   arg   --art-user-pwd=${WPSOAP_SCRIPT_PASSWORD}
api_art_activity_attachments_source   arg   --art-index=${WPSO_APPLICATION_NAME}
api_art_activity_attachments_source   arg   --kafka-host=${KAFKA_HOST}
api_art_activity_attachments_source   arg   --kafka-port=${KAFKA_PORT}
api_art_activity_attachments_source   arg   --log-level=INFO
api_art_activity_attachments_source   arg   --config=${COM}/share/schemas/net/sirti/ict/art/api/api-art-activity-attachments.json
api_art_activity_attachments_source   arg   --client-id=${ART_INSTANCE_NAME}
api_art_activity_attachments_source   arg   --daemon=1
#
# @network_annullamento_fibercop:log:$LOG_PATH/NetworkAnnullamentoFibercop/consumer.log.$CURDATE
# @network_annullamento_fibercop:log:$LOG_PATH/NetworkAnnullamentoFibercop/ramq.log.$CURDATE
# @network_annullamento_fibercop:log:$LOG_PATH/NetworkAnnullamentoFibercop/ramq_full.log.$CURDATE
network_annullamento_fibercop          arg   ramq
network_annullamento_fibercop          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_annullamento_fibercop          arg   --daemon=5
network_annullamento_fibercop          arg   --transaction-mode=c
network_annullamento_fibercop          arg   ${ETC}/ramq/NetworkAnnullamentoFibercop.conf
#
# @lc02_booking:log:$LOG_PATH/LC02Booking/consumer.log.$CURDATE
# @lc02_booking:log:$LOG_PATH/LC02Booking/ramq.log.$CURDATE
# @lc02_booking:log:$LOG_PATH/LC02Booking/ramq_full.log.$CURDATE
lc02_booking          arg   ramq
lc02_booking          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lc02_booking          arg   ${ETC}/ramq/PERMESSO_BUILDING/LC02Booking.conf
lc02_booking          arg   --daemon=5
lc02_booking          arg   --transaction-mode=c
#
# @lc_customer_project_bot_speedark_response:log:$LOG_PATH/LcCustomerProjectBOTSpeedarkResponse/consumer.log.$CURDATE
# @lc_customer_project_bot_speedark_response:log:$LOG_PATH/LcCustomerProjectBOTSpeedarkResponse/ramq.log.$CURDATE
# @lc_customer_project_bot_speedark_response:log:$LOG_PATH/LcCustomerProjectBOTSpeedarkResponse/ramq_full.log.$CURDATE
lc_customer_project_bot_speedark_response          arg   ramq
lc_customer_project_bot_speedark_response          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lc_customer_project_bot_speedark_response          arg   ${ETC}/ramq/LC_CUSTOMER_PROJECT/LcCustomerProjectBOTSpeedarkResponse.conf
lc_customer_project_bot_speedark_response          arg   --daemon=600
lc_customer_project_bot_speedark_response          arg   --transaction-mode=c
#
# @lc_customer_project_documentation_timeout:log:$LOG_PATH/LcCustomerProjectDocumentationTimeout/consumer.log.$CURDATE
# @lc_customer_project_documentation_timeout:log:$LOG_PATH/LcCustomerProjectDocumentationTimeout/ramq.log.$CURDATE
# @lc_customer_project_documentation_timeout:log:$LOG_PATH/LcCustomerProjectDocumentationTimeout/ramq_full.log.$CURDATE
lc_customer_project_documentation_timeout          arg   ramq
lc_customer_project_documentation_timeout          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
lc_customer_project_documentation_timeout          arg   ${ETC}/ramq/LC_CUSTOMER_PROJECT/LcCustomerProjectDocumentationTimeout.conf
lc_customer_project_documentation_timeout          arg   --daemon=300
lc_customer_project_documentation_timeout          arg   --transaction-mode=c
#
# @rashrink:log:$LOG_PATH/rashrink.log.$CURDATE
rashrink          arg   rashrink
rashrink          arg   --connect-string=${SQLID_RA}
rashrink          arg   --retention=1
rashrink          arg   --max-sessions=100
rashrink          arg   --runs=0
rashrink          arg   --delay=30
rashrink          arg   --force
rashrink          arg   --do-commit
#
# @network_link_site:log:$LOG_PATH/NetworkLinkSite/consumer.log.$CURDATE
# @network_link_site:log:$LOG_PATH/NetworkLinkSite/ramq.log.$CURDATE
# @network_link_site:log:$LOG_PATH/NetworkLinkSite/ramq_full.log.$CURDATE
network_link_site          arg   ramq
network_link_site          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
network_link_site          arg   ${ETC}/ramq/LC06/NetworkLinkSite.conf
network_link_site          arg   --daemon=5
network_link_site          arg   --transaction-mode=c
#
# @api_art_notificationService:log:$LOG_PATH/API_ART_Activity_NotificationService/consumer.log.$CURDATE
# @api_art_notificationService:log:$LOG_PATH/API_ART_Activity_NotificationService/ramq.log.$CURDATE
# @api_art_notificationService:log:$LOG_PATH/API_ART_Activity_NotificationService/ramq_full.log.$CURDATE
api_art_notificationService          arg   ramq
api_art_notificationService          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_notificationService          arg   --daemon=3
api_art_notificationService          arg   --transaction-mode=c
api_art_notificationService          arg   ${ETC}/notification_service_ramq.conf
#
# @api_art_emailservice:log:$LOG_PATH/API_ART_EmailService/consumer.log.$CURDATE
# @api_art_emailservice:log:$LOG_PATH/API_ART_EmailService/ramq.log.$CURDATE
# @api_art_emailservice:log:$LOG_PATH/API_ART_EmailService/ramq_full.log.$CURDATE
api_art_emailservice          arg   ramq
api_art_emailservice          arg   --log-config=${COM}/TOOLS/etc/ramq.log4perl.conf
api_art_emailservice          arg   --daemon=3
api_art_emailservice          arg   --transaction-mode=c
api_art_emailservice          arg   ${ETC}/email_service_ramq.conf
