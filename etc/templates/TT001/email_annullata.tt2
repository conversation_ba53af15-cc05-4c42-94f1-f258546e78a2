<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <title>Notifica Aggiornamento Attività</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        p {
            font-size: 14px;
            color: #333;
        }
        .section {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .details {
            font-size: 14px;
            line-height: 1.5;
        }
        .label {
            font-weight: bold;
            color: #2980b9;
        }
        .footer {
            font-size: 12px;
            color: #7f8c8d;
            text-align: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <p>On [% last_variation_date %] user [% user %] updated activity [% activity_id %] of type [% activity_type %]</p>

        <h2>SUMMARY</h2>
        <div class="section">
            <p class="details"><span class="label">Activity Id:</span> [% activity_id %]</p>
            <p class="details"><span class="label">Creation Date:</span> [% creation_date %]</p>
            <p class="details"><span class="label">Creation User:</span> [% creation_user %]</p>
            <p class="details"><span class="label">Status:</span> [% status %]</p>
            <p class="details"><span class="label">Last Action:</span> [% last_action %]</p>
            <p class="details"><span class="label">Last Variation Date:</span> [% last_variation_date %]</p>
        </div>

        <h2>DETAILS</h2>
        <div class="section">
            [% FOREACH key IN actVal.keys %]
                [% IF actVal.$key %]
                    <p class="details"><span class="label">[% actProps.$key.LABEL %]:</span> [% actVal.$key %]</p>
                [% END %]
            [% END %]
        </div>
        <p>Link WPSO Ticket : <a href="[% linkActivity %]">[% linkActivity %]</a></p>
        <p class="footer">This is an automatically generated email, please do not reply.</p>
    </div>
</body>
</html>
