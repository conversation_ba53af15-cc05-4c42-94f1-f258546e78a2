<html>
<head>
    <meta charset="UTF-8">
    <title>Richiesta Apertura Ticket</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        p {
            font-size: 14px;
            color: #333;
        }
        .info {
            background: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        strong {
            color: #2980b9;
        }
        .ascii-art {
            font-family: monospace;
            color: #3498db; 
            font-size: 8px; 
            margin-top: 20px;
            line-height: 1.2; 
            white-space: pre; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Richiesta Apertura Ticket</h2>
        <p>Salve,</p>
        <p>Si richiede Apertura Ticket.</p>

        <div class="info">
            <p><strong>Ambito:</strong> [% scope %]</p>
            <p><strong>TIPO WO:</strong> Aggiornamento impresa</p>
            <p><strong>Ambito WO:</strong> [% scopeWo %]</p>
            <p><strong>Specializzazione WO:</strong> [% specialisationWO %]</p>
            <p><strong>AOR/SETTORE/CENTRALE:</strong> [% AOR %] / [% sector %] / [% central %]</p>
            <p><strong>Nome WO:</strong> [% nameWO %]</p>
            [% IF FictitiousOL %]<p><strong>OL Fittizio:</strong> [% FictitiousOL %]</p>[% END %]
            <p><strong>NTW SAP:</strong> [% networkId %]</p>
            [% IF qtyPTELocked %]<p><strong>Numero PTE/ROE Bloccati:</strong> [% qtyPTELocked %]</p>[% END %]
            <p><strong>Utenza segnalante:</strong> [% reportingUser %]</p>
            <p><strong>Step di dettaglio della manualita' eseguita dall'utente:</strong> [% stepOperationPerformed %]</p>
            <p><strong>Descrizione del problema riscontrato:</strong> [% problemDescription %]</p>
        </div>
        <p>Link WPSO Ticket : <a href="[% linkActivity %]">[% linkActivity %]</a></p>
        <p>Cordiali Saluti</p>
        <p>[% userName %] [% userSurname %]<p>
        [% IF telephoneContact %]<p> Tel: [% telephoneContact %]</p>[% END %]
        <p>Si allega:</p>
        <p>1. File Word con dettagli errore riscontranto</p>
        <p>2. File di Log</p>    
</div>
</body>
</html>
