#### $HOME/etc/art.conf

SetEnv ART_DB_CONNECT_STRING    ${SQLID_WWW}
SetEnv ART_DB_RAISEERROR        1
SetEnv ART_DB_AUTOCOMMIT        1
SetEnv ART_DB_PRINTERROR        1
SetEnv ART_DB_DEBUG             1

PassEnv ART_BINDING_SUDO_GROUP
PassEnv ART_ENVIRONMENT
PassEnv ART_APPLICATION_NAME
PassEnv ART_DEFAULT_MAIL_SENDER
PassEnv ART_DEFAULT_REPLY_TO

PerlRequire                     ${ART_LIB}/ART/startup.pl
ErrorDocument                   500 /errore.html

PassEnv ART_REPOSITORY

PassEnv ETC
PassEnv COM
PassEnv ROOT
PassEnv TMP

#### PROJECT SPECIFICS

PassEnv WPSOAPWS_WS_ROUTES_PREFIX
PassEnv WPSOUI_HOMEPAGE

PassEnv SERVICE_INSTANCES

PassEnv WPSOCORE_USER_SID
PassEnv WPSOCORE_USER_USERNAME
PassEnv WPSOCORE_USER_PASSWORD

PassEnv WPSOAP_USER_SID
PassEnv WPSOAP_USER_USERNAME
PassEnv WPSOAP_USER_PASSWORD

PassEnv WPSOWORKS_USER_SID
PassEnv WPSOWORKS_USER_USERNAME
PassEnv WPSOWORKS_USER_PASSWORD

PassEnv WS_BASE_URL
#PassEnv SINFO_GET_BUILDINGS_MASK_RESOURCE
#PassEnv SINFO_PUT_AREA_PERMESSI_MASK_RESOURCE

