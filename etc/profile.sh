#!/bin/bash

function _warn {
	echo -e "\n*** WARNING ***\n$1\n"
}

# usage: _dir_check HOME VAR ETC ...
function _dir_check {
	for P in $*
	do
		eval X='$'$P
		[[ -d "$X" ]] || _warn "Missing directory $P=$X"
		unset X
	done
	unset P
}
function _file_check {
	for P in $*
	do
		eval X='$'$P
		[[ -r "$X" ]] || _warn "Missing or non readable file $P=$X"
		unset X
	done
	unset P
}

#
#  definizione dell' ambiente
#

if [[ "$1" != "" ]]; then
	if [[ ! -d "$1" ]]; then
		_warn "'$1' not a directory!"
	fi
	export HOME="$1"
fi

if [[ "$HOME" == "" ]]; then
	_warn "Environment HOME not defined"
fi
export CURDATE=$(date +%Y%m%d)

export NAMESPACE="WPSOAP"
export ROOT="$HOME/$NAMESPACE"
export ETC="$ROOT/etc"
export BIN="$ROOT/bin"
export SHARE="$ROOT/share"
export VAR="$ROOT/var"
export TMP="$ROOT/tmp"
export TMPDIR="$TMP"
export LIB="$ROOT/lib"
export LIB64="$ROOT/lib64"
export BINDING_ACTIVITY_PATH="$SHARE/perl5/API/ART/APP/Activity"
export PERLAPPL="$SHARE/script"
export LOG_PATH="$VAR/logs"
export LOCK_PATH="$VAR/lock"
export WWW="$SHARE/www"
export COM="$HOME/COM"
export REPOS="${VAR}/repository"

export ART_APPLICATION_NAME=$NAMESPACE
export ART_ENVIRONMENT="development"
export ART_HOME="$HOME/ART/Appl"
export ART_LIB="$ART_HOME"
export ART_REPOSITORY="$VAR/art_repository"
export ART_REPOSITORY_EMAIL="$ART_REPOSITORY/email"
export ART_REPOSITORY_TMP="$ART_REPOSITORY/tmp"
export ART_REPOSITORY_SHARED_RESOURCES="$ART_REPOSITORY/shared_resources"
export ART_REPOSITORY_TMP_TMPFILES="$ART_REPOSITORY_TMP/tmpfiles"
export ART_BINDING_SUDO_GROUP="ADMIN"

###### ORACLE ################################################################
export LANG=it_IT.UTF8
export NLS_LANG="ITALIAN_ITALY.AL32UTF8"
export NLS_DATE_FORMAT='dd/mm/yyyy hh24:mi:ss'
export NLS_TIMESTAMP_TZ_FORMAT='yyyy-mm-dd"T"hh24:mi:ss.fftzh:tzm'
export LD_LIBRARY_PATH="${COM}/lib:${LIB64}:${LIB}:${LD_LIBRARY_PATH}"
export PERL="$(which perl) -MCarp=verbose"

###### CONTROLLO DIRECTORIES #################################################
_dir_check ROOT ETC BIN SHARE VAR TMP PERLAPPL LOG_PATH LOCK_PATH WWW COM REPOS ART_REPOSITORY ART_REPOSITORY_EMAIL ART_REPOSITORY_TMP ART_REPOSITORY_SHARED_RESOURCES ART_REPOSITORY_TMP_TMPFILES


###### PERL ##################################################################
P5L=""
for P in \
	${SHARE}/perl5 \
	${COM}/share/perl5 \
	$ART_LIB
do
	[ -d "$P" ] && P5L="${P5L}:${P}"
done
unset P

P5L=${P5L:1}
if [[ "$PERL5LIB" != "" ]] ; then
	PERL5LIB="${P5L}:${PERL5LIB}"
else
	PERL5LIB="${P5L}"
fi
export PERL5LIB

export PATH="$BIN:${COM}/bin:$PATH"

# ART WEB ENVIRONMENT
export ART_WWW="$ART_HOME/web"
export APACHE_ART_PORT=10086
export APACHE_ART_USER=$(id -nu)
export APACHE_ART_GROUP=$(id -ng)
export APACHE_ART_BASE="$VAR/apache"
export APACHE_ART_SESSION_PATH="$APACHE_ART_BASE/session/WPSOAPART"
export APACHE_ART_LOG_PATH="$APACHE_ART_BASE/logs/WPSOAPART/$(hostname)"
export APACHE_ART_PID_PATH="$APACHE_ART_LOG_PATH"
export APACHE_ART_PID_FILE="$APACHE_ART_PID_PATH/httpd.WPSOAPART.pid"
export APACHE_ART_LOCK_PATH="$APACHE_ART_BASE/lock/$(hostname)"
export APACHE_ART_LOCK_FILE="$APACHE_ART_LOCK_PATH/httpd.WPSOAPART.lock"
export SSO_APPLICATION_NAME="wpsoaplegacy"
#export SSO_CONFIG_INI_PATH="$ART_LIB/ART/sso_config.ini"
#export SSO_FE_REWRITE_PREFIX=""

_dir_check ART_WWW APACHE_ART_BASE APACHE_ART_SESSION_PATH APACHE_ART_LOG_PATH APACHE_ART_PID_PATH APACHE_ART_LOCK_PATH

# WSART WEB ENVIRONMENT
export DANCER_ENVIRONMENT="$ART_ENVIRONMENT.$(hostname)"
export DANCER_CONFDIR="$ETC/WSART/"
export WSART_HOME="$ROOT/share/perl5/WebService/WSART"
export WSART_SESSION_DURATION="7200" # il valore della DURATION deve essere uguale alla chiave ART.TIMEOUT_SESSIONI in CONFIG_ART
export ART_WS_ROUTES_PREFIX="/wpso/ap/api/art" # default /api/art
export APACHE_WSART_PORT=10170
export APACHE_WSART_USER=$(id -nu)
export APACHE_WSART_GROUP=$(id -ng)
export APACHE_WSART_BASE="$VAR/apache"
export APACHE_WSART_SESSION_PATH="$APACHE_WSART_BASE/session/WSART/$(hostname)"
export APACHE_WSART_LOG_PATH="$APACHE_WSART_BASE/logs/WSART/$(hostname)"
export APACHE_WSART_PID_FILE="$APACHE_WSART_LOG_PATH/httpd.WSART.pid"
export APACHE_WSART_LOCK_FILE="$VAR/lock/$(hostname)/httpd.WSART.lock"
export ART_WS_AUTH_BY_JWT="1"
export ART_WS_AUTH_BY_JWT_MODE="slave"
export ART_WS_AUTH_BY_JWT_SESSION_EXPIRE="" # numero di secondi di inattività dopo i quali la sessione scade, solo in modalità peer
export ART_WS_AUTH_BY_JWT_CACHE_EXPIRE="3600" # numero di secondi di inattività dopo i quali la cache viene cancellata
export ART_WS_AUTH_BY_PROXY=1
export ART_ALLOW_LOGIN_WITHOUT_PASSWORD=1
export ART_WS_CACHE_MEMCACHED_SERVER="drvaswp001.corp.sirti.net:11219"
export ART_WS_SESSION_EXPIRE="86400" # numero di secondi in un giorno
export ART_WS_CACHE_EXPIRE="604800" # numero di secondi in una settimana
export ART_WS_CORS_ALLOWED_ORIGINS=""

_dir_check WSART_HOME APACHE_WSART_BASE APACHE_WSART_SESSION_PATH APACHE_WSART_LOG_PATH

export WPSOAPWS_WS_ROUTES_PREFIX='/wpso/ap' # default /wpso/ap

export WPSO_HOMEPAGE="https://services.sirti.net/artsoen"
export WPSOUI_HOMEPAGE="${WPSO_HOMEPAGE}/WPSOUI"

###############################################################################
#
# FINE SEZIONE CONDIVISIBILE
#
###############################################################################

export MAILADDR_BG="<EMAIL>,<EMAIL>"

export WPSOAP_ARTID="SIRTI_WPSOAP"
export WPSOAP_SCRIPT_USER="root" #WPSOAP_SCRIPT_USER
export WPSOAP_SCRIPT_PASSWORD="pippo123"
if [[ "$WPSOAP_SCRIPT_PASSWORD" == "" ]]; then
	_warn "Missing WPSOAP_SCRIPT_PASSWORD"
fi

export WPSOAP_ADMIN_USER="root"
export WPSOAP_ADMIN_PASSWORD="pippo123"
if [[ "$WPSOAP_ADMIN_PASSWORD" == "" ]]; then
        _warn "Missing WPSOAP_ADMIN_PASSWORD"
fi

export ARTID=$WPSOAP_ARTID
export ART_SCRIPT_USER=$WPSOAP_SCRIPT_USER
export ART_SCRIPT_PASSWORD=$WPSOAP_SCRIPT_PASSWORD

export ART_DEFAULT_MAIL_SENDER="\"$ART_APPLICATION_NAME\" <<EMAIL>>"
export ART_DEFAULT_REPLY_TO=$ART_DEFAULT_MAIL_SENDER
export ART_EMAIL_SERVICE_DONT_SEND=1 # se true il demone di invio mail esegue il fetch delle RA ma non invia le email

export ART_DB_DEBUG=0

# disabilita uso delle PW criptate
export ART_NO_DB_PWD=1

export ART_API_KEY_JWT_SECRET="qwsdfvbnmk123"

export SQLID_WWW=ap_art/ap_art@dwpsoap
export SQLID_SUPPORT=ap/ap@dwpsoap
export SQLID_RA=REMOTE_ACTIVITY/remote_activity@dwpsoap
export SQLID_REPORT=ap_rpt/ap_rpt@dwpsoap
export SQLID=$SQLID_WWW
export SQLID_WPSO=en_ftth/en_ftth@penftth
export SQLID_WPSOCORE=core_art/core_art@dwpsocore

export LOG4PERL_CONF="$ETC/wpsoap.log4perl.conf"
export FULL_LOG_FILE=""
export SSD_CONFIG_FILE="$ETC/daemon.conf"
export DAEMONS_ACTIVE_CONFIG_FILE="$ETC/daemons.active"
_file_check LOG4PERL_CONF SSD_CONFIG_FILE

export ART_AUTH_TYPE="LOCAL"
export RADIUS_SERVER="***********" # intranet
export RADIUS_SECRET="abcdfef15032012abcdfef" # intranet
export RADIUS_DOMAIN="@corp.sirti.net" # intranet
export RADIUS_SERVER_EXT="***********" # extranet
export RADIUS_SECRET_EXT="ghilmnp22102014ghilmnp" # extranet
export RADIUS_DOMAIN_EXT="@partner.sirti.net" # extranet
export RADIUS_SERVER_BKP="***********" # intranet bkp
export RADIUS_SECRET_BKP="abcdfef15032012abcdfef" # intranet bkp
export RADIUS_SERVER_EXT_BKP="***********" # extranet bkp
export RADIUS_SECRET_EXT_BKP="ghilmnp22102014ghilmnp" # extranet bkp

#export CLAMAV_DAEMON_PORT=/run/clamd.scan/clamd.sock
export CLAMAV_HOST=prvmrrclam001.corp.sirti.net
export CLAMAV_PORT=3310

export SERVICE_INSTANCES="WPSOCORE,WPSOAP,WPSOWORKS"

export WPSOCORE_USER_SID="5b37aba606dbec6abf7d0307377cdc85"
export WPSOCORE_USER_USERNAME="wpsocore"
export WPSOCORE_USER_PASSWORD="pippo123"
if [[ "$WPSOCORE_USER_PASSWORD" == "" ]]; then
	_warn "Missing WPSOCORE_USER_PASSWORD"
fi
export WPSOAP_USER_SID="de6c2b5d131218d6543241e4c9adc4ca"
export WPSOAP_USER_USERNAME="wpsoap"
export WPSOAP_USER_PASSWORD="pippo123"
if [[ "$WPSOAP_USER_PASSWORD" == "" ]]; then
	_warn "Missing WPSOAP_USER_PASSWORD"
fi
export WPSOWORKS_USER_SID="81ae39a935d496e8bef2320a3451181f"
export WPSOWORKS_USER_USERNAME="wpsoworks"
export WPSOWORKS_USER_PASSWORD="pippo123"
if [[ "$WPSOWORKS_USER_PASSWORD" == "" ]]; then
	_warn "Missing WPSOWORKS_USER_PASSWORD"
fi

export WPSO_APPLICATION_NAME="wpso"

export WS_BASE_URL="http://dlvgwpol001.ict.sirti.net:10172"
# export SINFO_GET_BUILDINGS_MASK_RESOURCE="/sirti/api/private/sinfo/customers/%s/contracts/%s/design/projects/%s/buildings"
# export SINFO_PUT_AREA_PERMESSI_MASK_RESOURCE="/sirti/api/private/sinfo/customers/%s/contracts/%s/design/projects/%s/permits-areas/%s/status"

# BOTGWWS WEB ENVIRONMENT
export BOTGWWS_ARTID=$WPSOAP_ARTID
export BOTGWWS_SCRIPT_USER=$WPSOAP_SCRIPT_USER
export BOTGWWS_SCRIPT_PASSWORD=$WPSOAP_SCRIPT_PASSWORD
export BOTGWWS_DANCER_ENVIRONMENT="$ART_ENVIRONMENT.$(hostname)" # SetEnv DANCER_ENVIRONMENT
export BOTGWWS_DANCER_CONFDIR="$ETC/BOTGWWS/" # SetEnv DANCER_CONFDIR
export BOTGWWS_HOME="$ROOT/share/perl5/WebService/WS"
export BOTGWWS_ROUTES_PREFIX="/botgwws/rest" # SetEnv WS_ROUTES_PREFIX - default /rest
export BOTGWWS_SUPPORT_DB=1 # SetEnv WS_SUPPORT_DB
export BOTGWWS_REPOSITORY_SHARED_RESOURCES=$ART_REPOSITORY_SHARED_RESOURCES
export APACHE_BOTGWWS_PORT=10097
export APACHE_BOTGWWS_USER=$(id -nu)
export APACHE_BOTGWWS_GROUP=$(id -ng)
export APACHE_BOTGWWS_BASE="$VAR/apache"
export APACHE_BOTGWWS_SESSION_PATH="$APACHE_BOTGWWS_BASE/session/BOTGWWS/$(hostname)"
export APACHE_BOTGWWS_LOG_PATH="$APACHE_BOTGWWS_BASE/logs/BOTGWWS/$(hostname)"
export APACHE_BOTGWWS_PID_FILE="$APACHE_BOTGWWS_LOG_PATH/httpd.BOTGWWS.pid"
export APACHE_BOTGWWS_LOCK_FILE="$VAR/lock/$(hostname)/httpd.BOTGWWS.lock"

_dir_check BOTGWWS_HOME APACHE_BOTGWWS_BASE APACHE_BOTGWWS_SESSION_PATH APACHE_BOTGWWS_LOG_PATH BOTGWWS_REPOSITORY_SHARED_RESOURCES

export BOTGWWS_WS_ROUTES_PREFIX="/botgwws/botgw" # default /botgwws/botgw
export BOTGWWS_URL_FOR_UPLOAD_FILES="http://pvmas090.ict.sirti.net:${APACHE_BOTGWWS_PORT}${BOTGWWS_WS_ROUTES_PREFIX}/files"

export CUSTOMER_PROJECT_DOCUMENTATION_TIMEOUT=604800 # corrisponde a 7 giorni in secondi

export GMAP_CLIENT="gme-sirtispa"
export GMAP_KEY="5O8ypjMBVY5U_dtcwrhtwV243NY="
if [[ "$GMAP_KEY" == "" ]]; then
        _warn "Missing GMAP_KEY"
fi

export TT001_NETWORK_RESOURCE="/sirti/api/private/wpso/customers/%s/so/projects/%s"
export TT001_NETWORK_API_KEY="eyJhbGciOiJIUzI1NiJ9.eyJ1c2VybmFtZSI6IlJPT1QifQ.Uas_xpF7qk80lZjo5mGQ05CveqBNpyfbX7ye97HxYSE"
export TT001_RECIPIENT_MAIL="<EMAIL>"


### sezione ELK ###
export KAFKA_HOST="drvkkwp001.corp.sirti.net"
export KAFKA_PORT="9092"
export KAFKA_CONNECT_PROTOCOL="http"
export KAFKA_CONNECT_HOST="drvkkwp001.corp.sirti.net"
export KAFKA_CONNECT_PORT="8083"
export KAFKA_REST_PROTOCOL="http"
export KAFKA_REST_HOST="drvkkwp001.corp.sirti.net"
export KAFKA_REST_PORT="8082"
export KAFKA_SCHEMA_REGISTRY_PROTOCOL="http"
export KAFKA_SCHEMA_REGISTRY_HOST="drvkkwp001.corp.sirti.net"
export KAFKA_SCHEMA_REGISTRY_PORT="8081"
# export KAFKA_TOPIC_PARTITIONS=5

export CONNECT_JKS_TRUSTSTORE="/etc/kafka/connect-ca/elastic.truststore.drvkkapp002.jks"
export CONNECT_JKS_TRUSTSTORE_PASSWORD="pippo123"
export ELASTICSEARCH_PROTOCOL="https"
export ELASTICSEARCH_HOST="drvkkap002"
export ELASTICSEARCH_PORT="9200"
export ELASTICSEARCH_USERNAME="wdso"
export ELASTICSEARCH_PASSWORD="pippo123"
if [[ "$ELASTICSEARCH_PASSWORD" == "" ]]; then
	_warn "Missing ELASTICSEARCH_PASSWORD"
fi

export ART_INSTANCE_NAME="wdsoap"
export STREAMER_FREQUENCY=1

export ELASTICSEARCH_NODES="$ELASTICSEARCH_PROTOCOL://$ELASTICSEARCH_USERNAME:$ELASTICSEARCH_PASSWORD@$ELASTICSEARCH_HOST:$ELASTICSEARCH_PORT"
export ELASTICSEARCH_SYNC_DELTA_TIME="60" #tempo in secondi
export ELASTICSEARCH_INSTANCE_NAME="$ART_INSTANCE_NAME"
export ELASTICSEARCH_CONFIG_FILE="$ETC/restART-config.ELK.yml"

### FINE sezione EKK ###

export RESTART_HOMEPAGE="https://services.sirti.net/authp/sirti/wpso/ui/"
### FINE sezione EKK ###

#
# Esegue una query sul db specificato dalla	connect-string
#
function oracle_query {
	local cs="$1" #	connect-string
	[[ "$cs" ==	"" ]] && echo -e "Missing connect-string!\n\nUsage: oracle_query CONNECT_STRING QUERY" && return 1
	local sql="$2" # query
	if [[ "$sql" == "" ]]; then
		# se la query non e' stata passata tento di leggere da STDIN
		sql=$(
			while read; do
				echo $REPLY
			done
		)
	fi
	[[ "$sql" == ""	]] && echo -e "Missing query!\n\nUsage: oracle_query CONNECT_STRING QUERY" && return 1
	local t="$TMP/$$_$(date +%Y%m%d%H%M%S%N).tmp"
	local RC=0
	(
		cat	<<!
			set	line 10000
			set	feedback off
			set	heading	off
			set	autoprint off
			set	echo off
			set	headsep	off
			set	newpage	none
			set	serverout off
			set	showmode off
			set	sqlnumber off
			set	termout	off
			set	trimspool on
			set	verify off

			$sql
			;

!
	) |	 sqlplus -S	$cs	> $t ||	RC=$?

	[[ "$RC" ==	"0"	]] && grep 'ERROR at line' $t >	/dev/null && { cat $t  >&2;	RC=$ERR_SYS; }
	[[ "$RC" ==	"0"	]] && grep '^ORA-' $t >	/dev/null && { cat $t  >&2;	RC=$ERR_SYS; }
	[[ "$RC" ==	"0"	]] && cat $t
	[ "$RC" -ne 0 ] && cat $t >&2
	rm -f $t
	return $RC
}

# Cancello le funzioni per non essere disponibili nel prompt
unset -f _dir_check
unset -f _file_check
