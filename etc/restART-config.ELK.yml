#
# Questo file di esempio descrive come deve essere costruito l'elenco dei Tipi Attività da sinkare in Elasticsearch attraverso Apache Kafka.
# Il file, specifico per ogni istanza restART, deve essere utilizzato in fase di setup dallo script `api-art-activity-stack-setup.pl` (parametro --config)
# Per ogni Tipo Attività è possibile indicare:
#
#  - name: il nome del tipo attività (case sensitive)
#  - partitions_count: il numero di partizioni con cui sarà creata la relativa topic
#  - replication_factor: il fattore di replica della topic
#  - use_global_topic: se vale 1, per il tipo attività non sarà creata una topic dedicata ma i relativi eventi confluiranno nella source topic globale (default: 1)
#  - retention_ms: la retention (in millisecondi) degli eventi della topic (se non specificata erediterà il valore di default impostato in `api-art-activity-stack-setup.pl`)
# 
-
  name: PERMESSO_BUILDING
  partitions_count: 10
  replication_factor: 1
# 30 days
  retention_ms: 2592000000
  use_global_topic: 0
  ws:
    search: 1
-
  name: PERMESSO_LAVORI
  partitions_count: 10
  replication_factor: 1
# 30 days
  retention_ms: 2592000000
  use_global_topic: 0
  ws:
    search: 1
-
  name: LC_CUSTOMER_PROJECT
  partitions_count: 10
  replication_factor: 1
# 30 days
  retention_ms: 2592000000
  use_global_topic: 0
  ws:
    search: 1
-
  name: LC06
  partitions_count: 10
  replication_factor: 1
# 1 year
  retention_ms: 31536000000
  use_global_topic: 0
  ws:
    search: 1
-
  name: TT001
  partitions_count: 2
  replication_factor: 1
# 1 year
  retention_ms: 31536000000
  use_global_topic: 0
  ws:
    search: 1
# AP_LC
# BUILDING_LC
