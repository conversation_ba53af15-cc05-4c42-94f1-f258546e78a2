
{
  	ID_TAG				:	"API_ART_EmailService",
   
  	SESSION_TYPE		:	"TARGET",
 
	SESSION_DESCRIPTION	:	"API::ART Email Service Consumer",

	CLASS				:	"API::ART::MQ::Consumer::EmailService",

	SOURCE_CONTEXT		:	"${ARTID}",
	TARGET_CONTEXT		:	"API::ART::Email",
	
	DB : {
		ART : {
			ID : "${ARTID}",
			USER : "${ART_SCRIPT_USER}",
			PASSWORD : "${ART_SCRIPT_PASSWORD}",
			DEBUG : "${ART_DB_DEBUG}",
			AUTOSAVE : 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "no",
	
	WORK_CONTEXT        : {
		ART_DEFAULT_MAIL_SENDER : "${ART_DEFAULT_MAIL_SENDER}",
		ART_EMAIL_SERVICE_DONT_SEND : "${ART_EMAIL_SERVICE_DONT_SEND}"
	}
}


