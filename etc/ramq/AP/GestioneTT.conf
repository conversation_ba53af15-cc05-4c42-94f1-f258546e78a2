{
	ID_TAG				: "GestioneTT",
	SESSION_TYPE		: "TARGET",
	SESSION_DESCRIPTION	: "Segnalazione ad AP che si vuole aprire un nuovo TT su una determinata area permessi",
	CLASS				: "WPSOAP::MQ::Consumer::AP::GestioneTT",
	SOURCE_SERVICE		: "ENFTTH_CORE",
	SOURCE_CONTEXT		: "CORE_TT",
	TARGET_SERVICE		: "ENFTTH_AP",
	TARGET_CONTEXT		: "CORE_TT",
	DB					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE	: "yes",
	WORK_CONTEXT		: {}
}
