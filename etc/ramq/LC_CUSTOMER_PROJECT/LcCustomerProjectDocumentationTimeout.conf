
{
	ID_TAG				:	"LcCustomerProjectDocumentationTimeout",
	SESSION_TYPE		:	"TARGET",
	SESSION_DESCRIPTION	:	"Customer Project Documentation Timeout",
	CLASS				:	"WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectDocumentationTimeout",
	SOURCE_CONTEXT		:	"CUSTOMER_PROJECT",
	TARGET_CONTEXT		:	"CUSTOMER_PROJECT",
	DB 					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE : "yes",
	WORK_CONTEXT : {}
}
