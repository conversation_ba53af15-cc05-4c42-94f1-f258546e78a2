{
	ID_TAG				: "LcCustomerProjectBOTSpeedarkResponse",
	SESSION_TYPE		: "TARGET",
	SESSION_DESCRIPTION	: "Gestione response ",
	CLASS				: "WPSOAP::MQ::Consumer::LC_CUSTOMER_PROJECT::LcCustomerProjectBOTSpeedarkResponse",
	SOURCE_SERVICE		: "BOT",
	SOURCE_CONTEXT		: "SPEEDARK",
	TARGET_SERVICE		: "ENFTTH_AP",
	TARGET_CONTEXT		: "CUSTOMER_PROJECT",
	DB					: {
		ART : {
			ID			: "${ARTID}",
			USER		: "${ART_SCRIPT_USER}",
			PASSWORD	: "${ART_SCRIPT_PASSWORD}",
			DEBUG		: "${ART_DB_DEBUG}",
			AUTOSAVE	: 0
		}
		,COMMIT_MODE: 'message'
	},
	USE_LOOKUP_TABLE	: "yes",
	WORK_CONTEXT		: {},
	RA_DBLINK           :       "ENFTTH_CORE"
}
