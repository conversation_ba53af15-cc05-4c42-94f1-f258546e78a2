digraph "TT001" {
	
	graph[ratio=auto, center=1, fontname="Calibri,Helvetica", fontsize="14", style="bold", label="TT001\n ", labelloc="t"];
	node[shape=ellipse, fontname="Calibri,Helvetica", fontsize="10", fillcolor="grey95", style=filled];
	edge[fontname="Calibri,Helvetica", fontsize="9", color="grey20"];

	"START" -> "APERTA" [label="APERTURA\nadmin\nat\nservice",labeltooltip="No properties",style="solid"]
	"___ANY_STATUS___" -> "___ANY_STATUS___" [labeltooltip="No properties",label="AGGIORNAMENTO DATI\nadmin\nBD",style="dashed"]
	"APERTA" -> "ANALISI" [style="solid",labeltooltip="No properties",label="DETTAGLI\nANOMALIA\nDAPHNE\nadmin\nat\nservice"]
	"APERTA" -> "ANALISI" [style="solid",labeltooltip="No properties",label="DETTAGLI\nANOMALIA\nNGNEER\nadmin\nat\nservice"]
	"APERTA" -> "ANALISI" [label="DETTAGLI\nANOMALIA UNICA\nadmin\nat\nservice",labeltooltip="No properties",style="solid"]
	"ANALISI" -> "ANALISI" [labeltooltip="No properties",style="solid",label="ASSEGNAZIONE\nadmin\nat\nBD"]
	"ATTESA_PRESA_IN_CARICO_CLIENTE" -> "ANALISI" [labeltooltip="No properties",style="solid",label="NON ACCETTATO\nadmin\nat\nBD"]
	"ATTESA_PRESA_IN_CARICO_CLIENTE" -> "IN_LAVORAZIONE_CLIENTE" [labeltooltip="No properties",label="PRESA IN\nCARICO CLIENTE\nadmin\nat\nBD",style="solid"]
	"IN_LAVORAZIONE_CLIENTE" -> "RISOLUZIONE_LAVORAZIONE_CLIENTE" [label="CHIUSURA TT\nCLIENTE\nadmin\nat\nBD",labeltooltip="No properties",style="solid"]
	/* "ANALISI" -> "ANALISI" [labeltooltip="No properties",label="DETTAGLI\nANOMALIA\nDAPHNE\nadmin\nat\nservice",style="solid"]
	"ANALISI" -> "ANALISI" [label="DETTAGLI\nANOMALIA\nNGNEER\nadmin\nat\nservice",labeltooltip="No properties",style="solid"]
	"ANALISI" -> "ANALISI" [labeltooltip="No properties",label="DETTAGLI\nANOMALIA UNICA\nadmin\nat\nservice",style="solid"]
	 */
	"ANALISI" -> "ATTESA_PRESA_IN_CARICO_CLIENTE" [label="APERTURA TT\nCLIENTE\nadmin\nat\nBD",labeltooltip="No properties",style="solid"]
	"ANALISI" -> "RISOLTA" [labeltooltip="No properties",label="RISOLUZIONE\nadmin\nat\nBD",style="solid"]
	"RISOLUZIONE_LAVORAZIONE_CLIENTE" -> "RISOLTA_CLIENTE" [labeltooltip="No properties",style="solid",label="VALIDAZIONE OK\nadmin\nat\nservice\nBD"]
	"RISOLUZIONE_LAVORAZIONE_CLIENTE" -> "NON_RISOLTA" [style="solid",labeltooltip="No properties",label="VALIDAZIONE KO\nadmin\nat\nservice\nBD"]
	"ANALISI" -> "ANNULLATA" [style="solid",labeltooltip="No properties",label="ANNULLAMENTO\nadmin\nat\nBD"]

	"ANNULLATA" [shape="ellipse",label="ANNULLATA",fillcolor="grey50"]
	"APERTA" [label="APERTA"]
	"ATTESA_PRESA_IN_CARICO_CLIENTE" [label="ATTESA PRESA\nIN CARICO\nCLIENTE"]
	"IN_LAVORAZIONE_CLIENTE" [label="IN LAVORAZIONE\nCLIENTE"]
	"NON_RISOLTA" [fillcolor="grey50",label="NON RISOLTA",shape="ellipse"]
	"RISOLTA" [shape="ellipse",label="RISOLTA",fillcolor="grey50"]
	"RISOLTA_CLIENTE" [label="RISOLTA\nCLIENTE",shape="ellipse",fillcolor="grey50"]
	"RISOLUZIONE_LAVORAZIONE_CLIENTE" [label="RISOLUZIONE\nLAVORAZIONE\nCLIENTE"]
	"START" [fillcolor="grey25",label="START",fontcolor="white",shape="diamond"]
	"ANALISI" [label="ANALISI"]
	"___ANY_STATUS___" [label="   ANY STATUS  "]
}
