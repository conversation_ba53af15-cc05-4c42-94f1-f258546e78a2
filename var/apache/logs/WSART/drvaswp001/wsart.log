[API::ART::REST:1912869] info @2025-07-28 15:31:34> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1912869] info @2025-07-28 15:31:36> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:1912869] warning @2025-07-28 15:31:36> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkap002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:1912870] info @2025-07-28 15:31:38> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1912870] warning @2025-07-28 15:31:40> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkap002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:1912871] info @2025-07-28 15:31:54> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1912871] info @2025-07-28 15:31:55> Error:
$VAR1 = {
          'UUID' => 'd54552cea7355c005ca368d9e5ff0a82',
          'message' => 'Unauthorized',
          'internalMessage' => 'No valid session in cache for sid aId7y79HIn7pYxlScLArkCRX3R3rh-co'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1912873] info @2025-07-28 15:31:56> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1912872] info @2025-07-28 15:31:56> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1912995] info @2025-07-28 15:31:56> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1912873] info @2025-07-28 15:31:57> Error:
$VAR1 = {
          'internalMessage' => 'No valid session in cache for sid aId7zaFcFklEIOWQ4awTC42E2LEG46PU',
          'message' => 'Unauthorized',
          'UUID' => '0057cc9f18ebac71387b1b9b9c55af72'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1912872] info @2025-07-28 15:31:57> Error:
$VAR1 = {
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'UUID' => 'f98379a5360c7c86d52428d11f2ecb65',
          'message' => 'Forbidden'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1912995] info @2025-07-28 15:31:57> Error:
$VAR1 = {
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
',
          'UUID' => 'eae36ade55a864d575de405069200efd',
          'message' => 'Forbidden'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1913002] info @2025-07-28 15:32:17> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1913002] warning @2025-07-28 15:32:18> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkap002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:1913002] warning @2025-07-28 15:32:19> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkap002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:1912870] info @2025-07-28 15:33:01> Error:
$VAR1 = {
          'UUID' => '75654c65ce979ee69f2d1c008147383e',
          'message' => 'Unauthorized',
          'internalMessage' => 'No valid session in cache for sid aId8DVMLfFqzLmoumwpa7NMAmT_d8SDe'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1913004] info @2025-07-28 15:33:02> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1913003] info @2025-07-28 15:33:02> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1913004] info @2025-07-28 15:33:03> Error:
$VAR1 = {
          'message' => 'Forbidden',
          'UUID' => '512fd65e21326ed64ee6f203fcf9737a',
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1913003] info @2025-07-28 15:33:03> Error:
$VAR1 = {
          'UUID' => 'ae7bfb7dc382d73a24f45ad31442180b',
          'message' => 'Forbidden',
          'internalMessage' => 'Unable to create API::ART despite a valid JWT and a valid cache for user {username}: API::ART : Missing USER!

Usage:
	API::ART->new(
		ARTID => $artid,
		[ USER => $artuser, PASSWORD => $password ] | [ USER_REFERENCE => $user_reference ] | [ API_KEY => $api_key ],
		[ DEBUG => $debug ]
	)

		 at /home/<USER>/COM/share/perl5/API/ART.pm line 1321.
'
        }; in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1835
[API::ART::REST:1913005] info @2025-07-28 15:33:51> Instance ELK configuration loaded from /home/<USER>/WPSOAP/etc/restART-config.ELK.yml file in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 66
[API::ART::REST:1913005] info @2025-07-28 15:33:52> User ROOT auth by LOCAL in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 1494
[API::ART::REST:1913005] warning @2025-07-28 15:33:52> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkap002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
[API::ART::REST:1912871] warning @2025-07-28 15:33:54> Elasticsearch error in ping [NoNodes] ** No nodes are available: [https://drvkkap002:9200], called from sub Search::Elasticsearch::Role::Client::Direct::__ANON__ at /home/<USER>/COM/share/perl5/API/ART/REST.pm line 7469. in /home/<USER>/COM/share/perl5/API/ART/REST.pm l. 7472
